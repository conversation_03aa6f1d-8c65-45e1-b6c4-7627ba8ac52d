import unittest
from OptionStrategy5 import DynamicParamsManager

class OptionStrategy5Test(unittest.TestCase):
    def test_initialization_of_DynamicParamsManager(self):
        """Verify that DynamicParamsManager initializes with correct default parameters and constraints."""
        # Initialize the manager
        manager = DynamicParamsManager()
        
        # Check default parameters
        self.assertEqual(manager.params["ema"], [5, 15, 30])
        self.assertEqual(manager.params["atr_period"], 14)
        self.assertEqual(manager.params["stop_mult"], 2.2)
        self.assertEqual(manager.params["profit_mult"], 3.0)
        self.assertEqual(manager.params["trail_step"], 1.0)
        
        # Check that parameters are within constraints
        # EMA constraints
        for i, ema in enumerate(manager.params["ema"]):
            self.assertGreaterEqual(ema, manager.constraints["ema"]["min"][i])
            self.assertLessEqual(ema, manager.constraints["ema"]["max"][i])
        
        # ATR period constraints
        self.assertGreaterEqual(manager.params["atr_period"], manager.constraints["atr_period"]["min"])
        self.assertLessEqual(manager.params["atr_period"], manager.constraints["atr_period"]["max"])
        
        # Stop multiplier constraints
        self.assertGreaterEqual(manager.params["stop_mult"], manager.constraints["stop_mult"]["min"])
        self.assertLessEqual(manager.params["stop_mult"], manager.constraints["stop_mult"]["max"])
        
        # Profit multiplier constraints
        self.assertGreaterEqual(manager.params["profit_mult"], manager.constraints["profit_mult"]["min"])
        self.assertLessEqual(manager.params["profit_mult"], manager.constraints["profit_mult"]["max"])
        
        # Trail step constraints
        self.assertGreaterEqual(manager.params["trail_step"], manager.constraints["trail_step"]["min"])
        self.assertLessEqual(manager.params["trail_step"], manager.constraints["trail_step"]["max"])

if __name__ == '__main__':
    unittest.main()