"""
模糊系统可视化工具
提供隶属函数、规则库、决策过程的可视化
"""

import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
from matplotlib.patches import Polygon
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import pandas as pd
from datetime import datetime, timedelta
import io
import base64

class FuzzyVisualization:
    """模糊系统可视化类"""
    
    def __init__(self):
        self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD']
        plt.style.use('seaborn-v0_8')
        
    def plot_membership_functions(self, fuzzy_sets, title="隶属函数", save_path=None):
        """绘制隶属函数"""
        fig, ax = plt.subplots(figsize=(12, 8))
        
        # 确定论域范围
        all_values = []
        for set_name, fuzzy_set in fuzzy_sets.items():
            if hasattr(fuzzy_set, 'a'):  # 梯形模糊数
                all_values.extend([fuzzy_set.a, fuzzy_set.b, fuzzy_set.c, fuzzy_set.d])
        
        if not all_values:
            return None
        
        x_min, x_max = min(all_values), max(all_values)
        x_range = np.linspace(x_min - 0.1 * (x_max - x_min), 
                             x_max + 0.1 * (x_max - x_min), 1000)
        
        # 绘制每个模糊集
        for i, (set_name, fuzzy_set) in enumerate(fuzzy_sets.items()):
            if hasattr(fuzzy_set, 'membership'):
                y_values = [fuzzy_set.membership(x) for x in x_range]
            elif hasattr(fuzzy_set, 'a'):  # 梯形模糊数
                y_values = []
                for x in x_range:
                    if x <= fuzzy_set.a or x >= fuzzy_set.d:
                        y_values.append(0)
                    elif fuzzy_set.a < x <= fuzzy_set.b:
                        y_values.append((x - fuzzy_set.a) / (fuzzy_set.b - fuzzy_set.a))
                    elif fuzzy_set.b < x <= fuzzy_set.c:
                        y_values.append(1)
                    else:  # fuzzy_set.c < x < fuzzy_set.d
                        y_values.append((fuzzy_set.d - x) / (fuzzy_set.d - fuzzy_set.c))
            else:
                continue
            
            color = self.colors[i % len(self.colors)]
            ax.plot(x_range, y_values, label=set_name, color=color, linewidth=2)
            ax.fill_between(x_range, y_values, alpha=0.3, color=color)
        
        ax.set_xlabel('输入值')
        ax.set_ylabel('隶属度')
        ax.set_title(title)
        ax.legend()
        ax.grid(True, alpha=0.3)
        ax.set_ylim(0, 1.1)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
        
        return fig
    
    def plot_rule_performance(self, rule_performance_data, title="规则性能分析"):
        """绘制规则性能分析图"""
        if not rule_performance_data:
            return None
        
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('规则使用频率', '规则性能分布', '规则权重变化', '规则成功率'),
            specs=[[{"type": "bar"}, {"type": "histogram"}],
                   [{"type": "scatter"}, {"type": "bar"}]]
        )
        
        # 准备数据
        rule_ids = list(rule_performance_data.keys())
        usage_counts = [data.get('usage_count', 0) for data in rule_performance_data.values()]
        performances = []
        weights = []
        success_rates = []
        
        for data in rule_performance_data.values():
            perf_history = data.get('performance_history', [])
            performances.extend(perf_history)
            weights.append(data.get('current_weight', 1.0))
            
            if perf_history:
                success_rate = sum(1 for p in perf_history if p > 0.5) / len(perf_history)
                success_rates.append(success_rate)
            else:
                success_rates.append(0)
        
        # 规则使用频率
        fig.add_trace(
            go.Bar(x=rule_ids, y=usage_counts, name="使用次数"),
            row=1, col=1
        )
        
        # 规则性能分布
        fig.add_trace(
            go.Histogram(x=performances, nbinsx=20, name="性能分布"),
            row=1, col=2
        )
        
        # 规则权重变化
        fig.add_trace(
            go.Scatter(x=rule_ids, y=weights, mode='markers+lines', name="当前权重"),
            row=2, col=1
        )
        
        # 规则成功率
        fig.add_trace(
            go.Bar(x=rule_ids, y=success_rates, name="成功率"),
            row=2, col=2
        )
        
        fig.update_layout(height=800, title_text=title, showlegend=False)
        return fig
    
    def plot_decision_process(self, input_values, membership_values, rule_activations, final_decision):
        """可视化决策过程"""
        fig = make_subplots(
            rows=3, cols=1,
            subplot_titles=('输入模糊化', '规则激活强度', '最终决策'),
            row_heights=[0.3, 0.4, 0.3]
        )
        
        # 输入模糊化可视化
        input_names = list(input_values.keys())
        input_vals = list(input_values.values())
        
        fig.add_trace(
            go.Bar(x=input_names, y=input_vals, name="输入值"),
            row=1, col=1
        )
        
        # 规则激活强度
        if rule_activations:
            rule_names = [f"规则{i+1}" for i in range(len(rule_activations))]
            activations = list(rule_activations)
            
            fig.add_trace(
                go.Bar(x=rule_names, y=activations, name="激活强度"),
                row=2, col=1
            )
        
        # 最终决策
        if isinstance(final_decision, tuple) and len(final_decision) >= 3:
            decision_labels = ['风险等级', '行动等级', '置信度']
            decision_values = [
                self._risk_to_numeric(final_decision[0]),
                self._action_to_numeric(final_decision[1]),
                final_decision[2]
            ]
            
            fig.add_trace(
                go.Bar(x=decision_labels, y=decision_values, name="决策结果"),
                row=3, col=1
            )
        
        fig.update_layout(height=900, title_text="模糊决策过程可视化")
        return fig
    
    def plot_fuzzy_clustering_results(self, data, cluster_centers, membership_matrix, title="模糊聚类结果"):
        """可视化模糊聚类结果"""
        if data.shape[1] < 2:
            return None
        
        fig, axes = plt.subplots(1, 2, figsize=(15, 6))
        
        # 聚类结果散点图
        n_clusters = len(cluster_centers)
        colors = plt.cm.Set3(np.linspace(0, 1, n_clusters))
        
        for i in range(n_clusters):
            # 根据隶属度着色
            cluster_membership = membership_matrix[i, :]
            scatter = axes[0].scatter(
                data[:, 0], data[:, 1], 
                c=cluster_membership, 
                cmap=plt.cm.viridis,
                alpha=0.6,
                s=50
            )
            
            # 绘制聚类中心
            axes[0].scatter(
                cluster_centers[i, 0], cluster_centers[i, 1],
                c='red', marker='x', s=200, linewidths=3,
                label=f'中心{i+1}'
            )
        
        axes[0].set_xlabel('特征1')
        axes[0].set_ylabel('特征2')
        axes[0].set_title('聚类结果')
        axes[0].legend()
        
        # 隶属度矩阵热图
        im = axes[1].imshow(membership_matrix, cmap='viridis', aspect='auto')
        axes[1].set_xlabel('数据点')
        axes[1].set_ylabel('聚类')
        axes[1].set_title('隶属度矩阵')
        plt.colorbar(im, ax=axes[1])
        
        plt.tight_layout()
        return fig
    
    def plot_time_series_prediction(self, historical_data, predictions, title="模糊时间序列预测"):
        """可视化时间序列预测结果"""
        fig = go.Figure()
        
        # 历史数据
        historical_x = list(range(len(historical_data)))
        fig.add_trace(go.Scatter(
            x=historical_x,
            y=historical_data,
            mode='lines+markers',
            name='历史数据',
            line=dict(color='blue')
        ))
        
        # 预测数据
        if predictions:
            pred_x = list(range(len(historical_data), len(historical_data) + len(predictions)))
            fig.add_trace(go.Scatter(
                x=pred_x,
                y=predictions,
                mode='lines+markers',
                name='预测数据',
                line=dict(color='red', dash='dash')
            ))
        
        fig.update_layout(
            title=title,
            xaxis_title='时间步',
            yaxis_title='值',
            hovermode='x unified'
        )
        
        return fig
    
    def plot_performance_dashboard(self, performance_data):
        """绘制性能仪表板"""
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=(
                '预测准确率趋势', '收益率分布', '风险调整收益',
                '决策置信度', '规则使用统计', '系统性能指标'
            ),
            specs=[[{"type": "scatter"}, {"type": "histogram"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "pie"}, {"type": "indicator"}]]
        )
        
        # 这里添加具体的性能数据可视化逻辑
        # 由于篇幅限制，这里只提供框架
        
        return fig
    
    def _risk_to_numeric(self, risk_level):
        """将风险等级转换为数值"""
        risk_map = {
            "RiskNone": 0, "RiskLow": 1, 
            "RiskMedium": 2, "RiskHigh": 3
        }
        return risk_map.get(risk_level, 2)
    
    def _action_to_numeric(self, action_level):
        """将行动等级转换为数值"""
        action_map = {
            "Stop": 0, "Conservative": 1, 
            "Normal": 2, "Aggressive": 3
        }
        return action_map.get(action_level, 2)
    
    def save_plot_as_base64(self, fig):
        """将图表保存为base64字符串"""
        if hasattr(fig, 'to_html'):  # Plotly图表
            return fig.to_html(include_plotlyjs='cdn')
        else:  # Matplotlib图表
            buffer = io.BytesIO()
            fig.savefig(buffer, format='png', dpi=300, bbox_inches='tight')
            buffer.seek(0)
            image_base64 = base64.b64encode(buffer.getvalue()).decode()
            buffer.close()
            return f"data:image/png;base64,{image_base64}"

# 全局可视化实例
fuzzy_viz = FuzzyVisualization()
