#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模拟交易测试脚本
用于对Strategy3.py进行实战化演练和性能评估
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import json
import warnings
warnings.filterwarnings('ignore')

# 模拟市场数据生成器
class MarketDataSimulator:
    """市场数据模拟器"""
    
    def __init__(self, initial_price=100.0, volatility=0.02, trend=0.0001):
        self.initial_price = initial_price
        self.volatility = volatility
        self.trend = trend
        self.current_price = initial_price
        self.time_step = 0
        
    def generate_tick_data(self, num_ticks=1000):
        """生成模拟tick数据"""
        prices = []
        volumes = []
        timestamps = []
        
        base_time = datetime.now()
        
        for i in range(num_ticks):
            # 价格随机游走 + 趋势 + 周期性
            random_change = np.random.normal(0, self.volatility)
            trend_change = self.trend
            
            # 添加周期性波动（模拟日内模式）
            cycle_change = 0.001 * np.sin(2 * np.pi * i / 100)
            
            # 添加突发事件（低概率大幅波动）
            if np.random.random() < 0.01:  # 1%概率
                shock = np.random.normal(0, self.volatility * 5)
                random_change += shock
            
            price_change = random_change + trend_change + cycle_change
            self.current_price *= (1 + price_change)
            
            # 确保价格不会变成负数
            self.current_price = max(self.current_price, 1.0)
            
            prices.append(self.current_price)
            
            # 生成成交量（与价格波动相关）
            volume = max(100, int(1000 + 500 * abs(price_change) * 10000))
            volumes.append(volume)
            
            # 时间戳
            timestamp = base_time + timedelta(seconds=i)
            timestamps.append(timestamp)
            
            self.time_step += 1
        
        return pd.DataFrame({
            'timestamp': timestamps,
            'price': prices,
            'volume': volumes,
            'high': [p * (1 + abs(np.random.normal(0, 0.001))) for p in prices],
            'low': [p * (1 - abs(np.random.normal(0, 0.001))) for p in prices],
            'open': prices,  # 简化处理
            'close': prices
        })

# 模拟交易环境
class TradingEnvironment:
    """模拟交易环境"""
    
    def __init__(self, initial_capital=100000, commission_rate=0.0003):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.commission_rate = commission_rate
        self.positions = {}  # {instrument_id: {'volume': int, 'avg_price': float}}
        self.trade_history = []
        self.pnl_history = []
        self.equity_curve = []
        
    def execute_order(self, instrument_id, direction, volume, price):
        """执行订单"""
        try:
            trade_value = volume * price
            commission = trade_value * self.commission_rate
            
            if direction == 'buy':
                # 检查资金是否充足
                required_capital = trade_value + commission
                if self.current_capital < required_capital:
                    return False, "资金不足"
                
                # 更新持仓
                if instrument_id not in self.positions:
                    self.positions[instrument_id] = {'volume': 0, 'avg_price': 0}
                
                current_pos = self.positions[instrument_id]
                total_volume = current_pos['volume'] + volume
                total_value = current_pos['volume'] * current_pos['avg_price'] + trade_value
                
                self.positions[instrument_id] = {
                    'volume': total_volume,
                    'avg_price': total_value / total_volume if total_volume > 0 else price
                }
                
                self.current_capital -= (trade_value + commission)
                
            elif direction == 'sell':
                # 检查持仓是否充足，如果没有持仓则进行空头开仓
                if instrument_id not in self.positions or self.positions[instrument_id]['volume'] < volume:
                    # 空头开仓
                    if instrument_id not in self.positions:
                        self.positions[instrument_id] = {'volume': 0, 'avg_price': 0}

                    # 检查资金是否充足（保证金要求）
                    required_margin = trade_value * 0.1  # 假设10%保证金
                    if self.current_capital < required_margin + commission:
                        return False, "保证金不足"

                    # 空头开仓
                    current_pos = self.positions[instrument_id]
                    new_volume = current_pos['volume'] - volume  # 负数表示空头
                    total_value = current_pos['volume'] * current_pos['avg_price'] - trade_value

                    self.positions[instrument_id] = {
                        'volume': new_volume,
                        'avg_price': price if new_volume != 0 else 0
                    }

                    self.current_capital -= (required_margin + commission)

                else:
                    # 平仓操作
                    avg_price = self.positions[instrument_id]['avg_price']
                    pnl = (price - avg_price) * volume - commission

                    # 更新持仓
                    self.positions[instrument_id]['volume'] -= volume
                    if self.positions[instrument_id]['volume'] == 0:
                        del self.positions[instrument_id]

                    self.current_capital += (trade_value - commission)
                    self.pnl_history.append(pnl)
            
            # 记录交易
            trade_record = {
                'timestamp': datetime.now(),
                'instrument_id': instrument_id,
                'direction': direction,
                'volume': volume,
                'price': price,
                'commission': commission,
                'capital_after': self.current_capital
            }
            self.trade_history.append(trade_record)
            
            return True, "交易成功"
            
        except Exception as e:
            return False, f"交易失败: {str(e)}"
    
    def get_portfolio_value(self, current_prices):
        """计算投资组合总价值"""
        portfolio_value = self.current_capital
        
        for instrument_id, position in self.positions.items():
            if instrument_id in current_prices:
                market_value = position['volume'] * current_prices[instrument_id]
                portfolio_value += market_value
        
        return portfolio_value
    
    def get_performance_metrics(self):
        """计算性能指标"""
        if not self.equity_curve:
            return {}
        
        equity_series = pd.Series(self.equity_curve)
        returns = equity_series.pct_change().dropna()
        
        total_return = (equity_series.iloc[-1] - equity_series.iloc[0]) / equity_series.iloc[0]
        annual_return = total_return * (252 * 24 * 60)  # 假设分钟级数据
        
        volatility = returns.std() * np.sqrt(252 * 24 * 60)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        max_drawdown = drawdown.min()
        
        # 胜率
        if self.pnl_history:
            win_rate = len([pnl for pnl in self.pnl_history if pnl > 0]) / len(self.pnl_history)
        else:
            win_rate = 0
        
        return {
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'total_trades': len(self.trade_history),
            'total_pnl': sum(self.pnl_history)
        }

# 策略适配器
class StrategyAdapter:
    """策略适配器 - 将Strategy3适配到模拟环境"""
    
    def __init__(self, strategy_class):
        self.strategy_class = strategy_class
        self.strategy = None
        self.last_decision = None
        
    def initialize_strategy(self):
        """初始化策略"""
        try:
            # 创建模拟的参数和状态对象
            class MockParams:
                def __init__(self):
                    self.exchange = "MOCK"
                    self.instrument_id = "TEST001"
                    self.kline_style = "M1"
                    self.tech_type = "STC_HULL"
                    self.trade_direction = "auto"
                    self.price_type = "D1"
                    self.order_volume = 1
                    self.trail_profit_start = 0.03
                    self.trail_profit_stop = 0.015
                    self.quick_stop_loss = 0.02
                    self.stability_margin = 0.7
                    self.N1 = 20
                    self.P1 = 10
            
            class MockState:
                def __init__(self):
                    self.fuzzy_risk = "RiskMedium"
                    self.fuzzy_action = "Normal"
                    self.fuzzy_confidence = 0.5
                    self.ml_prediction = "hold"
                    self.ml_confidence = 0.5
                    self.system_stability = 0.7
                    self.volatility_index = 0.5
                    self.last_action = "hold"
                    self.position_cost = 0.0
                    self.filtered_price = 100.0
            
            # 模拟策略初始化
            self.strategy = type('MockStrategy', (), {
                'params_map': MockParams(),
                'state_map': MockState(),
                'output': lambda msg: print(f"[策略] {msg}"),
                'trading': True,
                'long_price': 100.0,
                'short_price': 100.0,
                'signal_price': 100.0,
                'order_id': None
            })()
            
            return True
        except Exception as e:
            print(f"策略初始化失败: {e}")
            return False
    
    def process_market_data(self, market_data):
        """处理市场数据并生成交易信号"""
        try:
            if not self.strategy:
                return None
            
            # 更新价格信息
            current_price = market_data['price'].iloc[-1]
            self.strategy.long_price = current_price
            self.strategy.short_price = current_price
            
            # 构造市场数据
            processed_data = {
                'price_history': market_data['price'].tolist(),
                'volume_history': market_data['volume'].tolist(),
                'current_price': current_price,
                'volatility': market_data['price'].pct_change().std(),
                'returns': market_data['price'].pct_change().iloc[-1]
            }
            
            # 模拟策略决策过程
            decision = self._simulate_strategy_decision(processed_data)
            self.last_decision = decision
            
            return decision
            
        except Exception as e:
            print(f"市场数据处理失败: {e}")
            return None
    
    def _simulate_strategy_decision(self, market_data):
        """模拟策略决策"""
        try:
            # 简化的决策逻辑（基于价格动量和波动率）
            price_history = market_data['price_history']
            if len(price_history) < 20:
                return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
            
            # 计算技术指标
            prices = np.array(price_history[-20:])
            sma_short = np.mean(prices[-5:])
            sma_long = np.mean(prices[-20:])
            volatility = market_data['volatility']
            
            # 决策逻辑
            if sma_short > sma_long * 1.002 and volatility < 0.03:
                action = 'buy'
                confidence = 0.7
                volume = 1
            elif sma_short < sma_long * 0.998 and volatility < 0.03:
                action = 'sell'
                confidence = 0.7
                volume = 1
            else:
                action = 'hold'
                confidence = 0.5
                volume = 0
            
            return {
                'action': action,
                'confidence': confidence,
                'volume': volume,
                'price': market_data['current_price']
            }
            
        except Exception as e:
            print(f"策略决策失败: {e}")
            return {'action': 'hold', 'confidence': 0.5, 'volume': 0}

# 主测试类
class TradingSimulationTest:
    """交易模拟测试主类"""

    def __init__(self, initial_capital=100000):
        self.market_simulator = MarketDataSimulator()
        self.trading_env = TradingEnvironment(initial_capital)
        self.strategy_adapter = StrategyAdapter(None)  # 将在后续导入Strategy3
        self.test_results = {}

    def run_simulation(self, num_ticks=5000, save_results=True):
        """运行完整的模拟测试"""
        print("=" * 60)
        print("开始模拟交易测试")
        print("=" * 60)

        # 初始化策略
        if not self.strategy_adapter.initialize_strategy():
            print("策略初始化失败，测试终止")
            return None

        # 生成市场数据
        print(f"生成 {num_ticks} 个tick的市场数据...")
        market_data = self.market_simulator.generate_tick_data(num_ticks)

        # 执行模拟交易
        print("开始模拟交易...")
        self._execute_simulation(market_data)

        # 分析结果
        print("分析测试结果...")
        results = self._analyze_results(market_data)

        # 保存结果
        if save_results:
            self._save_results(results, market_data)

        # 显示结果
        self._display_results(results)

        return results

    def _execute_simulation(self, market_data):
        """执行模拟交易"""
        instrument_id = "TEST001"

        for i in range(20, len(market_data)):  # 从第20个tick开始，确保有足够历史数据
            # 获取当前市场数据窗口
            current_window = market_data.iloc[:i+1]

            # 策略决策
            decision = self.strategy_adapter.process_market_data(current_window)

            if decision and decision['action'] != 'hold' and decision['volume'] > 0:
                current_price = decision['price']

                # 执行交易
                success, message = self.trading_env.execute_order(
                    instrument_id=instrument_id,
                    direction=decision['action'],
                    volume=decision['volume'],
                    price=current_price
                )

                if not success:
                    print(f"Tick {i}: 交易失败 - {message}")

            # 更新权益曲线
            current_prices = {instrument_id: market_data.iloc[i]['price']}
            portfolio_value = self.trading_env.get_portfolio_value(current_prices)
            self.trading_env.equity_curve.append(portfolio_value)

            # 进度显示
            if i % 1000 == 0:
                print(f"处理进度: {i}/{len(market_data)} ({i/len(market_data)*100:.1f}%)")

    def _analyze_results(self, market_data):
        """分析测试结果"""
        performance_metrics = self.trading_env.get_performance_metrics()

        # 基准比较（买入持有策略）
        initial_price = market_data['price'].iloc[0]
        final_price = market_data['price'].iloc[-1]
        benchmark_return = (final_price - initial_price) / initial_price

        # 计算超额收益
        excess_return = performance_metrics.get('total_return', 0) - benchmark_return

        # 风险调整指标
        information_ratio = excess_return / performance_metrics.get('volatility', 1) if performance_metrics.get('volatility', 0) > 0 else 0

        results = {
            'performance_metrics': performance_metrics,
            'benchmark_return': benchmark_return,
            'excess_return': excess_return,
            'information_ratio': information_ratio,
            'market_data_summary': {
                'total_ticks': len(market_data),
                'price_range': (market_data['price'].min(), market_data['price'].max()),
                'avg_volatility': market_data['price'].pct_change().std(),
                'total_volume': market_data['volume'].sum()
            }
        }

        return results

    def _save_results(self, results, market_data):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存性能指标
        with open(f'simulation_results_{timestamp}.json', 'w', encoding='utf-8') as f:
            # 转换numpy类型为Python原生类型
            serializable_results = self._make_serializable(results)
            json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        # 保存交易记录
        if self.trading_env.trade_history:
            trade_df = pd.DataFrame(self.trading_env.trade_history)
            trade_df.to_csv(f'trade_history_{timestamp}.csv', index=False)

        # 保存权益曲线
        if self.trading_env.equity_curve:
            equity_df = pd.DataFrame({
                'timestamp': market_data['timestamp'].iloc[:len(self.trading_env.equity_curve)],
                'equity': self.trading_env.equity_curve
            })
            equity_df.to_csv(f'equity_curve_{timestamp}.csv', index=False)

        print(f"结果已保存到文件 (时间戳: {timestamp})")

    def _make_serializable(self, obj):
        """将对象转换为JSON可序列化格式"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(v) for v in obj]
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        else:
            return obj

    def _display_results(self, results):
        """显示测试结果"""
        print("\n" + "=" * 60)
        print("模拟交易测试结果")
        print("=" * 60)

        metrics = results['performance_metrics']

        print(f"总收益率: {metrics.get('total_return', 0):.2%}")
        print(f"年化收益率: {metrics.get('annual_return', 0):.2%}")
        print(f"基准收益率: {results['benchmark_return']:.2%}")
        print(f"超额收益: {results['excess_return']:.2%}")
        print(f"波动率: {metrics.get('volatility', 0):.2%}")
        print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
        print(f"信息比率: {results['information_ratio']:.2f}")
        print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
        print(f"胜率: {metrics.get('win_rate', 0):.2%}")
        print(f"总交易次数: {metrics.get('total_trades', 0)}")
        print(f"总盈亏: {metrics.get('total_pnl', 0):.2f}")

        # 风险评估
        print("\n风险评估:")
        if metrics.get('sharpe_ratio', 0) > 1.0:
            print("✓ 夏普比率良好 (>1.0)")
        else:
            print("⚠ 夏普比率偏低 (<1.0)")

        if abs(metrics.get('max_drawdown', 0)) < 0.1:
            print("✓ 最大回撤可控 (<10%)")
        else:
            print("⚠ 最大回撤较大 (>10%)")

        if metrics.get('win_rate', 0) > 0.5:
            print("✓ 胜率较高 (>50%)")
        else:
            print("⚠ 胜率偏低 (<50%)")

    def plot_results(self):
        """绘制测试结果图表"""
        if not self.trading_env.equity_curve:
            print("没有权益曲线数据可绘制")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('模拟交易测试结果', fontsize=16)

        # 权益曲线
        axes[0, 0].plot(self.trading_env.equity_curve)
        axes[0, 0].set_title('权益曲线')
        axes[0, 0].set_xlabel('时间')
        axes[0, 0].set_ylabel('权益价值')
        axes[0, 0].grid(True)

        # 收益分布
        if self.trading_env.pnl_history:
            axes[0, 1].hist(self.trading_env.pnl_history, bins=30, alpha=0.7)
            axes[0, 1].set_title('收益分布')
            axes[0, 1].set_xlabel('单笔盈亏')
            axes[0, 1].set_ylabel('频次')
            axes[0, 1].grid(True)

        # 回撤分析
        equity_series = pd.Series(self.trading_env.equity_curve)
        peak = equity_series.expanding().max()
        drawdown = (equity_series - peak) / peak
        axes[1, 0].fill_between(range(len(drawdown)), drawdown, 0, alpha=0.3, color='red')
        axes[1, 0].set_title('回撤分析')
        axes[1, 0].set_xlabel('时间')
        axes[1, 0].set_ylabel('回撤比例')
        axes[1, 0].grid(True)

        # 交易频率
        if self.trading_env.trade_history:
            trade_df = pd.DataFrame(self.trading_env.trade_history)
            trade_counts = trade_df.groupby('direction').size()
            axes[1, 1].pie(trade_counts.values, labels=trade_counts.index, autopct='%1.1f%%')
            axes[1, 1].set_title('交易方向分布')

        plt.tight_layout()
        plt.savefig(f'simulation_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png', dpi=300, bbox_inches='tight')
        plt.show()

# 多场景测试
class MultiScenarioTest:
    """多场景测试类"""

    def __init__(self):
        self.scenarios = [
            {'name': '牛市场景', 'trend': 0.0005, 'volatility': 0.015},
            {'name': '熊市场景', 'trend': -0.0005, 'volatility': 0.015},
            {'name': '震荡市场景', 'trend': 0.0, 'volatility': 0.02},
            {'name': '高波动场景', 'trend': 0.0001, 'volatility': 0.04},
            {'name': '低波动场景', 'trend': 0.0001, 'volatility': 0.008}
        ]
        self.results = {}

    def run_all_scenarios(self, num_ticks=3000):
        """运行所有场景测试"""
        print("开始多场景测试...")

        for scenario in self.scenarios:
            print(f"\n测试场景: {scenario['name']}")
            print("-" * 40)

            # 创建特定场景的测试实例
            test = TradingSimulationTest()
            test.market_simulator.trend = scenario['trend']
            test.market_simulator.volatility = scenario['volatility']

            # 运行测试
            result = test.run_simulation(num_ticks, save_results=False)
            self.results[scenario['name']] = result

        # 汇总分析
        self._analyze_scenario_results()

    def _analyze_scenario_results(self):
        """分析多场景结果"""
        print("\n" + "=" * 60)
        print("多场景测试汇总")
        print("=" * 60)

        summary_data = []
        for scenario_name, result in self.results.items():
            if result:
                metrics = result['performance_metrics']
                summary_data.append({
                    '场景': scenario_name,
                    '总收益率': f"{metrics.get('total_return', 0):.2%}",
                    '夏普比率': f"{metrics.get('sharpe_ratio', 0):.2f}",
                    '最大回撤': f"{metrics.get('max_drawdown', 0):.2%}",
                    '胜率': f"{metrics.get('win_rate', 0):.2%}",
                    '交易次数': metrics.get('total_trades', 0)
                })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))

            # 保存汇总结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_df.to_csv(f'multi_scenario_summary_{timestamp}.csv', index=False)
            print(f"\n汇总结果已保存到 multi_scenario_summary_{timestamp}.csv")

# 策略优化建议生成器
class StrategyOptimizer:
    """策略优化建议生成器"""

    def __init__(self, test_results):
        self.test_results = test_results

    def generate_optimization_suggestions(self):
        """生成优化建议"""
        suggestions = []

        if not self.test_results:
            return ["无测试结果，无法生成优化建议"]

        metrics = self.test_results.get('performance_metrics', {})

        # 收益率分析
        total_return = metrics.get('total_return', 0)
        if total_return < 0:
            suggestions.append("🔴 策略整体亏损，建议：")
            suggestions.append("   - 检查入场和出场逻辑")
            suggestions.append("   - 优化止损机制")
            suggestions.append("   - 考虑降低交易频率")
        elif total_return < 0.05:
            suggestions.append("🟡 收益率偏低，建议：")
            suggestions.append("   - 优化信号质量")
            suggestions.append("   - 调整仓位管理")

        # 夏普比率分析
        sharpe_ratio = metrics.get('sharpe_ratio', 0)
        if sharpe_ratio < 0.5:
            suggestions.append("🔴 夏普比率过低，建议：")
            suggestions.append("   - 降低交易频率，提高信号质量")
            suggestions.append("   - 优化风险管理机制")
            suggestions.append("   - 考虑增加市场过滤条件")
        elif sharpe_ratio < 1.0:
            suggestions.append("🟡 夏普比率有待提升，建议：")
            suggestions.append("   - 优化入场时机选择")
            suggestions.append("   - 改进止盈止损策略")

        # 最大回撤分析
        max_drawdown = abs(metrics.get('max_drawdown', 0))
        if max_drawdown > 0.2:
            suggestions.append("🔴 最大回撤过大，建议：")
            suggestions.append("   - 加强风险控制")
            suggestions.append("   - 降低单笔交易仓位")
            suggestions.append("   - 增加止损保护")
        elif max_drawdown > 0.1:
            suggestions.append("🟡 回撤控制需要改进，建议：")
            suggestions.append("   - 优化仓位管理")
            suggestions.append("   - 改进风险评估模型")

        # 胜率分析
        win_rate = metrics.get('win_rate', 0)
        if win_rate < 0.4:
            suggestions.append("🔴 胜率过低，建议：")
            suggestions.append("   - 重新评估信号生成逻辑")
            suggestions.append("   - 优化模糊规则参数")
            suggestions.append("   - 增加市场趋势过滤")
        elif win_rate < 0.5:
            suggestions.append("🟡 胜率有待提升，建议：")
            suggestions.append("   - 优化入场条件")
            suggestions.append("   - 改进信号确认机制")

        # 交易频率分析
        total_trades = metrics.get('total_trades', 0)
        if total_trades < 10:
            suggestions.append("🟡 交易频率过低，建议：")
            suggestions.append("   - 降低信号阈值")
            suggestions.append("   - 增加交易机会识别")
        elif total_trades > 1000:
            suggestions.append("🟡 交易频率过高，建议：")
            suggestions.append("   - 提高信号质量要求")
            suggestions.append("   - 增加信号过滤条件")

        # 模糊系统特定建议
        suggestions.append("\n📊 模糊系统优化建议：")
        suggestions.append("   - 调整隶属函数参数以适应当前市场特征")
        suggestions.append("   - 优化模糊规则权重分配")
        suggestions.append("   - 增强时空维度动态调整机制")
        suggestions.append("   - 改进神经模糊混合架构的融合权重")

        return suggestions

# 主执行函数
def main():
    """主执行函数"""
    print("模拟交易测试系统启动")
    print("=" * 60)

    try:
        # 单场景测试
        print("1. 执行单场景测试...")
        test = TradingSimulationTest(initial_capital=100000)
        results = test.run_simulation(num_ticks=3000, save_results=True)

        if results:
            # 绘制结果图表
            test.plot_results()

            # 生成优化建议
            optimizer = StrategyOptimizer(results)
            suggestions = optimizer.generate_optimization_suggestions()

            print("\n" + "=" * 60)
            print("策略优化建议")
            print("=" * 60)
            for suggestion in suggestions:
                print(suggestion)

        # 多场景测试
        print("\n2. 执行多场景测试...")
        multi_test = MultiScenarioTest()
        multi_test.run_all_scenarios(num_ticks=2000)

        print("\n测试完成！")

    except Exception as e:
        print(f"测试执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
