#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易策略测试启动脚本
提供简单的命令行界面来运行不同类型的测试
"""

import sys
import os
import argparse
from datetime import datetime

def print_banner():
    """打印启动横幅"""
    print("=" * 70)
    print("           Strategy3 模拟交易测试系统")
    print("=" * 70)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)

def run_basic_simulation():
    """运行基础模拟测试"""
    print("\n🚀 启动基础模拟测试...")
    try:
        from trading_simulator import main as simulator_main
        simulator_main()
    except Exception as e:
        print(f"❌ 基础模拟测试失败: {e}")
        return False
    return True

def run_strategy3_integration():
    """运行Strategy3集成测试"""
    print("\n🚀 启动Strategy3集成测试...")
    try:
        from strategy_integration_test import main as integration_main
        integration_main()
    except Exception as e:
        print(f"❌ Strategy3集成测试失败: {e}")
        return False
    return True

def run_quick_test():
    """运行快速测试"""
    print("\n🚀 启动快速测试...")
    try:
        from trading_simulator import TradingSimulationTest
        test = TradingSimulationTest(initial_capital=50000)
        results = test.run_simulation(num_ticks=1000, save_results=False)
        if results:
            print("✅ 快速测试完成")
            metrics = results['performance_metrics']
            print(f"总收益率: {metrics.get('total_return', 0):.2%}")
            print(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}")
            print(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}")
        return True
    except Exception as e:
        print(f"❌ 快速测试失败: {e}")
        return False

def run_multi_scenario():
    """运行多场景测试"""
    print("\n🚀 启动多场景测试...")
    try:
        from trading_simulator import MultiScenarioTest
        multi_test = MultiScenarioTest()
        multi_test.run_all_scenarios(num_ticks=1500)
        return True
    except Exception as e:
        print(f"❌ 多场景测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖项"""
    print("\n🔍 检查依赖项...")
    
    required_packages = [
        'numpy', 'pandas', 'matplotlib', 'seaborn'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - 未安装")
            missing_packages.append(package)
    
    # 检查Strategy3
    try:
        from Strategy3 import Strategy3
        print("✅ Strategy3 - 可用")
    except ImportError:
        print("⚠️  Strategy3 - 不可用，将使用模拟策略")
    
    if missing_packages:
        print(f"\n缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖项检查通过")
    return True

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "=" * 50)
        print("请选择测试类型:")
        print("1. 快速测试 (1000 ticks)")
        print("2. 基础模拟测试 (3000 ticks)")
        print("3. Strategy3集成测试 (完整测试)")
        print("4. 多场景测试")
        print("5. 检查依赖项")
        print("6. 退出")
        print("=" * 50)
        
        choice = input("请输入选择 (1-6): ").strip()
        
        if choice == '1':
            run_quick_test()
        elif choice == '2':
            run_basic_simulation()
        elif choice == '3':
            run_strategy3_integration()
        elif choice == '4':
            run_multi_scenario()
        elif choice == '5':
            check_dependencies()
        elif choice == '6':
            print("👋 退出测试系统")
            break
        else:
            print("❌ 无效选择，请重新输入")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Strategy3 交易策略测试系统')
    parser.add_argument('--test-type', choices=['quick', 'basic', 'integration', 'multi', 'check'], 
                       help='测试类型')
    parser.add_argument('--interactive', action='store_true', help='交互式模式')
    
    args = parser.parse_args()
    
    print_banner()
    
    # 首先检查依赖项
    if not check_dependencies():
        print("❌ 依赖项检查失败，请安装缺少的包后重试")
        return
    
    if args.interactive or not args.test_type:
        interactive_menu()
    else:
        if args.test_type == 'quick':
            run_quick_test()
        elif args.test_type == 'basic':
            run_basic_simulation()
        elif args.test_type == 'integration':
            run_strategy3_integration()
        elif args.test_type == 'multi':
            run_multi_scenario()
        elif args.test_type == 'check':
            check_dependencies()

if __name__ == "__main__":
    main()
