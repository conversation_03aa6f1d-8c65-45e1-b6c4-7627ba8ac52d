from typing import Literal, List
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

class Params(BaseParams):
    """参数映射模型 - 无限易Pro规范化"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M5", title="K线周期")
    
    # 指标参数
    hull_period: int = Field(default=20, title="Hull均线周期", ge=10, le=50)
    stc_k_period: int = Field(default=14, title="STC的K线周期", ge=5, le=30)
    stc_d_period: int = Field(default=3, title="STC的D线周期", ge=2, le=10)
    stc_smooth: int = Field(default=3, title="STC平滑周期", ge=1, le=5)
    ut_period: int = Field(default=14, title="UT趋势周期", ge=7, le=21)
    ut_threshold: float = Field(default=0.5, title="UT趋势强度阈值", ge=0.3, le=0.7)
    
    # 交易参数
    order_volume: int = Field(default=1, title="报单数量", ge=1)
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    stop_loss_mult: float = Field(default=1.5, title="止损倍数", ge=1.0, le=3.0)
    take_profit_mult: float = Field(default=2.0, title="止盈倍数", ge=1.5, le=3.0)

class State(BaseState):
    """状态映射模型 - 无限易Pro规范化"""
    # 指标状态
    hull_ma: float = Field(default=0.0, title="Hull均线值")
    stc_k: float = Field(default=50.0, title="STC的K值")
    stc_d: float = Field(default=50.0, title="STC的D值")
    ut_strength: float = Field(default=0.5, title="UT趋势强度")
    market_status: Literal["trending", "ranging"] = Field(default="ranging", title="市场状态")
    
    # 交易状态
    stop_loss: float = Field(default=0.0, title="止损价")
    take_profit: float = Field(default=0.0, title="止盈价")
    entry_price: float = Field(default=0.0, title="入场价格")
    highest_price: float = Field(default=0.0, title="最高价")
    lowest_price: float = Field(default=0.0, title="最低价")

class HullMAStrategy(BaseStrategy):
    """UT+HULL+STC组合策略 - 无限易Pro投产版"""
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 初始化缓冲区
        self._buffers = {
            'price': [],
            'hull': [],
            'stc_k': [],
            'stc_d': [],
            'ut': []
        }
        
        # 交易状态
        self.tick: TickData = None
        self.kline_generator = None
        self.order_id = None
        self.signal_price = 0.0
        
    @property
    def indicator_data(self) -> dict[str, float]:
        """指标数据 - 无限易Pro界面显示"""
        return {
            "HULL_MA": self.state_map.hull_ma,
            "STC_K": self.state_map.stc_k,
            "STC_D": self.state_map.stc_d,
            "UT_STRENGTH": self.state_map.ut_strength,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit
        }
    
    def on_start(self):
        """策略启动初始化"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        
        # 重置状态
        self._buffers = {k: [] for k in self._buffers}
        self.tick = None
        self.order_id = None
        self.signal_price = 0.0
        
        # 初始化状态映射
        for field in ['entry_price', 'highest_price', 'lowest_price', 'stop_loss', 'take_profit']:
            setattr(self.state_map, field, 0.0)
        
        super().on_start()
    
    def on_tick(self, tick: TickData):
        """Tick数据处理"""
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)
        
        # 更新动态止损止盈
        if self.tick and self.get_position(self.params_map.instrument_id).net_position > 0:
            self._update_dynamic_stops()
    
    def on_order_cancel(self, order: OrderData) -> None:
        """撤单回调"""
        super().on_order_cancel(order)
        self.order_id = None
    
    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回调"""
        super().on_trade(trade, log)
        self.order_id = None
        
        # 更新入场价格
        if trade.direction == "buy":
            self.state_map.entry_price = trade.price
            self.state_map.highest_price = trade.price
            self.state_map.lowest_price = trade.price
        elif trade.direction == "sell":
            self.state_map.entry_price = 0.0
            self.state_map.highest_price = 0.0
            self.state_map.lowest_price = 0.0
    
    def callback(self, kline: KLineData) -> None:
        """K线回调"""
        self._calculate_indicators(kline)
        self._execute_trades()
        
        # 更新界面
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.indicator_data
        })
        
        if self.trading:
            self.update_status_bar()
            self.widget.update_indicator_data(self.indicator_data)
    
    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        self._calculate_indicators(kline)
        self.widget.recv_kline({
            "kline": kline,
            **self.indicator_data
        })
        self.update_status_bar()
    
    def _calculate_indicators(self, kline: KLineData) -> None:
        """计算所有技术指标"""
        # 更新价格历史
        self._buffers['price'].append(kline.close)
        max_period = max(self.params_map.hull_period * 2, self.params_map.stc_k_period * 2)
        if len(self._buffers['price']) > max_period:
            self._buffers['price'].pop(0)
        
        # 计算Hull均线
        if len(self._buffers['price']) >= self.params_map.hull_period:
            self.state_map.hull_ma = self._hull_moving_average(
                self._buffers['price'], self.params_map.hull_period)
            self._buffers['hull'].append(self.state_map.hull_ma)
            if len(self._buffers['hull']) > 5:
                self._buffers['hull'].pop(0)
        
        # 计算STC指标
        klines = self.kline_generator.producer.history(self.params_map.stc_k_period * 2)
        if klines and len(klines) >= self.params_map.stc_k_period:
            self.state_map.stc_k, self.state_map.stc_d = self._stochastic_oscillator(
                klines, self.params_map.stc_k_period, self.params_map.stc_d_period, 
                self.params_map.stc_smooth)
        
        # 计算UT趋势强度
        klines_ut = self.kline_generator.producer.history(self.params_map.ut_period + 1)
        if klines_ut and len(klines_ut) >= self.params_map.ut_period + 1:
            self.state_map.ut_strength = self._ut_strength(klines_ut, self.params_map.ut_period)
            self.state_map.market_status = "trending" if (
                self.state_map.ut_strength > self.params_map.ut_threshold
            ) else "ranging"
    
    def _execute_trades(self) -> None:
        """执行交易信号"""
        if not self.tick or not self._buffers['price']:
            return
            
        position = self.get_position(self.params_map.instrument_id)
        current_price = self.tick.last_price
        
        # 取消未成交订单
        if self.order_id is not None:
            self.cancel_order(self.order_id)
            self.order_id = None
        
        # 生成交易信号
        buy_signal = self._check_buy_signal(position)
        sell_signal = self._check_sell_signal(position)
        
        # 执行交易
        if sell_signal and position.net_position > 0:
            self.signal_price = -self.tick.bid_price1
            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.signal_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
        elif buy_signal and position.net_position < self.params_map.max_positions:
            self.signal_price = self.tick.ask_price1
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.signal_price,
                    order_direction="buy"
                )
    
    def _check_buy_signal(self, position) -> bool:
        """检查买入信号"""
        if not self._buffers['hull'] or len(self._buffers['hull']) < 2:
            return False
            
        conditions = [
            # HULL均线方向向上
            self._buffers['hull'][-1] > self._buffers['hull'][-2],
            # STC指标超卖
            self.state_map.stc_d < 30,
            # UT趋势强度大于阈值
            self.state_map.ut_strength > self.params_map.ut_threshold,
            # 价格位于HULL均线之上
            self.tick.last_price > self.state_map.hull_ma
        ]
        
        return all(conditions) and position.net_position < self.params_map.max_positions
    
    def _check_sell_signal(self, position) -> bool:
        """检查卖出信号"""
        if not self._buffers['hull'] or len(self._buffers['hull']) < 2:
            return False
            
        conditions = [
            # HULL均线方向向下
            self._buffers['hull'][-1] < self._buffers['hull'][-2],
            # STC指标超买
            self.state_map.stc_d > 70,
            # UT趋势强度小于阈值
            self.state_map.ut_strength < (self.params_map.ut_threshold * 0.7),
            # 价格位于HULL均线之下
            self.tick.last_price < self.state_map.hull_ma,
            # 止损触发
            self.tick.last_price <= self.state_map.stop_loss
        ]
        
        return any(conditions) and position.net_position > 0
    
    def _update_dynamic_stops(self) -> None:
        """更新动态止损止盈"""
        if not self.tick or self.state_map.entry_price <= 0:
            return
        
        current_price = self.tick.last_price
        
        # 更新最高价和最低价
        self.state_map.highest_price = max(self.state_map.highest_price, current_price)
        self.state_map.lowest_price = min(self.state_map.lowest_price, current_price)
        
        # 计算ATR(简化版)
        atr_period = min(14, len(self._buffers['price']))
        if atr_period > 1:
            tr_values = []
            for i in range(1, atr_period):
                high = max(self._buffers['price'][-i], self._buffers['price'][-i-1])
                low = min(self._buffers['price'][-i], self._buffers['price'][-i-1])
                tr_values.append(high - low)
            atr = sum(tr_values) / len(tr_values) if tr_values else 0
        else:
            atr = 0
        
        # 设置止损止盈
        if atr > 0:
            self.state_map.stop_loss = current_price - atr * self.params_map.stop_loss_mult
            self.state_map.take_profit = current_price + atr * self.params_map.take_profit_mult
        else:
            self.state_map.stop_loss = self.state_map.entry_price * 0.99
            self.state_map.take_profit = self.state_map.entry_price * 1.02
    
    def _hull_moving_average(self, prices: list, period: int) -> float:
        """计算Hull移动平均线"""
        if len(prices) < period:
            return prices[-1] if prices else 0.0
        
        half_period = int(period / 2)
        sqrt_period = int(np.sqrt(period))
        
        def weighted_moving_average(data: list, p: int) -> float:
            if len(data) < p:
                return 0.0
            weights = np.arange(1, p+1)
            return np.sum(np.array(data[-p:]) * weights) / np.sum(weights)
        
        wma_full = weighted_moving_average(prices, period)
        wma_half = weighted_moving_average(prices, half_period)
        raw_hull = 2 * wma_half - wma_full
        
        hull_series = []
        for i in range(sqrt_period):
            start_idx = -(period + i)
            if len(prices) >= period + i:
                wma_half_i = weighted_moving_average(prices[:start_idx], half_period)
                wma_full_i = weighted_moving_average(prices[:start_idx], period)
                hull_series.append(2 * wma_half_i - wma_full_i)
        
        return weighted_moving_average(hull_series, sqrt_period) if hull_series else raw_hull
    
    def _stochastic_oscillator(self, klines: list, k_period: int, 
                             d_period: int, smooth: int = 3) -> tuple:
        """计算随机指数(STC)的K值和D值"""
        if len(klines) < k_period:
            return 50.0, 50.0
        
        highs = [k.high for k in klines[-k_period:]]
        lows = [k.low for k in klines[-k_period:]]
        highest_high = max(highs)
        lowest_low = min(lows)
        close = klines[-1].close
        
        raw_k = 100 * (close - lowest_low) / (highest_high - lowest_low) if highest_high != lowest_low else 50.0
        
        self._buffers['stc_k'].append(raw_k)
        if len(self._buffers['stc_k']) > smooth:
            self._buffers['stc_k'].pop(0)
        k_value = sum(self._buffers['stc_k']) / len(self._buffers['stc_k'])
        
        self._buffers['stc_d'].append(k_value)
        if len(self._buffers['stc_d']) > d_period:
            self._buffers['stc_d'].pop(0)
        d_value = sum(self._buffers['stc_d']) / len(self._buffers['stc_d'])
        
        return k_value, d_value
    
    def _ut_strength(self, klines: list, period: int) -> float:
        """计算UT趋势强度指标"""
        if len(klines) < period + 1:
            return 0.5
        
        gains = []
        losses = []
        
        for i in range(1, period + 1):
            change = (klines[-i].close - klines[-i-1].close) / klines[-i-1].close
            gains.append(max(change, 0.0))
            losses.append(max(-change, 0.0))
        
        avg_gain = sum(gains) / period
        avg_loss = sum(losses) / period
        
        if avg_loss == 0:
            return 1.0
        
        rs = avg_gain / avg_loss
        return min(max(1 - (1 / (1 + rs)), 0.0), 1.0)