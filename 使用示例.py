#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy3.py 高级数学功能使用示例
展示如何使用新实现的高级数学模块
"""

import numpy as np
from Strategy3 import Strategy3, Params, KLineData, TickData

def demo_advanced_math_features():
    """演示高级数学功能"""
    print("=" * 60)
    print("Strategy3.py 高级数学功能演示")
    print("=" * 60)
    
    # 1. 创建策略实例
    print("1. 初始化策略...")
    strategy = Strategy3()
    
    # 设置参数启用所有高级功能
    strategy.params_map.enable_fuzzy = True
    strategy.params_map.enable_control = True
    strategy.params_map.enable_ml = True
    
    print("   ✓ 策略初始化完成")
    print("   ✓ 高级数学模块已启用")
    
    # 2. 模拟市场数据
    print("\n2. 模拟市场数据输入...")
    
    # 创建模拟K线数据
    for i in range(30):
        # 生成模拟价格数据（带趋势和噪声）
        base_price = 100
        trend = i * 0.1
        noise = np.random.normal(0, 0.5)
        price = base_price + trend + noise
        
        # 创建K线数据
        kline = KLineData(
            open=price - 0.2,
            high=price + 0.3,
            low=price - 0.3,
            close=price,
            volume=1000 + np.random.randint(-200, 200)
        )
        
        # 处理K线数据
        strategy.calc_indicator(kline)
        strategy.calc_signal(kline)
        
        # 每10个数据点输出一次状态
        if i % 10 == 9:
            print(f"   第{i+1}根K线处理完成")
            print(f"   当前价格: {price:.2f}")
            print(f"   Hull MA: {strategy.state_map.hull_ma:.3f}")
            print(f"   STC值: {strategy.state_map.stc_value:.1f}")
            print(f"   模糊信号: {strategy.state_map.fuzzy_signal:.3f}")
            print(f"   最终信号: {strategy.state_map.final_signal:.3f}")
    
    # 3. 展示高级数学模块的独立使用
    print("\n3. 高级数学模块独立使用演示...")
    
    # 群论模块演示
    print("\n   3.1 群论决策优化:")
    from Strategy3 import GroupTheoryDecisionEngine
    
    group_engine = GroupTheoryDecisionEngine()
    market_data = {
        'price': 103.5,
        'volume': 1200.0,
        'volatility': 0.025
    }
    
    group_result = group_engine.optimize_decision_rules(market_data)
    print(f"       群论优化信号: {group_result.get('optimized_signal', 0.0):.3f}")
    print(f"       决策置信度: {group_result.get('confidence', 0.0):.3f}")
    
    # 拓扑模糊集演示
    print("\n   3.2 拓扑模糊集分析:")
    from Strategy3 import ContinuousMembershipSystem
    
    topology_system = ContinuousMembershipSystem()
    topo_result = topology_system.process_market_data(market_data)
    print(f"       拓扑信号: {topo_result.get('topological_signal', 0.0):.3f}")
    print(f"       连续性状态: {topo_result.get('continuity_status', {})}")
    
    # 信息论模块演示
    print("\n   3.3 信息论不确定性分析:")
    from Strategy3 import EntropyCalculator
    
    entropy_calc = EntropyCalculator(20)
    # 添加价格数据
    for i in range(25):
        price = 100 + np.sin(i * 0.3) * 3 + np.random.normal(0, 0.5)
        entropy_calc.update_data(price)
    
    uncertainty = entropy_calc.market_uncertainty_quantification()
    print(f"       Shannon熵: {uncertainty.get('shannon_entropy', 0.0):.3f}")
    print(f"       不确定性水平: {uncertainty.get('uncertainty_level', 'unknown')}")
    print(f"       不确定性分数: {uncertainty.get('uncertainty_score', 0.0):.3f}")
    
    # 智能协作架构演示
    print("\n   3.4 智能协作架构:")
    from Strategy3 import MarketStatePerceiver, IntelligentScheduler
    
    perceiver = MarketStatePerceiver()
    scheduler = IntelligentScheduler()
    
    # 更新市场状态
    for i in range(15):
        price = 100 + i * 0.2
        volume = 1000 + np.random.normal(0, 100)
        volatility = 0.02 + abs(np.sin(i * 0.2)) * 0.01
        
        perceiver.update_market_data(price, volume, volatility)
        scheduler.update_market_state(price, volume, volatility)
    
    state_info = perceiver.detect_market_state()
    print(f"       检测到的市场状态: {state_info.get('current_state', 'unknown')}")
    print(f"       状态置信度: {state_info.get('confidence', 0.0):.3f}")
    print(f"       状态持续性: {state_info.get('state_persistence', 0.0):.3f}")
    
    # 预测下一个状态
    next_state = perceiver.predict_next_state()
    print(f"       预测下一状态: {next_state.get('predicted_state', 'unknown')}")
    print(f"       预测概率: {next_state.get('probability', 0.0):.3f}")
    
    # 4. 决策融合演示
    print("\n4. 决策融合引擎演示...")
    from Strategy3 import DecisionFusionEngine
    
    fusion_engine = DecisionFusionEngine()
    fusion_engine.register_modules(['module1', 'module2', 'module3'])
    
    # 模拟多个模块的决策结果
    module_results = {
        'module1': {'signal': 0.15, 'confidence': 0.8},
        'module2': {'signal': 0.08, 'confidence': 0.7},
        'module3': {'signal': -0.05, 'confidence': 0.6}
    }
    
    fusion_result = fusion_engine.fuse_module_decisions(module_results)
    print(f"   融合后信号: {fusion_result.get('fused_signal', 0.0):.3f}")
    print(f"   融合置信度: {fusion_result.get('fused_confidence', 0.0):.3f}")
    print(f"   使用的融合策略: {fusion_result.get('fusion_strategy', 'unknown')}")
    print(f"   一致性分数: {fusion_result.get('consistency_score', 0.0):.3f}")
    
    # 5. 性能总结
    print("\n5. 策略性能总结...")
    print(f"   总交易次数: {strategy.state_map.total_trades}")
    print(f"   获胜交易: {strategy.state_map.winning_trades}")
    print(f"   胜率: {strategy.state_map.win_rate:.1%}")
    print(f"   总盈利: {strategy.state_map.total_profit:.2f}")
    print(f"   当前波动率: {strategy.state_map.volatility:.4f}")
    print(f"   系统稳定性: {strategy.state_map.system_stability:.3f}")
    
    print("\n" + "=" * 60)
    print("高级数学功能演示完成！")
    print("=" * 60)
    
    return True

def demo_parameter_optimization():
    """演示参数优化功能"""
    print("\n" + "=" * 60)
    print("参数优化演示")
    print("=" * 60)
    
    # 创建自适应反馈优化器
    from Strategy3 import AdaptiveFeedbackOptimizer
    
    optimizer = AdaptiveFeedbackOptimizer(learning_rate=0.01)
    module_names = ['fuzzy', 'control', 'ml']
    optimizer.initialize_module_weights(module_names)
    
    print("1. 初始模块权重:")
    for name, weight in optimizer.get_optimized_weights().items():
        print(f"   {name}: {weight:.3f}")
    
    # 模拟优化过程
    print("\n2. 模拟优化过程...")
    for iteration in range(10):
        # 模拟模块结果
        module_results = {
            'fuzzy': {'signal': np.random.normal(0, 0.1), 'confidence': 0.7 + np.random.normal(0, 0.1)},
            'control': {'signal': np.random.normal(0, 0.08), 'confidence': 0.6 + np.random.normal(0, 0.1)},
            'ml': {'signal': np.random.normal(0, 0.12), 'confidence': 0.8 + np.random.normal(0, 0.1)}
        }
        
        # 模拟实际结果
        actual_outcome = np.random.normal(0, 0.1)
        
        # 执行优化
        opt_result = optimizer.optimize_collaboration(module_results, actual_outcome)
        
        if iteration % 3 == 2:
            print(f"   第{iteration+1}次优化完成")
            print(f"   优化效果: {opt_result.get('optimization_effect', 0.0):.3f}")
            print(f"   当前策略: {opt_result.get('current_strategy', 'unknown')}")
    
    print("\n3. 优化后的模块权重:")
    final_weights = optimizer.get_optimized_weights()
    for name, weight in final_weights.items():
        print(f"   {name}: {weight:.3f}")
    
    # 获取优化总结
    summary = optimizer.get_optimization_summary()
    print(f"\n4. 优化总结:")
    print(f"   平均优化效果: {summary.get('avg_recent_effect', 0.0):.3f}")
    print(f"   优化趋势: {summary.get('optimization_trend', 0.0):.3f}")
    print(f"   总优化次数: {summary.get('total_optimizations', 0)}")
    print(f"   当前学习率: {summary.get('learning_rate', 0.0):.4f}")
    
    return True

def main():
    """主演示函数"""
    print("Strategy3.py 高级数学功能完整演示")
    print("本演示将展示所有已实现的高级数学模块功能")
    
    try:
        # 基础功能演示
        demo_advanced_math_features()
        
        # 参数优化演示
        demo_parameter_optimization()
        
        print("\n🎉 所有演示完成！")
        print("\n📋 功能总结:")
        print("✓ 群论数学基础模块 - 对称性检测和决策优化")
        print("✓ 拓扑空间模糊集系统 - 连续性保证的模糊推理")
        print("✓ 概率测度论模块 - 严格的概率理论支持")
        print("✓ 泛函分析框架 - 函数空间映射和不动点求解")
        print("✓ 信息论优化模块 - 熵计算和不确定性量化")
        print("✓ 智能协作架构 - 市场状态感知和智能调度")
        print("✓ 决策融合引擎 - 贝叶斯融合和自适应优化")
        
        print("\n📈 这些高级数学模块已完全集成到Strategy3.py中，")
        print("   可以直接用于实际交易策略部署！")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中出现错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 演示成功完成")
    else:
        print("\n❌ 演示未能完成")
