"""
模糊理论交易策略演示和测试脚本
展示所有新增的模糊理论功能
"""

import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import asyncio
import time

# 模拟导入Strategy3（实际使用时需要正确的导入路径）
# from Strategy3 import Strategy3

class FuzzyStrategyDemo:
    """模糊策略演示类"""
    
    def __init__(self):
        self.demo_data = self.generate_demo_data()
        
    def generate_demo_data(self):
        """生成演示数据"""
        np.random.seed(42)
        
        # 生成模拟价格数据
        n_points = 1000
        time_series = np.arange(n_points)
        
        # 基础趋势
        trend = 0.001 * time_series + 100
        
        # 添加噪声和波动
        noise = np.random.normal(0, 0.5, n_points)
        volatility = 0.02 * np.sin(time_series / 50) + 0.01
        price_changes = np.random.normal(0, volatility)
        
        prices = trend + noise
        for i in range(1, n_points):
            prices[i] = prices[i-1] * (1 + price_changes[i])
        
        # 生成成交量数据
        volumes = np.random.lognormal(8, 0.5, n_points)
        
        # 生成其他指标
        stability = np.random.beta(2, 2, n_points)  # 0-1之间
        volatility_index = np.abs(np.diff(prices, prepend=prices[0])) / prices
        
        return {
            'prices': prices,
            'volumes': volumes,
            'stability': stability,
            'volatility': volatility_index,
            'timestamps': [datetime.now() - timedelta(minutes=i) for i in range(n_points-1, -1, -1)]
        }
    
    def demo_fuzzy_neural_network(self):
        """演示模糊神经网络功能"""
        print("=== 模糊神经网络演示 ===")
        
        try:
            # 这里需要实际的FuzzyNeuralNetwork类
            # from Strategy3 import FuzzyNeuralNetwork
            
            print("1. 创建模糊神经网络...")
            # fnn = FuzzyNeuralNetwork(input_size=3, hidden_sizes=[10, 8], output_size=3)
            
            print("2. 准备训练数据...")
            X = np.random.rand(100, 3)  # 模拟输入特征
            y = np.random.rand(100, 3)  # 模拟输出标签
            
            print("3. 训练网络...")
            # fnn.train(X, y, epochs=50)
            
            print("4. 进行预测...")
            # predictions = fnn.predict(X[:10])
            
            print("模糊神经网络演示完成")
            
        except Exception as e:
            print(f"模糊神经网络演示失败: {e}")
    
    def demo_fuzzy_clustering(self):
        """演示模糊聚类功能"""
        print("\n=== 模糊聚类演示 ===")
        
        try:
            # 生成聚类数据
            data = np.random.rand(200, 4)
            labels = np.random.randint(0, 3, 200)
            
            print("1. 创建模糊聚类器...")
            # from Strategy3 import FuzzyCMeansClusterer
            # clusterer = FuzzyCMeansClusterer(n_clusters=3)
            
            print("2. 执行聚类...")
            # cluster_centers, membership_matrix = clusterer.fit(data)
            
            print("3. 提取模糊规则...")
            # rules = clusterer.extract_fuzzy_rules(data, labels)
            
            print(f"聚类完成，生成了模拟规则")
            
        except Exception as e:
            print(f"模糊聚类演示失败: {e}")
    
    def demo_time_series_prediction(self):
        """演示模糊时间序列预测"""
        print("\n=== 模糊时间序列预测演示 ===")
        
        try:
            prices = self.demo_data['prices']
            
            print("1. 创建时间序列预测器...")
            # from Strategy3 import FuzzyTimeSeriesPredictor
            # predictor = FuzzyTimeSeriesPredictor(window_size=20)
            
            print("2. 训练预测模型...")
            # predictor.fit(prices[:800])
            
            print("3. 进行预测...")
            # predictions = predictor.predict(prices[800], steps=5)
            
            print("4. 计算预测准确率...")
            actual_values = prices[801:806]
            # accuracy = predictor.evaluate_predictions(predictions, actual_values)
            
            print(f"时间序列预测演示完成")
            
        except Exception as e:
            print(f"时间序列预测演示失败: {e}")
    
    def demo_multi_source_fusion(self):
        """演示多源信息融合"""
        print("\n=== 多源信息融合演示 ===")
        
        try:
            prices = self.demo_data['prices']
            volumes = self.demo_data['volumes']
            
            print("1. 创建信息融合器...")
            # from Strategy3 import MultiSourceInformationFusion
            # fusion = MultiSourceInformationFusion()
            
            print("2. 提取技术信号...")
            # technical_signals = fusion.extract_technical_features(prices, volumes)
            
            print("3. 分析市场情绪...")
            # sentiment_signals = fusion.extract_market_sentiment(prices, volumes)
            
            print("4. 融合多源信息...")
            # fused_signals = fusion.fuse_information(
            #     technical_signals, sentiment_signals
            # )
            
            print("多源信息融合演示完成")
            
        except Exception as e:
            print(f"多源信息融合演示失败: {e}")
    
    def demo_advanced_defuzzification(self):
        """演示高级去模糊化方法"""
        print("\n=== 高级去模糊化演示 ===")
        
        try:
            # 创建模拟模糊集
            fuzzy_set = [0.1, 0.3, 0.8, 1.0, 0.7, 0.4, 0.1]
            universe = [0, 1, 2, 3, 4, 5, 6]
            
            print("1. 重心法去模糊化...")
            # from Strategy3 import AdvancedDefuzzification
            # defuzz = AdvancedDefuzzification()
            # centroid_result = defuzz.centroid_method(fuzzy_set, universe)
            
            print("2. 面积中心法去模糊化...")
            # area_center_result = defuzz.area_center_method(fuzzy_set, universe)
            
            print("3. 最大值法去模糊化...")
            # max_result = defuzz.maximum_method(fuzzy_set, universe)
            
            print("4. q-ROFS得分函数...")
            # score = defuzz.q_rofs_score_function(0.8, 0.2, q=2)
            
            print("高级去模糊化演示完成")
            
        except Exception as e:
            print(f"高级去模糊化演示失败: {e}")
    
    def demo_dynamic_rule_library(self):
        """演示动态规则库"""
        print("\n=== 动态规则库演示 ===")
        
        try:
            print("1. 创建动态规则库...")
            # from Strategy3 import DynamicRuleLibrary
            # rule_lib = DynamicRuleLibrary(max_rules=20)
            
            print("2. 添加规则...")
            # rule_id = rule_lib.add_rule(
            #     condition=lambda s, v, p: s.get('High', 0) > 0.7,
            #     action=lambda: ("RiskHigh", "Aggressive", 0.9)
            # )
            
            print("3. 更新规则性能...")
            # rule_lib.update_rule_performance(rule_id, 0.8)
            
            print("4. 增量学习...")
            new_data = np.random.rand(20, 10)
            new_labels = np.random.randint(0, 3, 20)
            # rule_lib.incremental_learning(new_data, new_labels)
            
            print("5. 强化学习更新...")
            # rule_lib.reinforcement_learning_update(rule_id, 0.9)
            
            print("动态规则库演示完成")
            
        except Exception as e:
            print(f"动态规则库演示失败: {e}")
    
    async def demo_async_processing(self):
        """演示异步模糊处理"""
        print("\n=== 异步模糊处理演示 ===")
        
        try:
            print("1. 启动异步处理器...")
            # from async_fuzzy_processor import async_fuzzy_processor
            # await async_fuzzy_processor.start()
            
            print("2. 提交异步决策请求...")
            # 模拟模糊系统
            class MockFuzzySystem:
                def adaptive_inference(self, s_mem, v_mem, p_mem):
                    time.sleep(0.01)  # 模拟计算时间
                    return ("RiskMedium", "Normal", 0.75)
            
            mock_system = MockFuzzySystem()
            input_data = ({'High': 0.8}, {'Medium': 0.6}, {'Low': 0.4})
            
            # result = await async_fuzzy_processor.submit_decision_request(
            #     mock_system, input_data
            # )
            
            print("3. 批量处理请求...")
            batch_inputs = [input_data] * 10
            # batch_results = await async_fuzzy_processor.submit_batch_request(
            #     mock_system, batch_inputs
            # )
            
            print("4. 获取性能统计...")
            # stats = async_fuzzy_processor.get_performance_stats()
            
            print("异步模糊处理演示完成")
            
        except Exception as e:
            print(f"异步模糊处理演示失败: {e}")
    
    def demo_configuration_management(self):
        """演示配置管理"""
        print("\n=== 配置管理演示 ===")
        
        try:
            print("1. 加载配置...")
            # from fuzzy_config import fuzzy_config
            # config_summary = fuzzy_config.get_config_summary()
            
            print("2. 更新配置...")
            # fuzzy_config.set('learning_parameters.learning_rate', 0.02)
            
            print("3. 批量更新...")
            updates = {
                'fusion_weights.technical': 0.5,
                'fusion_weights.sentiment': 0.3
            }
            # fuzzy_config.update(updates)
            
            print("4. 导出配置...")
            # fuzzy_config.export_config('demo_config_export.yaml')
            
            print("配置管理演示完成")
            
        except Exception as e:
            print(f"配置管理演示失败: {e}")
    
    def demo_visualization(self):
        """演示可视化功能"""
        print("\n=== 可视化演示 ===")
        
        try:
            print("1. 创建可视化器...")
            # from fuzzy_visualization import fuzzy_viz
            
            print("2. 绘制隶属函数...")
            # 这里需要实际的模糊集对象
            
            print("3. 绘制规则性能...")
            # 模拟规则性能数据
            
            print("4. 绘制决策过程...")
            # 模拟决策数据
            
            print("可视化演示完成")
            
        except Exception as e:
            print(f"可视化演示失败: {e}")
    
    def run_full_demo(self):
        """运行完整演示"""
        print("开始模糊理论交易策略完整演示...")
        print("=" * 50)
        
        # 运行各个模块演示
        self.demo_fuzzy_neural_network()
        self.demo_fuzzy_clustering()
        self.demo_time_series_prediction()
        self.demo_multi_source_fusion()
        self.demo_advanced_defuzzification()
        self.demo_dynamic_rule_library()
        self.demo_configuration_management()
        self.demo_visualization()
        
        # 异步演示需要在事件循环中运行
        print("\n运行异步处理演示...")
        try:
            asyncio.run(self.demo_async_processing())
        except Exception as e:
            print(f"异步演示失败: {e}")
        
        print("\n" + "=" * 50)
        print("模糊理论交易策略演示完成！")
        print("\n主要功能包括:")
        print("✓ 模糊神经网络 (FNN)")
        print("✓ 动态规则库更新")
        print("✓ 多源信息融合")
        print("✓ 改进的去模糊化方法")
        print("✓ 模糊聚类规则提取")
        print("✓ 模糊时间序列预测")
        print("✓ 异步模糊决策处理")
        print("✓ 配置管理和热更新")
        print("✓ 可视化工具")

if __name__ == "__main__":
    demo = FuzzyStrategyDemo()
    demo.run_full_demo()
