# -*- coding: utf-8 -*-
from typing import Literal
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

class Params(BaseParams):
    """参数映射模型（新版PythonGO要求）"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量", ge=1)
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 指标参数
    stc_length: int = Field(default=10, title="STC周期长度", ge=5, le=20)
    stc_fast_length: int = Field(default=23, title="STC快速长度", ge=10, le=30)
    stc_slow_length: int = Field(default=50, title="STC慢速长度", ge=30, le=60)
    hull_period: int = Field(default=9, title="HULL均线周期", ge=5, le=20)


class State(BaseState):
    """状态映射模型（新版PythonGO要求）"""
    # 指标状态
    ut_signal: bool = Field(default=False, title="UT指标信号")
    stc_value: float = Field(default=0, title="STC指标值")
    stc_trend: Literal["up", "down", "neutral"] = Field(default="neutral", title="STC趋势方向")
    hull_ma: float = Field(default=0, title="HULL均线值")
    atr: float = Field(default=0, title="ATR值")  # 添加缺失的ATR字段
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    key_kline_low: float = Field(default=0, title="关键K线低点")


class DeepseekStrategy(BaseStrategy):
    """
    UT Bot Alerts+STC Indicator+HULL组合策略
    类名改为大驼峰命名法（PythonGO规范要求）
    """
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 15  # 趋势判断周期
        self.price_history = []
        self.trend_history = []
        self.volatility_history = []
        
        # 趋势判断参数
        self.trend_threshold = 0.6
        self.volatility_threshold = 0.5
        self.min_trend_duration = 5
        self.max_trend_duration = 30
        
        # 参数组合（改为类常量）
        self.PARAM_SETS = {
            "A": {"atr_period": 14, "stop_mult": 2.2, "profit_mult": 3.0, "trail_step": 1.0},
            "B": {"atr_period": 21, "stop_mult": 1.8, "profit_mult": 2.5, "trail_step": 0.5}
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        self.long_price = 0  # 添加缺失的long_price定义
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标（PythonGO规范要求）"""
        return {
            "HULL_MA": self.state_map.hull_ma,
            "ATR": self.state_map.atr
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标（PythonGO规范要求）"""
        return {
            "STC": self.state_map.stc_value,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop
        }

    def on_tick(self, tick: TickData) -> None:
        """Tick数据处理（规范化的回调方法）"""
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单回调（规范化的回调方法）"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回调（规范化的回调方法）"""
        super().on_trade(trade, log)
        self.order_id = None

    def on_start(self) -> None:
        """策略启动初始化（规范化的回调方法）"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        super().on_start()
        self._reset_state_variables()
        
        # 初始化参数（根据PythonGO最佳实践简化）
        self.current_params = (
            {
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            } 
            if self.params_map.mode == "manual" 
            else None
        )
        self.update_status_bar()

    def on_stop(self) -> None:
        """策略停止（规范化的回调方法）"""
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """K线回调（规范化的回调方法）"""
        self.calc_signal(kline)
        self.exec_signal()
        
        # 线图更新（符合PythonGO数据格式要求）
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        
        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调（规范化的回调方法）"""
        self.calc_signal(kline)
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })
        self.update_status_bar()

    def calc_trend(self, kline: KLineData) -> None:
        """趋势计算（符合PythonGO性能优化建议）"""
        self._update_price_history(kline)
        if len(self.price_history) != self.trend_period:
            return
            
        price_changes = np.diff(self.price_history)
        direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
        volatility = np.std(price_changes)
        
        self._update_volatility(volatility)
        self._update_trend_strength()
        self._update_trend_duration()
        
        is_trending = self._determine_trend_state(direction_consistency, volatility)
        self._update_trend_status(is_trending)
        self.state_map.volatility = volatility
        self._check_trend_reversal()

    def _update_price_history(self, kline: KLineData) -> None:
        """更新价格历史（私有方法规范）"""
        self.price_history.append(kline.close)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)

    def _update_volatility(self, volatility: float) -> None:
        """更新波动率（私有方法规范）"""
        self.volatility_history.append(volatility)
        if len(self.volatility_history) > 10:
            self.volatility_history.pop(0)

    def _update_trend_strength(self) -> None:
        """更新趋势强度（私有方法规范）"""
        hull_ma = self.kline_generator.producer.hull_moving_average(
            self.params_map.hull_period, array=True
        )
        if len(hull_ma) >= 2:
            slope = (hull_ma[-1] - hull_ma[-2]) / hull_ma[-2]
            self.state_map.trend_strength = min(1.0, abs(slope))

    def _update_trend_duration(self) -> None:
        """更新趋势持续时间（私有方法规范）"""
        if self.state_map.is_trending:
            self.state_map.trend_duration = min(
                self.state_map.trend_duration + 1, 
                self.max_trend_duration
            )
            if self.state_map.trend_duration >= self.max_trend_duration:
                self.state_map.is_trending = False
        else:
            self.state_map.trend_duration = 0

    def _determine_trend_state(self, direction_consistency: float, volatility: float) -> bool:
        """判断趋势状态（私有方法规范）"""
        return (
            direction_consistency > self.trend_threshold and
            volatility > self.volatility_threshold and
            self.state_map.trend_strength > 0.3 and
            (self.state_map.trend_duration >= self.min_trend_duration or 
             direction_consistency > 0.8)
        )

    def _update_trend_status(self, is_trending: bool) -> None:
        """更新趋势状态（私有方法规范）"""
        if is_trending == self.state_map.is_trending:
            return
            
        self.state_map.is_trending = is_trending
        self.state_map.trend_type = "A" if is_trending else "B"
        self.trend_history.append(is_trending)
        if len(self.trend_history) > 20:
            self.trend_history.pop(0)
        
        self.current_params = self.PARAM_SETS[self.state_map.trend_type]

    def _check_trend_reversal(self) -> None:
        """检查趋势反转（私有方法规范）"""
        if len(self.trend_history) < 3:
            return
            
        recent_trends = self.trend_history[-3:]
        if recent_trends.count(True) == 2 and recent_trends[-1] == False:
            mult = 0.9 if self.state_map.trend_type == "A" else 1.1
            self.current_params["stop_mult"] *= mult
            self.current_params["profit_mult"] *= mult

    def calc_indicator(self) -> None:
        """指标计算（符合PythonGO性能优化建议）"""
        if self.current_params is None:
            self.current_params = self.PARAM_SETS["A"]
            
        self._calculate_hull_ma()
        self._calculate_stc()
        self._calculate_ut_signal()
        
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state_map.atr = round(atr, 2)
        
        if self.tick:
            self._calculate_stop_profit()

    def _calculate_hull_ma(self) -> None:
        """计算HULL均线（私有方法规范）"""
        hull_ma = self.kline_generator.producer.hull_moving_average(
            self.params_map.hull_period, array=True
        )
        self.state_map.hull_ma = round(hull_ma[-1], 2) if hull_ma else 0

    def _calculate_stc(self) -> None:
        """计算STC指标（私有方法规范）"""
        stc = self.kline_generator.producer.schaff_trend_cycle(
            self.params_map.stc_length,
            self.params_map.stc_fast_length,
            self.params_map.stc_slow_length,
            array=True
        )
        if not stc:
            return
            
        self.state_map.stc_value = round(stc[-1], 2)
        
        if len(stc) >= 2:
            self.state_map.stc_trend = (
                "up" if stc[-1] > stc[-2] else 
                "down" if stc[-1] < stc[-2] else 
                "neutral"
            )

    def _calculate_ut_signal(self) -> None:
        """计算UT信号（私有方法规范）"""
        hull_ma = self.kline_generator.producer.hull_moving_average(
            self.params_map.hull_period, array=True
        )
        close_prices = self.kline_generator.producer.close(array=True)
        
        self.state_map.ut_signal = (
            len(hull_ma) >= 3 and 
            len(close_prices) > 0 and 
            close_prices[-1] > hull_ma[-1] and 
            hull_ma[-1] > hull_ma[-2] and 
            hull_ma[-2] > hull_ma[-3]
        )

    def _calculate_stop_profit(self) -> None:
        """计算止损止盈（私有方法规范）"""
        current_price = self.tick.last_price
        
        if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
            self.state_map.stop_loss = self.params_map.fixed_stop_loss
            self.state_map.take_profit = self.params_map.fixed_take_profit
        else:
            self.state_map.stop_loss = round(
                current_price - self.state_map.atr * self.current_params["stop_mult"], 2
            )
            self.state_map.take_profit = round(
                current_price + self.state_map.atr * self.current_params["profit_mult"], 2
            )
        
        self.state_map.trailing_stop = round(
            current_price - self.state_map.atr * self.current_params["trail_step"], 2
        )

    def update_dynamic_stops(self, current_price: float) -> None:
        """动态止盈止损更新（符合PythonGO性能优化建议）"""
        if self.position_size <= 0 or (self.params_map.mode == "manual" and self.params_map.use_fixed_stops):
            return
            
        self._update_price_extremes(current_price)
        self._update_profit_status(current_price)
        self._update_stop_loss(current_price)

    def _update_price_extremes(self, current_price: float) -> None:
        """更新价格极值（私有方法规范）"""
        self.state_map.highest_price = max(self.state_map.highest_price, current_price)
        self.state_map.lowest_price = min(self.state_map.lowest_price, current_price)

    def _update_profit_status(self, current_price: float) -> None:
        """更新盈利状态（私有方法规范）"""
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)
        
        if not self.is_trailing and self.state_map.current_profit > self.state_map.atr:
            self.is_trailing = True

    def _update_stop_loss(self, current_price: float) -> None:
        """更新止损价格（私有方法规范）"""
        if self.is_trailing:
            self.state_map.stop_loss = round(
                self.state_map.highest_price - self.state_map.atr * self.current_params["stop_mult"], 2
            )
        else:
            initial_stop = round(self.entry_price - self.state_map.atr * self.current_params["stop_mult"], 2)
            self.state_map.stop_loss = (
                min(initial_stop, self.state_map.key_kline_low) 
                if self.state_map.key_kline_low > 0 
                else initial_stop
            )

    def calc_signal(self, kline: KLineData) -> None:
        """信号计算（符合PythonGO性能优化建议）"""
        self.calc_trend(kline)
        self.calc_indicator()
        
        if self.tick:
            self.update_dynamic_stops(self.tick.last_price)
        
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position
        
        self._generate_signals(kline, position)

    def _generate_signals(self, kline: KLineData, position) -> None:
        """生成交易信号（私有方法规范）"""
        long_conditions = (
            self.state_map.ut_signal and
            self.state_map.stc_value < 25 and
            self.state_map.stc_trend == "up" and
            kline.close > self.state_map.hull_ma and
            kline.close > kline.open
        )
        
        if long_conditions and position.net_position == 0:
            self.state_map.key_kline_low = kline.low
        
        self.buy_signal = (
            long_conditions and 
            position.net_position < self.params_map.max_positions
        )
        
        self.sell_signal = (
            position.net_position > 0 and (
                (self.tick and self.tick.last_price <= self.state_map.stop_loss) or
                (self.state_map.stc_value > 75) or
                (self.state_map.stc_trend == "down") or
                (kline.close < self.state_map.hull_ma)
            )
        )
        
        self._update_price_levels(kline)

    def _update_price_levels(self, kline: KLineData) -> None:
        """更新价格水平（私有方法规范）"""
        self.long_price = kline.close
        if self.tick:
            self.long_price = (
                self.tick.ask_price2 
                if self.params_map.price_type == "D2" 
                else self.tick.ask_price1
            )

    def exec_signal(self) -> None:
        """执行信号（符合PythonGO交易规范）"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        
        if self.order_id is not None:
            self.cancel_order(self.order_id)

        self._execute_sell_signal(position)
        self._execute_buy_signal(position)

    def _execute_sell_signal(self, position) -> None:
        """执行卖出信号（私有方法规范）"""
        if not (position.net_position > 0 and self.sell_signal):
            return
            
        self.signal_price = -self.long_price
        
        if not self.trading:
            return
            
        self.order_id = self.auto_close_position(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            price=self.long_price,
            volume=position.net_position,
            order_direction="sell"
        )
        self._reset_state_variables()

    def _execute_buy_signal(self, position) -> None:
        """执行买入信号（私有方法规范）"""
        if not (self.buy_signal and position.net_position < self.params_map.max_positions):
            return
            
        self.signal_price = self.long_price
        
        if not self.trading:
            return
            
        self.order_id = self.send_order(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            volume=self.params_map.order_volume,
            price=self.long_price,
            order_direction="buy"
        )
        self.entry_price = self.long_price
        self.state_map.highest_price = self.long_price
        self.state_map.lowest_price = self.long_price
        
        if self.state_map.key_kline_low > 0:
            self.state_map.stop_loss = self.state_map.key_kline_low - (self.state_map.atr * 0.5)

    def _reset_state_variables(self) -> None:
        """重置状态变量（私有方法规范）"""
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.price_history = []
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        self.state_map.key_kline_low = 0