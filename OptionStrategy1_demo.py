from typing import Literal

import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # EMA参数设置
    ema_fast_period: int = Field(default=5, title="快速EMA周期", ge=3, le=10)
    ema_mid_period: int = Field(default=10, title="中速EMA周期", ge=8, le=20)
    ema_slow_period: int = Field(default=20, title="慢速EMA周期", ge=15, le=30)
    
    # RSI参数设置
    rsi_period: int = Field(default=14, title="RSI周期", ge=7, le=21)
    rsi_upper: int = Field(default=70, title="RSI上限", ge=60, le=80)
    rsi_lower: int = Field(default=30, title="RSI下限", ge=20, le=40)
    
    # 成交量参数设置
    volume_ma_period: int = Field(default=20, title="成交量MA周期", ge=10, le=30)
    volume_breakout_mult: float = Field(default=1.5, title="成交量突破倍数", ge=1.2, le=2.0)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=1.5, title="止损倍数", ge=1.0, le=2.0)
    profit_mult: float = Field(default=2.0, title="止盈倍数", ge=1.5, le=3.0)
    trail_step: float = Field(default=0.5, title="追踪步长", ge=0.3, le=1.0)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # RSI指标
    rsi: float = Field(default=0, title="RSI值")
    rsi_trend: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="RSI趋势")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量MA")
    volume_ratio: float = Field(default=0, title="量比")
    is_volume_breakout: bool = Field(default=False, title="成交量突破")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    
    # 趋势状态
    trend_type: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")


class OptionStrategy1_demo(BaseStrategy):
    """EMA+RSI+成交量突破买方策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 10  # 趋势判断周期
        self.trend_count = 0
        self.price_history = []
        self.volume_history = []
        self.rsi_history = []
        
        # 趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.4  # 波动率阈值
        self.min_trend_duration = 3  # 最小趋势持续周期
        self.max_trend_duration = 20  # 最大趋势持续周期
        
        # 参数组合
        self.param_sets = {
            "上升": {  # 上升趋势参数
                "stop_mult": 1.5,
                "profit_mult": 2.0,
                "trail_step": 0.5
            },
            "下降": {  # 下降趋势参数
                "stop_mult": 1.2,
                "profit_mult": 1.5,
                "trail_step": 0.3
            },
            "震荡": {  # 震荡行情参数
                "stop_mult": 1.0,
                "profit_mult": 1.5,
                "trail_step": 0.3
            }
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "EMA_FAST": self.state_map.ema_fast,
            "EMA_MID": self.state_map.ema_mid,
            "EMA_SLOW": self.state_map.ema_slow,
            "ATR": self.state_map.atr
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "RSI": self.state_map.rsi,
            "VOLUME_RATIO": self.state_map.volume_ratio,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop
        }

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.price_history = []
        self.trend_count = 0
        
        # 初始化动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        
        # 初始化参数
        if self.params_map.mode == "manual":
            self.current_params = {
                "ema": [5, 15, 30],  # 默认EMA参数
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = None  # 自动模式等待趋势判断

        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标和信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_signal(kline)

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        # 更新价格历史
        self.price_history.append(kline.close)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)
            
        if len(self.price_history) == self.trend_period:
            # 1. 计算价格变化率
            price_changes = np.diff(self.price_history)
            
            # 2. 计算方向一致性
            direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
            
            # 3. 计算波动率
            volatility = np.std(price_changes)
            
            # 4. 计算趋势强度
            # 使用EMA斜率和RSI趋势作为趋势强度指标
            ema_slopes = []
            for period in [self.params_map.ema_fast_period, self.params_map.ema_mid_period]:
                ema = self.kline_generator.producer.ema(period, array=True)
                if len(ema) >= 2:
                    slope = (ema[-1] - ema[-2]) / ema[-2]
                    ema_slopes.append(slope)
            
            # 计算趋势强度（结合EMA斜率和RSI）
            if ema_slopes:
                trend_strength = abs(np.mean(ema_slopes))
                if self.state_map.rsi_trend == "上升":
                    trend_strength *= 1.2
                elif self.state_map.rsi_trend == "下降":
                    trend_strength *= 0.8
                self.state_map.trend_strength = min(1.0, trend_strength)
            
            # 5. 判断趋势方向
            if direction_consistency > self.trend_threshold:
                if np.mean(price_changes) > 0:
                    new_trend = "上升"
                else:
                    new_trend = "下降"
            else:
                new_trend = "震荡"
            
            # 6. 更新趋势状态
            if new_trend != self.state_map.trend_type:
                self.state_map.trend_type = new_trend
                if new_trend != "震荡":
                    self.state_map.is_trending = True
                    self.state_map.trend_duration = 1
                else:
                    self.state_map.is_trending = False
                    self.state_map.trend_duration = 0
                
                # 更新参数
                self.current_params = self.param_sets[self.state_map.trend_type]
            elif self.state_map.is_trending:
                self.state_map.trend_duration += 1
                if self.state_map.trend_duration > self.max_trend_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
                    self.state_map.trend_type = "震荡"
                    self.current_params = self.param_sets["震荡"]
            
            # 7. 更新状态映射
            self.state_map.volatility = volatility

    def calc_indicator(self) -> None:
        """计算技术指标"""
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]  # 默认使用震荡参数
            
        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.params_map.ema_fast_period, array=True)
        ema_mid = self.kline_generator.producer.ema(self.params_map.ema_mid_period, array=True)
        ema_slow = self.kline_generator.producer.ema(self.params_map.ema_slow_period, array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        # 计算RSI
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period, array=True)
        self.state_map.rsi = round(rsi[-1], 2)
        
        # 计算RSI趋势
        if len(rsi) >= 3:
            rsi_slope = (rsi[-1] - rsi[-3]) / 2
            if rsi_slope > 1:
                self.state_map.rsi_trend = "上升"
            elif rsi_slope < -1:
                self.state_map.rsi_trend = "下降"
            else:
                self.state_map.rsi_trend = "震荡"
        
        # 计算成交量指标
        volume_array = self.kline_generator.producer.volume
        if len(volume_array) >= self.params_map.volume_ma_period:
            # 计算成交量MA
            volume_ma = np.mean(volume_array[-self.params_map.volume_ma_period:])
            current_volume = volume_array[-1]
            
            self.state_map.volume_ma = round(volume_ma, 2)
            self.state_map.volume_ratio = round(current_volume / volume_ma, 2)
            self.state_map.is_volume_breakout = (
                self.state_map.volume_ratio >= self.params_map.volume_breakout_mult
            )
        else:
            self.state_map.volume_ma = 0
            self.state_map.volume_ratio = 0
            self.state_map.is_volume_breakout = False
        
        # 计算ATR
        atr, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        self.state_map.atr = round(atr, 2)
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            # 根据模式选择止损止盈计算方式
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                # 使用固定止盈止损
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                # 使用ATR动态止盈止损
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * self.current_params["trail_step"], 2
            )

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损"""
        if self.position_size > 0:
            # 如果使用固定止盈止损，则不更新动态止损
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                return
                
            # 更新最高价和最低价
            if current_price > self.state_map.highest_price:
                self.state_map.highest_price = current_price
            if current_price < self.state_map.lowest_price:
                self.state_map.lowest_price = current_price
            
            # 计算当前盈亏
            self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
            
            # 更新最大盈亏
            if self.state_map.current_profit > self.state_map.max_profit:
                self.state_map.max_profit = self.state_map.current_profit
            
            # 动态调整止损
            if self.state_map.current_profit > 0:
                # 当盈利超过ATR的1倍时，启动追踪止损
                if not self.is_trailing and self.state_map.current_profit > self.state_map.atr:
                    self.is_trailing = True
                
                if self.is_trailing:
                    # 使用最高价回撤ATR的倍数作为止损
                    self.state_map.stop_loss = round(
                        self.state_map.highest_price - self.state_map.atr * self.current_params["stop_mult"], 2
                    )
            else:
                # 未盈利时使用初始止损
                self.state_map.stop_loss = round(
                    self.entry_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        # 计算趋势
        self.calc_trend(kline)
        
        # 计算指标
        self.calc_indicator()
        
        # 更新动态止盈止损
        if self.tick:
            self.update_dynamic_stops(self.tick.last_price)
        
        # 生成交易信号
        # 1. 基础趋势条件
        trend_condition = (
            self.state_map.trend_type == "上升" and
            self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
            self.state_map.trend_strength > 0.3
        )
        
        # 2. RSI条件
        rsi_condition = (
            30 <= self.state_map.rsi <= 70 and  # 避免过买过卖区域
            self.state_map.rsi_trend in ["上升", "震荡"]  # RSI趋势向上或震荡
        )
        
        # 3. 成交量条件
        volume_condition = self.state_map.is_volume_breakout
        
        # 生成买入信号
        self.buy_signal = (
            trend_condition and
            rsi_condition and
            volume_condition and
            not self.state_map.trend_type == "下降"  # 避免在下降趋势中开仓
        )
        
        # 生成卖出信号
        self.sell_signal = (
            # 趋势反转
            (self.state_map.trend_type == "下降" and self.state_map.trend_duration >= 2) or
            # RSI超买
            self.state_map.rsi >= self.params_map.rsi_upper or
            # EMA死叉
            (self.state_map.ema_fast < self.state_map.ema_mid and
             self.state_map.trend_strength < 0.2)
        )
        
        # 更新价格
        self.long_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2

    def exec_signal(self):
        """简易交易信号执行"""
        self.signal_price = 0

        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        if self.order_id is not None:
            # 挂单未成交
            self.cancel_order(self.order_id)

        # 检查是否达到最大持仓数
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False

        # 检查动态止损
        if self.tick and position.net_position > 0:
            if self.tick.last_price <= self.state_map.stop_loss:
                self.sell_signal = True

        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.long_price

            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                # 重置动态止盈止损相关变量
                self.entry_price = 0
                self.position_size = 0
                self.is_trailing = False
                self.state_map.highest_price = 0
                self.state_map.lowest_price = 0
                self.state_map.current_profit = 0
                self.state_map.max_profit = 0

        # 买开
        if self.buy_signal:
            self.signal_price = self.long_price

            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                # 记录入场价格
                self.entry_price = self.long_price
                self.state_map.highest_price = self.long_price
                self.state_map.lowest_price = self.long_price
