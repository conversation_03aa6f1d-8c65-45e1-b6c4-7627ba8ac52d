from typing import Literal
import numpy as np
import time
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator
from strategy_utils import AdaptiveParameterManager, MarketStateAnalyzer, SignalGenerator, RiskManager

class Params(BaseParams):
    # ...（略，保持不变）
    pass

class State(BaseState):
    # ...（略，保持不变）
    pass

class OptionStrategy6(BaseStrategy):
    """EMA+RSI+成交量突破买方策略（已接入全局寻优与风控模块）"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.order_time = 0
        self.buy_signal: bool = False
        self.sell_signal: bool = False

        # 历史数据
        self._price_history = []
        self._volume_history = []
        self._rsi_history = []
        self._max_history_length = max(
            self.params_map.ema_slow_period,
            self.params_map.rsi_period,
            self.params_map.atr_period,
            self.params_map.volume_ma_period
        )

        # 全局寻优与风控模块实例
        self.param_manager = AdaptiveParameterManager()
        self.market_analyzer = MarketStateAnalyzer()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()

        # 其它本地变量
        self.current_params = None
        self.param_sets = {
            "上升": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.5,
                "profit_mult": 2.0,
                "trail_step": 0.5
            },
            "下降": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.2,
                "profit_mult": 1.5,
                "trail_step": 0.3
            },
            "震荡": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.0,
                "profit_mult": 1.5,
                "trail_step": 0.3
            }
        }

    def _update_history_data(self, kline: KLineData) -> None:
        self._price_history.append(kline.close)
        self._volume_history.append(kline.volume)
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        self._rsi_history.append(rsi)
        max_length = self._max_history_length
        if len(self._price_history) > max_length:
            self._price_history.pop(0)
            self._volume_history.pop(0)
            self._rsi_history.pop(0)

    def calc_indicator(self) -> None:
        # ...（保持原有指标计算逻辑不变）
        pass

    def calc_trend(self, kline: KLineData) -> None:
        # ...（保持原有趋势计算逻辑不变）
        pass

    def calc_signal(self, kline: KLineData):
        """集成全局寻优与风控模块的信号计算"""
        self._update_history_data(kline)
        self.calc_trend(kline)
        self.calc_indicator()

        # 1. 用全局市场状态分析器判断市场状态
        indicators = {
            "volatility": self.state_map.volatility_ratio,
            "trend_strength": self.state_map.trend_strength,
            "volume": self.state_map.volume_ratio,
            "momentum": self.state_map.price_momentum
        }
        market_state = self.market_analyzer.analyze_market_state(indicators)

        # 2. 用全局参数管理器自适应参数
        performance = {"win_rate": 0.6}  # 可根据实际绩效填充
        self.current_params = self.param_manager.update_parameters(market_state, performance)

        # 3. 用全局信号生成器生成信号
        signal = self.signal_generator.generate_signal(indicators, market_state)
        self.buy_signal = signal["is_valid"]
        self.signal_strength = signal["score"]

        # 4. 用全局风控管理器判断仓位
        account = self.get_account()
        self.risk_manager.update_account_status(account.available)
        self.position_size = self.risk_manager.get_position_size(self.tick.last_price if self.tick else kline.close)

        # 5. 其它本地风控逻辑可逐步迁移至 self.risk_manager

    def exec_signal(self):
        """交易信号执行（可逐步迁移风控与下单逻辑）"""
        if not self.buy_signal:
            return
        # ...（保持原有下单逻辑或逐步迁移到 RiskManager）

    # 其它方法保持不变或逐步迁移
