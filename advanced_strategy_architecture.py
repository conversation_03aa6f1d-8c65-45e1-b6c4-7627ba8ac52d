"""
高级策略架构设计
基于最佳实践的全面优化策略框架
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from enum import Enum


class MarketRegime(Enum):
    """市场状态枚举"""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    BREAKOUT = "breakout"


class VolatilityRegime(Enum):
    """波动率状态枚举"""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"


@dataclass
class SignalScore:
    """信号评分"""
    trend_score: float      # 趋势评分 0-1
    momentum_score: float   # 动量评分 0-1
    volatility_score: float # 波动率评分 0-1
    volume_score: float     # 成交量评分 0-1
    overall_score: float    # 综合评分 0-1
    confidence: float       # 信号置信度 0-1


@dataclass
class RiskParameters:
    """风险参数"""
    stop_loss_atr_mult: float
    take_profit_atr_mult: float
    position_size_mult: float
    max_risk_per_trade: float
    trailing_stop_atr_mult: float


class MarketRegimeDetector:
    """市场状态检测器"""
    
    def __init__(self):
        self.adx_period = 14
        self.bb_period = 20
        self.bb_std = 2
        
    def detect_regime(self, data: pd.DataFrame, current_idx: int) -> MarketRegime:
        """检测当前市场状态"""
        if current_idx < 50:
            return MarketRegime.RANGING
            
        # 计算ADX
        adx = self._calculate_adx(data, current_idx)
        
        # 计算布林带宽度
        bb_width = self._calculate_bb_width(data, current_idx)
        
        # 计算价格趋势
        price_trend = self._calculate_price_trend(data, current_idx)
        
        # 判断市场状态
        if adx > 30 and abs(price_trend) > 0.02:
            if price_trend > 0:
                return MarketRegime.TRENDING_UP
            else:
                return MarketRegime.TRENDING_DOWN
        elif bb_width > np.percentile(data['close'].rolling(100).std(), 80):
            return MarketRegime.HIGH_VOLATILITY
        elif adx < 20:
            return MarketRegime.RANGING
        else:
            return MarketRegime.BREAKOUT
    
    def _calculate_adx(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算ADX"""
        if current_idx < self.adx_period + 1:
            return 20.0
            
        high = data['high'].iloc[current_idx-self.adx_period:current_idx+1]
        low = data['low'].iloc[current_idx-self.adx_period:current_idx+1]
        close = data['close'].iloc[current_idx-self.adx_period:current_idx+1]
        
        # 简化ADX计算
        tr = np.maximum(high - low, 
                       np.maximum(abs(high - close.shift(1)), 
                                 abs(low - close.shift(1))))
        atr = tr.rolling(self.adx_period).mean().iloc[-1]
        
        # 使用ATR作为ADX的代理
        return min(50, atr / close.iloc[-1] * 1000)
    
    def _calculate_bb_width(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算布林带宽度"""
        if current_idx < self.bb_period:
            return 0.05
            
        close = data['close'].iloc[current_idx-self.bb_period+1:current_idx+1]
        sma = close.mean()
        std = close.std()
        
        return (std * 2 * 2) / sma if sma > 0 else 0.05
    
    def _calculate_price_trend(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算价格趋势"""
        if current_idx < 20:
            return 0.0
            
        close = data['close'].iloc[current_idx-19:current_idx+1]
        return (close.iloc[-1] - close.iloc[0]) / close.iloc[0]


class VolatilityRegimeDetector:
    """波动率状态检测器"""
    
    def __init__(self):
        self.lookback_period = 20
        
    def detect_volatility_regime(self, data: pd.DataFrame, current_idx: int) -> VolatilityRegime:
        """检测波动率状态"""
        if current_idx < self.lookback_period:
            return VolatilityRegime.NORMAL
            
        # 计算当前ATR百分比
        atr = data['atr'].iloc[current_idx]
        price = data['close'].iloc[current_idx]
        atr_pct = atr / price if price > 0 else 0.01
        
        # 计算历史ATR百分比分位数
        historical_atr = data['atr'].iloc[current_idx-self.lookback_period:current_idx]
        historical_prices = data['close'].iloc[current_idx-self.lookback_period:current_idx]
        historical_atr_pct = historical_atr / historical_prices
        
        percentile_25 = np.percentile(historical_atr_pct, 25)
        percentile_75 = np.percentile(historical_atr_pct, 75)
        
        if atr_pct < percentile_25:
            return VolatilityRegime.LOW
        elif atr_pct > percentile_75:
            return VolatilityRegime.HIGH
        else:
            return VolatilityRegime.NORMAL


class MultiIndicatorSignalGenerator:
    """多指标信号生成器"""
    
    def __init__(self):
        # 指标参数
        self.ema_fast = 8
        self.ema_medium = 21
        self.ema_slow = 50
        self.rsi_period = 14
        self.macd_fast = 12
        self.macd_slow = 26
        self.macd_signal = 9
        self.bb_period = 20
        self.bb_std = 2
        
    def generate_signal_score(self, data: pd.DataFrame, current_idx: int) -> SignalScore:
        """生成信号评分"""
        if current_idx < self.ema_slow:
            return SignalScore(0, 0, 0, 0, 0, 0)
        
        # 计算各指标评分
        trend_score = self._calculate_trend_score(data, current_idx)
        momentum_score = self._calculate_momentum_score(data, current_idx)
        volatility_score = self._calculate_volatility_score(data, current_idx)
        volume_score = self._calculate_volume_score(data, current_idx)
        
        # 计算综合评分（加权平均）
        weights = [0.3, 0.3, 0.2, 0.2]  # 趋势、动量、波动率、成交量权重
        overall_score = (trend_score * weights[0] + 
                        momentum_score * weights[1] + 
                        volatility_score * weights[2] + 
                        volume_score * weights[3])
        
        # 计算置信度
        confidence = self._calculate_confidence(trend_score, momentum_score, 
                                              volatility_score, volume_score)
        
        return SignalScore(trend_score, momentum_score, volatility_score, 
                          volume_score, overall_score, confidence)
    
    def _calculate_trend_score(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算趋势评分"""
        ema_fast = data['ema_8'].iloc[current_idx]
        ema_medium = data['ema_21'].iloc[current_idx]
        ema_slow = data['ema_50'].iloc[current_idx]
        
        # EMA排列评分
        if ema_fast > ema_medium > ema_slow:
            trend_score = 1.0
        elif ema_fast < ema_medium < ema_slow:
            trend_score = -1.0
        else:
            # 计算部分排列的评分
            score = 0
            if ema_fast > ema_medium:
                score += 0.5
            if ema_medium > ema_slow:
                score += 0.5
            trend_score = score - 0.5  # 转换为-0.5到1.0的范围
        
        return max(0, trend_score)  # 只返回正向趋势评分
    
    def _calculate_momentum_score(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算动量评分"""
        rsi = data['rsi'].iloc[current_idx]
        macd = data['macd'].iloc[current_idx]
        macd_signal = data['macd_signal'].iloc[current_idx]
        
        # RSI评分 (30-70为正常区间)
        if 30 <= rsi <= 70:
            rsi_score = 1.0
        elif rsi < 30:
            rsi_score = 0.5  # 超卖，可能反弹
        else:  # rsi > 70
            rsi_score = 0.2  # 超买，风险较高
        
        # MACD评分
        macd_score = 1.0 if macd > macd_signal else 0.0
        
        return (rsi_score + macd_score) / 2
    
    def _calculate_volatility_score(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算波动率评分"""
        close = data['close'].iloc[current_idx]
        bb_upper = data['bb_upper'].iloc[current_idx]
        bb_lower = data['bb_lower'].iloc[current_idx]
        bb_middle = data['bb_middle'].iloc[current_idx]
        
        # 布林带位置评分
        if bb_lower <= close <= bb_upper:
            # 在布林带内，根据位置评分
            position = (close - bb_lower) / (bb_upper - bb_lower)
            if 0.2 <= position <= 0.8:
                return 1.0  # 在中间区域，较安全
            else:
                return 0.6  # 接近边界，风险适中
        else:
            return 0.2  # 在布林带外，风险较高
    
    def _calculate_volume_score(self, data: pd.DataFrame, current_idx: int) -> float:
        """计算成交量评分"""
        if current_idx < 20:
            return 0.5
            
        current_volume = data['volume'].iloc[current_idx]
        avg_volume = data['volume'].iloc[current_idx-19:current_idx].mean()
        
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # 成交量评分
        if 1.2 <= volume_ratio <= 3.0:
            return 1.0  # 成交量适度放大
        elif 0.8 <= volume_ratio < 1.2:
            return 0.7  # 成交量正常
        elif volume_ratio > 3.0:
            return 0.5  # 成交量过度放大，可能是异常
        else:
            return 0.3  # 成交量萎缩
    
    def _calculate_confidence(self, trend: float, momentum: float, 
                            volatility: float, volume: float) -> float:
        """计算信号置信度"""
        scores = [trend, momentum, volatility, volume]
        
        # 计算一致性
        high_scores = sum(1 for score in scores if score > 0.7)
        low_scores = sum(1 for score in scores if score < 0.3)
        
        if high_scores >= 3:
            return 0.9  # 高置信度
        elif high_scores >= 2 and low_scores == 0:
            return 0.7  # 中等置信度
        elif high_scores >= 1 and low_scores <= 1:
            return 0.5  # 低置信度
        else:
            return 0.2  # 很低置信度


class AdaptiveRiskManager:
    """自适应风险管理器"""
    
    def __init__(self):
        self.base_risk_per_trade = 0.02  # 基础风险2%
        self.max_risk_per_trade = 0.03   # 最大风险3%
        self.min_risk_per_trade = 0.01   # 最小风险1%
        
    def get_risk_parameters(self, market_regime: MarketRegime, 
                          volatility_regime: VolatilityRegime,
                          signal_confidence: float) -> RiskParameters:
        """获取自适应风险参数"""
        
        # 基础参数
        base_stop_mult = 2.0
        base_profit_mult = 2.0
        base_position_mult = 1.0
        base_trailing_mult = 1.0
        
        # 根据市场状态调整
        if market_regime in [MarketRegime.TRENDING_UP, MarketRegime.TRENDING_DOWN]:
            stop_mult = base_stop_mult * 0.8  # 趋势市场收紧止损
            profit_mult = base_profit_mult * 1.2  # 扩大止盈
            trailing_mult = base_trailing_mult * 0.8
        elif market_regime == MarketRegime.RANGING:
            stop_mult = base_stop_mult * 1.2  # 震荡市场放宽止损
            profit_mult = base_profit_mult * 0.8  # 收紧止盈
            trailing_mult = base_trailing_mult * 1.2
        else:  # HIGH_VOLATILITY, BREAKOUT
            stop_mult = base_stop_mult * 1.5  # 高波动放宽止损
            profit_mult = base_profit_mult * 1.0
            trailing_mult = base_trailing_mult * 1.3
        
        # 根据波动率状态调整
        if volatility_regime == VolatilityRegime.LOW:
            stop_mult *= 0.8
            position_mult = base_position_mult * 1.2
        elif volatility_regime == VolatilityRegime.HIGH:
            stop_mult *= 1.3
            position_mult = base_position_mult * 0.7
        else:
            position_mult = base_position_mult
        
        # 根据信号置信度调整
        confidence_mult = 0.5 + signal_confidence * 0.5  # 0.5-1.0
        position_mult *= confidence_mult
        
        # 计算风险
        risk_per_trade = self.base_risk_per_trade * confidence_mult
        risk_per_trade = max(self.min_risk_per_trade, 
                           min(self.max_risk_per_trade, risk_per_trade))
        
        return RiskParameters(
            stop_loss_atr_mult=stop_mult,
            take_profit_atr_mult=profit_mult,
            position_size_mult=position_mult,
            max_risk_per_trade=risk_per_trade,
            trailing_stop_atr_mult=trailing_mult
        )


def print_architecture_summary():
    """打印架构总结"""
    print("=" * 80)
    print("高级策略架构设计")
    print("=" * 80)
    
    components = [
        "1. 市场状态检测器 (MarketRegimeDetector)",
        "   - 趋势上涨/下跌/震荡/高波动/突破状态识别",
        "   - 基于ADX、布林带宽度、价格趋势综合判断",
        "",
        "2. 波动率状态检测器 (VolatilityRegimeDetector)", 
        "   - 低/正常/高波动率状态识别",
        "   - 基于ATR百分比的历史分位数判断",
        "",
        "3. 多指标信号生成器 (MultiIndicatorSignalGenerator)",
        "   - 趋势评分：EMA多头排列",
        "   - 动量评分：RSI + MACD",
        "   - 波动率评分：布林带位置",
        "   - 成交量评分：成交量放大确认",
        "   - 综合评分：加权平均 + 置信度计算",
        "",
        "4. 自适应风险管理器 (AdaptiveRiskManager)",
        "   - 根据市场状态动态调整止损止盈",
        "   - 根据波动率调整仓位大小",
        "   - 根据信号置信度调整风险敞口",
        "",
        "5. 核心优化特性:",
        "   ✓ 多层信号过滤和确认机制",
        "   ✓ 自适应参数调整系统",
        "   ✓ 智能风险管理",
        "   ✓ 市场状态感知",
        "   ✓ 信号质量评估",
        "   ✓ 动态止损止盈",
        "",
        "6. 预期性能目标:",
        "   - 胜率: 50-60%",
        "   - 夏普比率: > 1.5",
        "   - 最大回撤: < 15%",
        "   - 盈亏比: > 1.3",
    ]
    
    for component in components:
        print(component)


if __name__ == "__main__":
    print_architecture_summary()
