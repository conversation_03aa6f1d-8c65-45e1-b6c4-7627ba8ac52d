"""
实用优化策略 - Optimized Practical Strategy
基于原策略的实用改进版本，专注于提升实际性能
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from strategy_backtester import BaseBacktestStrategy


class OptimizedPracticalStrategy(BaseBacktestStrategy):
    """实用优化策略 - 基于原策略的实际改进"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 核心参数 - 基于测试结果优化
        self.ema_fast = 8
        self.ema_slow = 21
        self.rsi_period = 14
        self.atr_period = 14
        
        # 优化的风险参数
        self.stop_mult = 1.8        # 降低止损倍数
        self.profit_mult = 2.5      # 提高止盈倍数
        self.rsi_oversold = 35      # RSI超卖阈值
        self.rsi_overbought = 65    # RSI超买阈值
        
        # 信号过滤参数
        self.min_volume_ratio = 1.1  # 最小成交量比率
        self.trend_strength_threshold = 0.02  # 趋势强度阈值
        
        # 交易控制
        self.max_trades_per_day = 2  # 每日最大交易次数
        self.min_trade_interval = 6  # 最小交易间隔(小时)
        
        # 状态跟踪
        self.last_trade_time = None
        self.daily_trade_count = 0
        self.current_date = None
        
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算技术指标"""
        # EMA
        data['ema_fast'] = data['close'].ewm(span=self.ema_fast, adjust=False).mean()
        data['ema_slow'] = data['close'].ewm(span=self.ema_slow, adjust=False).mean()
        
        # RSI
        data['rsi'] = self._calculate_rsi(data['close'], self.rsi_period)
        
        # ATR
        data['atr'] = self._calculate_atr(data['high'], data['low'], data['close'], self.atr_period)
        
        # 成交量移动平均
        data['volume_ma'] = data['volume'].rolling(window=20).mean()
        
        # 趋势强度
        data['trend_strength'] = abs(data['ema_fast'] - data['ema_slow']) / data['close']
        
        return data
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = delta.where(delta > 0, 0).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算指标
        data = self.calculate_indicators(data)
        
        # 初始化信号
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        
        # 状态跟踪
        in_position = False
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        
        for i in range(max(self.ema_slow, self.rsi_period, self.atr_period), len(data)):
            current_data = data.iloc[i]
            current_price = current_data['close']
            current_time = data.index[i]
            
            # 重置每日交易计数
            if self.current_date != current_time.date():
                self.current_date = current_time.date()
                self.daily_trade_count = 0
            
            # 检查退出条件
            if in_position:
                should_exit, exit_reason = self._check_exit_conditions(
                    current_price, stop_loss, take_profit, current_data
                )
                
                if should_exit:
                    signals.loc[current_time, 'action'] = 'sell'
                    signals.loc[current_time, 'reason'] = exit_reason
                    in_position = False
                    continue
            
            # 检查入场条件
            if not in_position:
                should_enter, enter_reason = self._check_entry_conditions(
                    current_data, data.iloc[i-20:i+1], current_time
                )
                
                if should_enter:
                    signals.loc[current_time, 'action'] = 'buy'
                    signals.loc[current_time, 'reason'] = enter_reason
                    
                    # 设置止损止盈
                    in_position = True
                    entry_price = current_price
                    atr = current_data['atr']
                    stop_loss = current_price - atr * self.stop_mult
                    take_profit = current_price + atr * self.profit_mult
                    
                    # 更新交易记录
                    self.last_trade_time = current_time
                    self.daily_trade_count += 1
        
        return signals
    
    def _check_exit_conditions(self, current_price: float, stop_loss: float, 
                             take_profit: float, current_data: pd.Series) -> Tuple[bool, str]:
        """检查退出条件"""
        
        # 止损
        if current_price <= stop_loss:
            return True, "stop_loss"
        
        # 止盈
        if current_price >= take_profit:
            return True, "take_profit"
        
        # RSI极端值退出
        rsi = current_data['rsi']
        if rsi > 75:  # 极度超买
            return True, "rsi_overbought"
        
        # EMA交叉退出
        if current_data['ema_fast'] < current_data['ema_slow']:
            return True, "ema_cross_down"
        
        return False, ""
    
    def _check_entry_conditions(self, current_data: pd.Series, 
                               recent_data: pd.DataFrame, 
                               current_time) -> Tuple[bool, str]:
        """检查入场条件"""
        
        # 交易频率控制
        if self.daily_trade_count >= self.max_trades_per_day:
            return False, ""
        
        if (self.last_trade_time is not None and 
            (current_time - self.last_trade_time).total_seconds() < self.min_trade_interval * 3600):
            return False, ""
        
        # 基本技术条件
        ema_fast = current_data['ema_fast']
        ema_slow = current_data['ema_slow']
        rsi = current_data['rsi']
        volume = current_data['volume']
        volume_ma = current_data['volume_ma']
        trend_strength = current_data['trend_strength']
        
        # 检查数据有效性
        if pd.isna(ema_fast) or pd.isna(ema_slow) or pd.isna(rsi) or pd.isna(volume_ma):
            return False, ""
        
        # 1. EMA多头排列
        if ema_fast <= ema_slow:
            return False, ""
        
        # 2. RSI在合理区间
        if rsi < self.rsi_oversold or rsi > self.rsi_overbought:
            return False, ""
        
        # 3. 成交量确认
        volume_ratio = volume / volume_ma if volume_ma > 0 else 0
        if volume_ratio < self.min_volume_ratio:
            return False, ""
        
        # 4. 趋势强度确认
        if trend_strength < self.trend_strength_threshold:
            return False, ""
        
        # 5. 价格位置确认 - 价格在EMA之上
        current_price = current_data['close']
        if current_price < ema_fast:
            return False, ""
        
        # 6. 动量确认 - 近期价格上涨
        if len(recent_data) >= 3:
            recent_close = recent_data['close'].iloc[-3:]
            if recent_close.iloc[-1] <= recent_close.iloc[0]:
                return False, ""
        
        return True, "multi_condition_confirmed"
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            return 1
        return 0
    
    def get_strategy_info(self) -> Dict:
        """获取策略信息"""
        return {
            'name': 'Optimized Practical Strategy',
            'version': '1.0',
            'description': '基于原策略的实用改进版本',
            'key_improvements': [
                '优化止损止盈比例 (1.8:2.5)',
                'RSI过滤机制',
                '成交量确认',
                '趋势强度验证',
                '交易频率控制',
                '多重技术确认'
            ]
        }


def print_strategy_summary():
    """打印策略总结"""
    print("=" * 80)
    print("Optimized Practical Strategy - 实用优化策略")
    print("=" * 80)
    
    improvements = [
        "核心改进:",
        "✓ 优化风险参数: 止损1.8倍ATR, 止盈2.5倍ATR",
        "✓ RSI过滤: 35-65区间确保合理入场",
        "✓ 成交量确认: 要求成交量放大1.1倍",
        "✓ 趋势强度验证: 确保有足够的趋势动力",
        "✓ 交易频率控制: 每日最多2次交易",
        "✓ 多重技术确认: 6个条件同时满足才入场",
        "",
        "预期改进:",
        "• 降低交易频率，减少交易成本",
        "• 提高信号质量，改善胜率",
        "• 优化风险收益比",
        "• 增强策略稳定性",
        "",
        "目标性能:",
        "• 胜率: 45-55%",
        "• 夏普比率: > 1.0",
        "• 最大回撤: < 10%",
        "• 年化收益: > 15%"
    ]
    
    for improvement in improvements:
        print(improvement)


if __name__ == "__main__":
    print_strategy_summary()
