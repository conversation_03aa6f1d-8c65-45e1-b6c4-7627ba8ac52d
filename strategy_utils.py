from typing import Any, Dict, List
import numpy as np

class AdaptiveParameterManager:
    """参数自适应管理器（通用）"""
    def __init__(self):
        self.base_params = {
            "ema": [5, 15, 30],
            "stop_mult": 1.2,
            "profit_mult": 1.5,
            "trail_step": 0.3
        }
        self.param_history = {k: [] for k in self.base_params}
        self.performance_history = []
        self.max_history_length = 100
        self.learning_rate = 0.1
        self.performance_threshold = 0.6
        self.state_transition_matrix = {
            "trending": {"trending": 0.6, "choppy": 0.2, "high_volatility": 0.1, "low_volatility": 0.1},
            "choppy": {"trending": 0.2, "choppy": 0.5, "high_volatility": 0.2, "low_volatility": 0.1},
            "high_volatility": {"trending": 0.1, "choppy": 0.3, "high_volatility": 0.4, "low_volatility": 0.2},
            "low_volatility": {"trending": 0.3, "choppy": 0.2, "high_volatility": 0.1, "low_volatility": 0.4}
        }

    def predict_market_state(self, current_state: str) -> str:
        transitions = self.state_transition_matrix.get(current_state, {})
        if not transitions:
            return current_state
        states, probs = list(transitions.keys()), list(transitions.values())
        return np.random.choice(states, p=probs)

    def update_parameters(self, market_state: str, performance: dict) -> dict:
        next_state = self.predict_market_state(market_state)
        params = self.base_params.copy()
        if next_state == "trending":
            params["ema"] = [x * 1.2 for x in params["ema"]]
            params["stop_mult"] *= 0.8
            params["profit_mult"] *= 1.2
        elif next_state == "choppy":
            params["ema"] = [x * 0.8 for x in params["ema"]]
            params["stop_mult"] *= 1.2
            params["profit_mult"] *= 0.8
        elif next_state == "high_volatility":
            params["stop_mult"] *= 1.5
            params["profit_mult"] *= 0.7
        elif next_state == "low_volatility":
            params["stop_mult"] *= 0.7
            params["profit_mult"] *= 1.5
        # 性能微调
        if performance.get("win_rate", 1) < self.performance_threshold:
            params["stop_mult"] *= 0.9
            params["profit_mult"] *= 0.8
        elif performance.get("win_rate", 0) > 0.7:
            params["stop_mult"] *= 1.1
            params["profit_mult"] *= 1.2
        self._update_param_history(params)
        return params

    def _update_param_history(self, params: dict) -> None:
        for k, v in params.items():
            self.param_history[k].append(v)
            if len(self.param_history[k]) > self.max_history_length:
                self.param_history[k].pop(0)

class MarketStateAnalyzer:
    """市场状态分析器（通用）"""
    def __init__(self):
        self.state_weights = {"volatility": 0.3, "trend": 0.3, "volume": 0.2, "momentum": 0.2}
        self.state_thresholds = {"high_volatility": 0.8, "low_volatility": 0.2, "trending": 0.6, "choppy": 0.4}

    def analyze_market_state(self, indicators: dict) -> str:
        v = indicators.get("volatility", 0)
        t = indicators.get("trend_strength", 0)
        vol = indicators.get("volume", 0)
        m = indicators.get("momentum", 0)
        score = v * self.state_weights["volatility"] + t * self.state_weights["trend"] + vol * self.state_weights["volume"] + m * self.state_weights["momentum"]
        if v > self.state_thresholds["high_volatility"]:
            return "high_volatility"
        elif v < self.state_thresholds["low_volatility"]:
            return "low_volatility"
        elif t > self.state_thresholds["trending"] and vol > 0.5:
            return "trending"
        elif t < self.state_thresholds["choppy"] and vol < 0.3:
            return "choppy"
        else:
            return "normal"

class SignalGenerator:
    """信号生成器（通用）"""
    def __init__(self):
        self.signal_weights = {"trend": 0.4, "rsi": 0.3, "volume": 0.2, "momentum": 0.1}
        self.base_threshold = 0.7

    def generate_signal(self, indicators: dict, market_state: str) -> dict:
        trend_score = indicators.get("trend_strength", 0)
        rsi_score = indicators.get("rsi", 0)
        volume_score = indicators.get("volume", 0)
        momentum_score = indicators.get("momentum", 0)
        total_score = (self.signal_weights["trend"] * trend_score +
                       self.signal_weights["rsi"] * rsi_score +
                       self.signal_weights["volume"] * volume_score +
                       self.signal_weights["momentum"] * momentum_score)
        threshold = self.base_threshold
        return {"score": total_score, "threshold": threshold, "is_valid": total_score >= threshold}

class RiskManager:
    """风险管理器（通用）"""
    def __init__(self):
        self.risk_levels = {
            "conservative": {"max_positions": 3, "position_size": 0.1, "max_drawdown": 0.05},
            "moderate": {"max_positions": 5, "position_size": 0.15, "max_drawdown": 0.08},
            "aggressive": {"max_positions": 8, "position_size": 0.2, "max_drawdown": 0.12}
        }
        self.current_risk_level = "moderate"
        self.account_equity = 0
        self.max_equity = 0
        self.drawdown = 0

    def update_account_status(self, equity: float) -> None:
        self.account_equity = equity
        self.max_equity = max(self.max_equity, equity)
        self.drawdown = (self.max_equity - equity) / self.max_equity if self.max_equity > 0 else 0
        self._adjust_risk_level()

    def _adjust_risk_level(self) -> None:
        max_drawdown = self.risk_levels[self.current_risk_level]["max_drawdown"]
        if self.drawdown > max_drawdown * 1.2:
            if self.current_risk_level == "aggressive":
                self.current_risk_level = "moderate"
            elif self.current_risk_level == "moderate":
                self.current_risk_level = "conservative"
        elif self.drawdown < max_drawdown * 0.5:
            if self.current_risk_level == "conservative" and self.account_equity > self.max_equity * 0.95:
                self.current_risk_level = "moderate"
            elif self.current_risk_level == "moderate" and self.account_equity > self.max_equity * 0.98:
                self.current_risk_level = "aggressive"

    def get_position_size(self, price: float) -> float:
        risk_params = self.risk_levels[self.current_risk_level]
        return self.account_equity * risk_params["position_size"] / price if price > 0 else 0
