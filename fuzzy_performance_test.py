"""
模糊理论交易策略性能测试脚本
测试各个模块的性能和准确性
"""

import time
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import asyncio
import threading
import psutil
import gc

class FuzzyPerformanceTest:
    """模糊策略性能测试类"""
    
    def __init__(self):
        self.test_results = {}
        self.performance_metrics = {}
        
    def measure_execution_time(self, func, *args, **kwargs):
        """测量函数执行时间"""
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        execution_time = end_time - start_time
        return result, execution_time
    
    def measure_memory_usage(self, func, *args, **kwargs):
        """测量内存使用情况"""
        process = psutil.Process()
        
        # 垃圾回收
        gc.collect()
        
        # 测量前的内存
        memory_before = process.memory_info().rss / 1024 / 1024  # MB
        
        # 执行函数
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        
        # 测量后的内存
        memory_after = process.memory_info().rss / 1024 / 1024  # MB
        memory_used = memory_after - memory_before
        
        return result, end_time - start_time, memory_used
    
    def test_fuzzy_neural_network_performance(self):
        """测试模糊神经网络性能"""
        print("=== 模糊神经网络性能测试 ===")
        
        try:
            # 生成测试数据
            input_sizes = [3, 5, 10, 20]
            hidden_sizes_options = [[5], [10, 5], [20, 10, 5]]
            sample_sizes = [100, 500, 1000, 2000]
            
            results = {}
            
            for input_size in input_sizes:
                for hidden_sizes in hidden_sizes_options:
                    for sample_size in sample_sizes:
                        test_key = f"input_{input_size}_hidden_{hidden_sizes}_samples_{sample_size}"
                        
                        # 生成测试数据
                        X = np.random.rand(sample_size, input_size)
                        y = np.random.rand(sample_size, 3)
                        
                        # 模拟FNN训练
                        def mock_fnn_training():
                            # 模拟训练过程
                            for epoch in range(10):
                                # 模拟前向传播
                                hidden = np.tanh(X @ np.random.rand(input_size, hidden_sizes[0]))
                                for i in range(1, len(hidden_sizes)):
                                    hidden = np.tanh(hidden @ np.random.rand(hidden_sizes[i-1], hidden_sizes[i]))
                                output = hidden @ np.random.rand(hidden_sizes[-1], 3)
                                
                                # 模拟反向传播
                                loss = np.mean((output - y) ** 2)
                            
                            return output
                        
                        # 测量性能
                        result, exec_time, memory_used = self.measure_memory_usage(mock_fnn_training)
                        
                        results[test_key] = {
                            'execution_time': exec_time,
                            'memory_usage': memory_used,
                            'samples_per_second': sample_size / exec_time if exec_time > 0 else 0
                        }
                        
                        print(f"{test_key}: {exec_time:.3f}s, {memory_used:.2f}MB")
            
            self.test_results['fuzzy_neural_network'] = results
            
        except Exception as e:
            print(f"模糊神经网络性能测试失败: {e}")
    
    def test_fuzzy_clustering_performance(self):
        """测试模糊聚类性能"""
        print("\n=== 模糊聚类性能测试 ===")
        
        try:
            data_sizes = [100, 500, 1000, 2000]
            feature_dims = [2, 4, 8, 16]
            cluster_nums = [3, 5, 8, 10]
            
            results = {}
            
            for data_size in data_sizes:
                for feature_dim in feature_dims:
                    for n_clusters in cluster_nums:
                        test_key = f"data_{data_size}_dim_{feature_dim}_clusters_{n_clusters}"
                        
                        # 生成测试数据
                        data = np.random.rand(data_size, feature_dim)
                        
                        # 模拟FCM聚类
                        def mock_fcm_clustering():
                            # 初始化聚类中心
                            centers = np.random.rand(n_clusters, feature_dim)
                            membership = np.random.rand(data_size, n_clusters)
                            membership = membership / membership.sum(axis=1, keepdims=True)
                            
                            # 迭代优化
                            for iteration in range(50):
                                # 更新聚类中心
                                for i in range(n_clusters):
                                    weights = membership[:, i] ** 2
                                    centers[i] = np.average(data, axis=0, weights=weights)
                                
                                # 更新隶属度
                                distances = np.linalg.norm(data[:, np.newaxis] - centers, axis=2)
                                membership = 1 / (distances ** (2/(2-1)))
                                membership = membership / membership.sum(axis=1, keepdims=True)
                            
                            return centers, membership
                        
                        # 测量性能
                        result, exec_time, memory_used = self.measure_memory_usage(mock_fcm_clustering)
                        
                        results[test_key] = {
                            'execution_time': exec_time,
                            'memory_usage': memory_used,
                            'data_points_per_second': data_size / exec_time if exec_time > 0 else 0
                        }
                        
                        print(f"{test_key}: {exec_time:.3f}s, {memory_used:.2f}MB")
            
            self.test_results['fuzzy_clustering'] = results
            
        except Exception as e:
            print(f"模糊聚类性能测试失败: {e}")
    
    def test_time_series_prediction_performance(self):
        """测试时间序列预测性能"""
        print("\n=== 时间序列预测性能测试 ===")
        
        try:
            series_lengths = [100, 500, 1000, 2000]
            window_sizes = [10, 20, 50]
            prediction_steps = [1, 3, 5, 10]
            
            results = {}
            
            for series_length in series_lengths:
                for window_size in window_sizes:
                    for pred_steps in prediction_steps:
                        if window_size >= series_length:
                            continue
                            
                        test_key = f"length_{series_length}_window_{window_size}_steps_{pred_steps}"
                        
                        # 生成时间序列数据
                        time_series = np.cumsum(np.random.randn(series_length)) + 100
                        
                        # 模拟模糊时间序列预测
                        def mock_fuzzy_ts_prediction():
                            # 创建模式
                            patterns = []
                            for i in range(series_length - window_size):
                                pattern = time_series[i:i+window_size]
                                patterns.append(pattern)
                            
                            # 模糊化
                            linguistic_values = ["VL", "L", "M", "H", "VH"]
                            fuzzy_patterns = []
                            
                            for pattern in patterns:
                                fuzzy_pattern = []
                                for value in pattern:
                                    # 简单的模糊化
                                    normalized = (value - np.min(time_series)) / (np.max(time_series) - np.min(time_series))
                                    fuzzy_value = int(normalized * (len(linguistic_values) - 1))
                                    fuzzy_pattern.append(linguistic_values[fuzzy_value])
                                fuzzy_patterns.append(tuple(fuzzy_pattern))
                            
                            # 预测
                            predictions = []
                            for step in range(pred_steps):
                                # 简单的基于相似性的预测
                                last_pattern = fuzzy_patterns[-1]
                                similar_patterns = [p for p in fuzzy_patterns if p == last_pattern]
                                if similar_patterns:
                                    pred_value = np.mean([time_series[i+window_size] for i, p in enumerate(fuzzy_patterns) if p == last_pattern])
                                else:
                                    pred_value = time_series[-1]
                                predictions.append(pred_value)
                            
                            return predictions
                        
                        # 测量性能
                        result, exec_time, memory_used = self.measure_memory_usage(mock_fuzzy_ts_prediction)
                        
                        results[test_key] = {
                            'execution_time': exec_time,
                            'memory_usage': memory_used,
                            'predictions_per_second': pred_steps / exec_time if exec_time > 0 else 0
                        }
                        
                        print(f"{test_key}: {exec_time:.3f}s, {memory_used:.2f}MB")
            
            self.test_results['time_series_prediction'] = results
            
        except Exception as e:
            print(f"时间序列预测性能测试失败: {e}")
    
    async def test_async_processing_performance(self):
        """测试异步处理性能"""
        print("\n=== 异步处理性能测试 ===")
        
        try:
            # 模拟异步模糊处理器
            class MockAsyncProcessor:
                def __init__(self, max_workers=4):
                    self.max_workers = max_workers
                    self.processing_times = []
                
                async def submit_decision_request(self, fuzzy_system, input_data, priority=1):
                    # 模拟异步处理
                    await asyncio.sleep(0.01)  # 模拟处理时间
                    return {'decision': ('RiskMedium', 'Normal', 0.75)}
                
                async def submit_batch_request(self, fuzzy_system, input_batch, priority=1):
                    # 模拟批量处理
                    tasks = []
                    for input_data in input_batch:
                        task = self.submit_decision_request(fuzzy_system, input_data, priority)
                        tasks.append(task)
                    
                    results = await asyncio.gather(*tasks)
                    return {'decisions': [r['decision'] for r in results]}
            
            # 测试不同的并发级别
            concurrency_levels = [1, 5, 10, 20, 50]
            batch_sizes = [1, 5, 10, 20]
            
            results = {}
            
            for concurrency in concurrency_levels:
                for batch_size in batch_sizes:
                    test_key = f"concurrency_{concurrency}_batch_{batch_size}"
                    
                    async def async_performance_test():
                        processor = MockAsyncProcessor(max_workers=4)
                        
                        # 创建模拟任务
                        tasks = []
                        for i in range(concurrency):
                            if batch_size == 1:
                                task = processor.submit_decision_request(None, None)
                            else:
                                batch_input = [None] * batch_size
                                task = processor.submit_batch_request(None, batch_input)
                            tasks.append(task)
                        
                        # 执行所有任务
                        start_time = time.time()
                        await asyncio.gather(*tasks)
                        end_time = time.time()
                        
                        return end_time - start_time
                    
                    # 测量性能
                    exec_time = await async_performance_test()
                    
                    total_requests = concurrency * batch_size
                    throughput = total_requests / exec_time if exec_time > 0 else 0
                    
                    results[test_key] = {
                        'execution_time': exec_time,
                        'total_requests': total_requests,
                        'throughput': throughput
                    }
                    
                    print(f"{test_key}: {exec_time:.3f}s, {throughput:.1f} req/s")
            
            self.test_results['async_processing'] = results
            
        except Exception as e:
            print(f"异步处理性能测试失败: {e}")
    
    def test_defuzzification_performance(self):
        """测试去模糊化性能"""
        print("\n=== 去模糊化性能测试 ===")
        
        try:
            fuzzy_set_sizes = [10, 50, 100, 500, 1000]
            methods = ['centroid', 'area_center', 'maximum', 'mean_of_maximum']
            
            results = {}
            
            for size in fuzzy_set_sizes:
                for method in methods:
                    test_key = f"size_{size}_method_{method}"
                    
                    # 生成模糊集
                    fuzzy_set = np.random.rand(size)
                    universe = np.arange(size)
                    
                    # 模拟去模糊化方法
                    def mock_defuzzification():
                        if method == 'centroid':
                            return np.sum(fuzzy_set * universe) / np.sum(fuzzy_set)
                        elif method == 'area_center':
                            cumsum = np.cumsum(fuzzy_set)
                            total_area = cumsum[-1]
                            half_area = total_area / 2
                            idx = np.argmax(cumsum >= half_area)
                            return universe[idx]
                        elif method == 'maximum':
                            return universe[np.argmax(fuzzy_set)]
                        elif method == 'mean_of_maximum':
                            max_val = np.max(fuzzy_set)
                            max_indices = np.where(fuzzy_set == max_val)[0]
                            return np.mean(universe[max_indices])
                    
                    # 测量性能
                    result, exec_time = self.measure_execution_time(mock_defuzzification)
                    
                    results[test_key] = {
                        'execution_time': exec_time,
                        'result': result,
                        'operations_per_second': 1 / exec_time if exec_time > 0 else 0
                    }
                    
                    print(f"{test_key}: {exec_time:.6f}s")
            
            self.test_results['defuzzification'] = results
            
        except Exception as e:
            print(f"去模糊化性能测试失败: {e}")
    
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "=" * 60)
        print("模糊理论交易策略性能测试报告")
        print("=" * 60)
        
        for module_name, module_results in self.test_results.items():
            print(f"\n{module_name.upper()} 性能统计:")
            print("-" * 40)
            
            if module_results:
                # 计算统计信息
                exec_times = [r.get('execution_time', 0) for r in module_results.values()]
                memory_usages = [r.get('memory_usage', 0) for r in module_results.values() if 'memory_usage' in r]
                
                print(f"测试用例数量: {len(module_results)}")
                print(f"平均执行时间: {np.mean(exec_times):.4f}s")
                print(f"最大执行时间: {np.max(exec_times):.4f}s")
                print(f"最小执行时间: {np.min(exec_times):.4f}s")
                
                if memory_usages:
                    print(f"平均内存使用: {np.mean(memory_usages):.2f}MB")
                    print(f"最大内存使用: {np.max(memory_usages):.2f}MB")
                
                # 显示最佳和最差性能的测试用例
                best_case = min(module_results.items(), key=lambda x: x[1].get('execution_time', float('inf')))
                worst_case = max(module_results.items(), key=lambda x: x[1].get('execution_time', 0))
                
                print(f"最佳性能: {best_case[0]} ({best_case[1].get('execution_time', 0):.4f}s)")
                print(f"最差性能: {worst_case[0]} ({worst_case[1].get('execution_time', 0):.4f}s)")
            else:
                print("无测试结果")
        
        print("\n" + "=" * 60)
        print("性能测试完成")
    
    def run_all_tests(self):
        """运行所有性能测试"""
        print("开始模糊理论交易策略性能测试...")
        
        # 运行各项测试
        self.test_fuzzy_neural_network_performance()
        self.test_fuzzy_clustering_performance()
        self.test_time_series_prediction_performance()
        self.test_defuzzification_performance()
        
        # 异步测试
        try:
            asyncio.run(self.test_async_processing_performance())
        except Exception as e:
            print(f"异步性能测试失败: {e}")
        
        # 生成报告
        self.generate_performance_report()

if __name__ == "__main__":
    test_suite = FuzzyPerformanceTest()
    test_suite.run_all_tests()
