#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy3 集成测试脚本
将真实的Strategy3策略集成到模拟交易环境中进行测试
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 导入模拟交易测试框架
from trading_simulator import TradingSimulationTest, MultiScenarioTest, StrategyOptimizer

# 尝试导入Strategy3
try:
    from Strategy3 import Strategy3
    STRATEGY3_AVAILABLE = True
    print("✓ Strategy3 导入成功")
except ImportError as e:
    STRATEGY3_AVAILABLE = False
    print(f"✗ Strategy3 导入失败: {e}")
    print("将使用模拟策略进行测试")

class Strategy3Adapter:
    """Strategy3 适配器 - 将Strategy3适配到测试环境"""
    
    def __init__(self):
        self.strategy = None
        self.initialized = False
        self.decision_history = []
        
    def initialize_strategy(self):
        """初始化Strategy3策略"""
        try:
            if not STRATEGY3_AVAILABLE:
                print("Strategy3不可用，使用模拟策略")
                return self._initialize_mock_strategy()
            
            # 创建模拟的参数和状态对象
            self.strategy = self._create_strategy_instance()
            
            # 初始化策略组件
            self._initialize_strategy_components()
            
            self.initialized = True
            print("✓ Strategy3 初始化成功")
            return True
            
        except Exception as e:
            print(f"✗ Strategy3 初始化失败: {e}")
            return self._initialize_mock_strategy()
    
    def _create_strategy_instance(self):
        """创建Strategy3实例"""
        # 模拟BaseStrategy的基本接口
        class MockBaseStrategy:
            def __init__(self):
                self.params_map = self._create_params_map()
                self.state_map = self._create_state_map()
                self.trading = True
                self.long_price = 100.0
                self.short_price = 100.0
                self.signal_price = 100.0
                self.order_id = None
                self.price_history = []
                self.volume_history = []
                self.return_history = []
                self.prediction_accuracy_history = []
                self.performance_metrics = {
                    'total_decisions': 0,
                    'successful_trades': 0,
                    'last_performance_update': datetime.now()
                }
                
            def output(self, message):
                print(f"[Strategy3] {message}")
                
            def get_position(self, instrument_id):
                # 模拟持仓信息
                class Position:
                    def __init__(self):
                        self.volume = 0
                        self.avg_price = 100.0
                return Position()
                
            def send_order(self, **kwargs):
                # 模拟发单，返回订单ID
                return f"ORDER_{datetime.now().strftime('%H%M%S')}"
            
            def _create_params_map(self):
                class ParamsMap:
                    def __init__(self):
                        self.exchange = "MOCK"
                        self.instrument_id = "TEST001"
                        self.kline_style = "M1"
                        self.tech_type = "STC_HULL"
                        self.trade_direction = "auto"
                        self.price_type = "D1"
                        self.order_volume = 1
                        self.trail_profit_start = 0.03
                        self.trail_profit_stop = 0.015
                        self.quick_stop_loss = 0.02
                        self.stability_margin = 0.7
                        self.N1 = 20
                        self.P1 = 10
                return ParamsMap()
            
            def _create_state_map(self):
                class StateMap:
                    def __init__(self):
                        self.fuzzy_risk = "RiskMedium"
                        self.fuzzy_action = "Normal"
                        self.fuzzy_confidence = 0.5
                        self.ml_prediction = "hold"
                        self.ml_confidence = 0.5
                        self.system_stability = 0.7
                        self.volatility_index = 0.5
                        self.last_action = "hold"
                        self.position_cost = 0.0
                        self.filtered_price = 100.0
                return StateMap()
        
        # 创建Strategy3实例
        if STRATEGY3_AVAILABLE:
            # 使用真实的Strategy3
            strategy = Strategy3()
            # 设置必要的属性
            for attr_name, attr_value in MockBaseStrategy().__dict__.items():
                if not hasattr(strategy, attr_name):
                    setattr(strategy, attr_name, attr_value)
        else:
            # 使用模拟策略
            strategy = MockBaseStrategy()
            
        return strategy
    
    def _initialize_strategy_components(self):
        """初始化策略组件"""
        try:
            # 如果是真实的Strategy3，调用其初始化方法
            if hasattr(self.strategy, '_initialize_core_components'):
                self.strategy._initialize_core_components()
            
            if hasattr(self.strategy, '_initialize_fuzzy_modules'):
                self.strategy._initialize_fuzzy_modules()
                
            if hasattr(self.strategy, '_initialize_ml_modules'):
                self.strategy._initialize_ml_modules()
                
            print("✓ Strategy3 组件初始化完成")
            
        except Exception as e:
            print(f"⚠ 组件初始化部分失败: {e}")
            # 继续执行，使用基础功能
    
    def _initialize_mock_strategy(self):
        """初始化模拟策略"""
        self.strategy = self._create_strategy_instance()
        self.initialized = True
        print("✓ 模拟策略初始化成功")
        return True
    
    def process_market_data(self, market_data):
        """处理市场数据并生成交易决策"""
        try:
            if not self.initialized or not self.strategy:
                return None
            
            # 更新策略的市场数据
            self._update_strategy_data(market_data)
            
            # 生成交易决策
            decision = self._generate_trading_decision(market_data)
            
            # 记录决策历史
            self.decision_history.append({
                'timestamp': datetime.now(),
                'decision': decision,
                'market_data': {
                    'price': market_data['price'].iloc[-1],
                    'volume': market_data['volume'].iloc[-1],
                    'volatility': market_data['price'].pct_change().std()
                }
            })
            
            return decision
            
        except Exception as e:
            print(f"市场数据处理失败: {e}")
            return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
    
    def _update_strategy_data(self, market_data):
        """更新策略的市场数据"""
        # 更新价格历史
        self.strategy.price_history = market_data['price'].tolist()
        self.strategy.volume_history = market_data['volume'].tolist()
        
        # 更新当前价格
        current_price = market_data['price'].iloc[-1]
        self.strategy.long_price = current_price
        self.strategy.short_price = current_price
        self.strategy.signal_price = current_price
        
        # 更新状态信息
        if hasattr(self.strategy.state_map, 'filtered_price'):
            self.strategy.state_map.filtered_price = current_price
    
    def _generate_trading_decision(self, market_data):
        """生成交易决策"""
        try:
            # 如果有真实的Strategy3决策方法，使用它
            if hasattr(self.strategy, 'control_center') and hasattr(self.strategy.control_center, 'make_decision'):
                # 构造输入数据
                processed_data = self._prepare_decision_input(market_data)
                
                # 调用Strategy3的决策方法
                fuzzy_decision = self.strategy.control_center.make_decision(
                    processed_data.get('stability', 0.5),
                    processed_data.get('volatility', 0.5),
                    processed_data.get('profit', 0.0)
                )
                
                # 转换为标准格式
                return self._convert_fuzzy_decision(fuzzy_decision)
            
            else:
                # 使用简化的决策逻辑
                return self._simple_decision_logic(market_data)
                
        except Exception as e:
            print(f"决策生成失败: {e}")
            return self._simple_decision_logic(market_data)
    
    def _prepare_decision_input(self, market_data):
        """准备决策输入数据"""
        prices = market_data['price'].values
        volumes = market_data['volume'].values
        
        # 计算稳定性指标
        if len(prices) >= 20:
            price_std = np.std(prices[-20:])
            price_mean = np.mean(prices[-20:])
            stability = max(0, 1 - (price_std / price_mean)) if price_mean > 0 else 0.5
        else:
            stability = 0.5
        
        # 计算波动率
        returns = np.diff(prices) / prices[:-1]
        volatility = np.std(returns) if len(returns) > 0 else 0.02
        
        # 计算收益率
        if len(prices) >= 2:
            profit = (prices[-1] - prices[-2]) / prices[-2]
        else:
            profit = 0.0
        
        return {
            'stability': stability,
            'volatility': volatility,
            'profit': profit,
            'price_trend': 1 if len(prices) >= 2 and prices[-1] > prices[-2] else -1
        }
    
    def _convert_fuzzy_decision(self, fuzzy_decision):
        """转换模糊决策为标准格式"""
        try:
            if isinstance(fuzzy_decision, tuple) and len(fuzzy_decision) >= 3:
                risk_level, action_level, confidence = fuzzy_decision[:3]
                
                # 转换为交易动作
                if action_level in ['Aggressive', 'Normal'] and risk_level != 'RiskNone':
                    action = 'buy' if np.random.random() > 0.5 else 'sell'  # 简化处理
                    volume = 1
                else:
                    action = 'hold'
                    volume = 0
                
                return {
                    'action': action,
                    'confidence': float(confidence),
                    'volume': volume,
                    'risk_level': str(risk_level),
                    'action_level': str(action_level)
                }
            else:
                return self._simple_decision_logic(None)
                
        except Exception as e:
            print(f"决策转换失败: {e}")
            return self._simple_decision_logic(None)
    
    def _simple_decision_logic(self, market_data):
        """简化的决策逻辑"""
        if market_data is None:
            return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
        
        try:
            prices = market_data['price'].values
            if len(prices) < 10:
                return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
            
            # 简单的移动平均策略
            short_ma = np.mean(prices[-5:])
            long_ma = np.mean(prices[-10:])
            current_price = prices[-1]
            
            # 计算信号强度
            signal_strength = abs(short_ma - long_ma) / long_ma
            
            if short_ma > long_ma * 1.001 and signal_strength > 0.005:
                return {'action': 'buy', 'confidence': min(0.8, signal_strength * 100), 'volume': 1}
            elif short_ma < long_ma * 0.999 and signal_strength > 0.005:
                return {'action': 'sell', 'confidence': min(0.8, signal_strength * 100), 'volume': 1}
            else:
                return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
                
        except Exception as e:
            print(f"简化决策逻辑失败: {e}")
            return {'action': 'hold', 'confidence': 0.5, 'volume': 0}
    
    def get_strategy_status(self):
        """获取策略状态"""
        if not self.initialized:
            return "未初始化"
        
        status = {
            'initialized': self.initialized,
            'strategy_type': 'Strategy3' if STRATEGY3_AVAILABLE else 'Mock',
            'decision_count': len(self.decision_history),
            'last_decision': self.decision_history[-1] if self.decision_history else None
        }
        
        return status

# 集成测试类
class Strategy3IntegrationTest(TradingSimulationTest):
    """Strategy3 集成测试类"""

    def __init__(self, initial_capital=100000):
        super().__init__(initial_capital)
        # 替换策略适配器
        self.strategy_adapter = Strategy3Adapter()

    def run_comprehensive_test(self, num_ticks=5000):
        """运行综合测试"""
        print("=" * 60)
        print("Strategy3 综合集成测试")
        print("=" * 60)

        # 运行基础测试
        results = self.run_simulation(num_ticks, save_results=True)

        if results:
            # 策略特定分析
            self._analyze_strategy_performance()

            # 生成详细报告
            self._generate_detailed_report(results)

            return results

        return None

    def _analyze_strategy_performance(self):
        """分析策略特定性能"""
        print("\n" + "=" * 40)
        print("Strategy3 特定性能分析")
        print("=" * 40)

        # 分析决策历史
        decision_history = self.strategy_adapter.decision_history
        if decision_history:
            actions = [d['decision']['action'] for d in decision_history]
            confidences = [d['decision']['confidence'] for d in decision_history]

            print(f"总决策次数: {len(decision_history)}")
            print(f"买入信号: {actions.count('buy')}")
            print(f"卖出信号: {actions.count('sell')}")
            print(f"持有信号: {actions.count('hold')}")
            print(f"平均置信度: {np.mean(confidences):.2f}")
            print(f"置信度标准差: {np.std(confidences):.2f}")

        # 策略状态
        status = self.strategy_adapter.get_strategy_status()
        print(f"\n策略状态: {status}")

    def _generate_detailed_report(self, results):
        """生成详细报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_filename = f"strategy3_detailed_report_{timestamp}.txt"

        with open(report_filename, 'w', encoding='utf-8') as f:
            f.write("Strategy3 详细测试报告\n")
            f.write("=" * 50 + "\n\n")

            # 基础性能指标
            f.write("基础性能指标:\n")
            f.write("-" * 20 + "\n")
            metrics = results['performance_metrics']
            for key, value in metrics.items():
                f.write(f"{key}: {value}\n")

            # 决策分析
            f.write("\n决策分析:\n")
            f.write("-" * 20 + "\n")
            decision_history = self.strategy_adapter.decision_history
            if decision_history:
                f.write(f"总决策次数: {len(decision_history)}\n")

                # 决策分布
                actions = [d['decision']['action'] for d in decision_history]
                for action in ['buy', 'sell', 'hold']:
                    count = actions.count(action)
                    percentage = count / len(actions) * 100
                    f.write(f"{action}: {count} ({percentage:.1f}%)\n")

                # 置信度分析
                confidences = [d['decision']['confidence'] for d in decision_history]
                f.write(f"平均置信度: {np.mean(confidences):.3f}\n")
                f.write(f"置信度范围: {np.min(confidences):.3f} - {np.max(confidences):.3f}\n")

            # 风险分析
            f.write("\n风险分析:\n")
            f.write("-" * 20 + "\n")
            f.write(f"最大回撤: {metrics.get('max_drawdown', 0):.2%}\n")
            f.write(f"波动率: {metrics.get('volatility', 0):.2%}\n")
            f.write(f"夏普比率: {metrics.get('sharpe_ratio', 0):.2f}\n")

            # 优化建议
            optimizer = StrategyOptimizer(results)
            suggestions = optimizer.generate_optimization_suggestions()
            f.write("\n优化建议:\n")
            f.write("-" * 20 + "\n")
            for suggestion in suggestions:
                f.write(f"{suggestion}\n")

        print(f"详细报告已保存到: {report_filename}")

# 压力测试
class Strategy3StressTest:
    """Strategy3 压力测试"""

    def __init__(self):
        self.stress_scenarios = [
            {'name': '极端牛市', 'trend': 0.002, 'volatility': 0.05, 'shock_prob': 0.02},
            {'name': '极端熊市', 'trend': -0.002, 'volatility': 0.05, 'shock_prob': 0.02},
            {'name': '高频震荡', 'trend': 0.0, 'volatility': 0.08, 'shock_prob': 0.05},
            {'name': '黑天鹅事件', 'trend': 0.0001, 'volatility': 0.02, 'shock_prob': 0.1},
            {'name': '流动性危机', 'trend': -0.001, 'volatility': 0.15, 'shock_prob': 0.08}
        ]

    def run_stress_tests(self, num_ticks=2000):
        """运行压力测试"""
        print("=" * 60)
        print("Strategy3 压力测试")
        print("=" * 60)

        stress_results = {}

        for scenario in self.stress_scenarios:
            print(f"\n压力测试场景: {scenario['name']}")
            print("-" * 40)

            # 创建压力测试实例
            test = Strategy3IntegrationTest()

            # 设置极端市场条件
            test.market_simulator.trend = scenario['trend']
            test.market_simulator.volatility = scenario['volatility']

            # 修改市场数据生成器以包含更多冲击
            original_generate = test.market_simulator.generate_tick_data

            def generate_stress_data(num_ticks=1000):
                data = original_generate(num_ticks)
                # 添加额外的市场冲击
                for i in range(len(data)):
                    if np.random.random() < scenario['shock_prob']:
                        shock_magnitude = np.random.normal(0, scenario['volatility'] * 3)
                        data.loc[i, 'price'] *= (1 + shock_magnitude)
                return data

            test.market_simulator.generate_tick_data = generate_stress_data

            # 运行测试
            result = test.run_simulation(num_ticks, save_results=False)
            stress_results[scenario['name']] = result

        # 分析压力测试结果
        self._analyze_stress_results(stress_results)

        return stress_results

    def _analyze_stress_results(self, stress_results):
        """分析压力测试结果"""
        print("\n" + "=" * 60)
        print("压力测试结果汇总")
        print("=" * 60)

        summary_data = []
        for scenario_name, result in stress_results.items():
            if result:
                metrics = result['performance_metrics']
                summary_data.append({
                    '压力场景': scenario_name,
                    '总收益率': f"{metrics.get('total_return', 0):.2%}",
                    '最大回撤': f"{metrics.get('max_drawdown', 0):.2%}",
                    '夏普比率': f"{metrics.get('sharpe_ratio', 0):.2f}",
                    '胜率': f"{metrics.get('win_rate', 0):.2%}",
                    '交易次数': metrics.get('total_trades', 0),
                    '风险评级': self._assess_risk_level(metrics)
                })

        if summary_data:
            summary_df = pd.DataFrame(summary_data)
            print(summary_df.to_string(index=False))

            # 保存压力测试结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            summary_df.to_csv(f'stress_test_results_{timestamp}.csv', index=False)
            print(f"\n压力测试结果已保存到 stress_test_results_{timestamp}.csv")

    def _assess_risk_level(self, metrics):
        """评估风险等级"""
        max_drawdown = abs(metrics.get('max_drawdown', 0))
        sharpe_ratio = metrics.get('sharpe_ratio', 0)

        if max_drawdown > 0.3 or sharpe_ratio < -0.5:
            return "高风险"
        elif max_drawdown > 0.15 or sharpe_ratio < 0.5:
            return "中风险"
        else:
            return "低风险"

# 主执行函数
def main():
    """主执行函数"""
    print("Strategy3 集成测试系统启动")
    print("=" * 60)

    try:
        # 1. 基础集成测试
        print("1. 执行基础集成测试...")
        integration_test = Strategy3IntegrationTest(initial_capital=100000)
        results = integration_test.run_comprehensive_test(num_ticks=3000)

        if results:
            # 绘制结果
            integration_test.plot_results()

        # 2. 多场景测试
        print("\n2. 执行多场景测试...")
        multi_test = MultiScenarioTest()
        # 替换为Strategy3适配器
        for scenario in multi_test.scenarios:
            test = Strategy3IntegrationTest()
            test.market_simulator.trend = scenario['trend']
            test.market_simulator.volatility = scenario['volatility']
            result = test.run_simulation(2000, save_results=False)
            multi_test.results[scenario['name']] = result

        multi_test._analyze_scenario_results()

        # 3. 压力测试
        print("\n3. 执行压力测试...")
        stress_test = Strategy3StressTest()
        stress_results = stress_test.run_stress_tests(num_ticks=1500)

        print("\n所有测试完成！")
        print("请查看生成的报告文件获取详细分析结果。")

    except Exception as e:
        print(f"测试执行失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
