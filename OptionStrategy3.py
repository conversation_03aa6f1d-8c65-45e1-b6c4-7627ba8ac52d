from typing import Literal
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import K<PERSON><PERSON>Data, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

#----------------------------------------
# 1. 配置管理模块
#----------------------------------------
class TradingConfig:
    """交易配置管理"""
    # 趋势判断配置
    TREND_PERIOD = 30  # 分析周期
    TREND_UPDATE_INTERVAL = 15 * 60  # 更新间隔(秒)
    TREND_THRESHOLD = 0.65  # 趋势判断阈值
    VOLATILITY_THRESHOLD = 0.4  # 波动率阈值
    MIN_TREND_DURATION = 3  # 最小趋势持续周期
    
    # 参数组合配置
    PARAM_SETS = {
        "A": {  # 趋势型
            "ema": [5, 15, 30],
            "atr_period": 14,
            "stop_mult": 2.2,
            "profit_mult": 3.0,
            "trail_step": 1.0
        },
        "B": {  # 震荡型
            "ema": [3, 10, 20],
            "atr_period": 21,
            "stop_mult": 1.8,
            "profit_mult": 2.5,
            "trail_step": 0.5
        }
    }

#----------------------------------------
# 2. 参数和状态定义
#----------------------------------------
class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0.0, title="快线EMA")
    ema_mid: float = Field(default=0.0, title="中线EMA")
    ema_slow: float = Field(default=0.0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0.0, title="ATR")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0.0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0.0, title="波动率")
    trend_reversal: bool = Field(default=False, title="趋势反转")
    trend_reversal_warning: bool = Field(default=False, title="趋势反转预警")
    trend_reversal_time: str = Field(default="", title="趋势反转时间")
    
    # 止损止盈
    stop_loss: float = Field(default=0.0, title="止损价")
    take_profit: float = Field(default=0.0, title="止盈价")
    trailing_stop: float = Field(default=0.0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0.0, title="最高价")
    lowest_price: float = Field(default=0.0, title="最低价")
    current_profit: float = Field(default=0.0, title="当前盈亏")
    max_profit: float = Field(default=0.0, title="最大盈亏")
    
    # 新增状态字段
    market_state: Literal["Trend", "Range"] = Field(default="Range", title="行情状态")
    last_signal_time: str = Field(default="", title="最近信号时间")
    position_status: str = Field(default="空仓", title="持仓状态")

#----------------------------------------
# 3. 趋势分析模块
#----------------------------------------
class TrendAnalyzer:
    """趋势分析器"""
    def __init__(self):
        self.price_history = []
        self.trend_history = []
        self.volatility_history = []
        self.last_trend_update = 0
        
    def should_update(self, current_time: float) -> bool:
        """检查是否需要更新趋势"""
        return current_time - self.last_trend_update >= TradingConfig.TREND_UPDATE_INTERVAL
        
    def analyze(self, price: float, current_time: float) -> tuple[bool, float, float]:
        """分析趋势状态"""
        if not self.should_update(current_time):
            return False, 0.0, 0.0  # Default to "震荡" state if no update is needed
            
        self._update_price_history(price)
        if len(self.price_history) >= TradingConfig.TREND_PERIOD:
            direction_consistency, volatility = self._calculate_metrics()
            trend_strength = self._calculate_strength()
            is_trending = self._determine_trend(
                direction_consistency, volatility, trend_strength
            )
            return is_trending, trend_strength, volatility  # Ensure "趋势" or "震荡" is always determined
        return False, 0.0, 0.0  # Default to "震荡" state if insufficient data
        
    def _update_price_history(self, price: float) -> None:
        """更新价格历史"""
        self.price_history.append(price)
        if len(self.price_history) > TradingConfig.TREND_PERIOD:
            self.price_history.pop(0)

    def _calculate_metrics(self) -> tuple[float, float]:
        """计算方向一致性和波动率"""
        if len(self.price_history) < 2:
            return 0.5, 0.0
            
        price_changes = np.diff(self.price_history)
        up_moves = sum(1 for x in price_changes if x > 0)
        down_moves = sum(1 for x in price_changes if x < 0)
        total_moves = len(price_changes)
        
        direction_bias = abs(up_moves - down_moves) / total_moves
        direction_consistency = 0.5 + (direction_bias / 2)
        
        percent_changes = price_changes / self.price_history[:-1]
        volatility = np.std(percent_changes) if len(percent_changes) > 0 else 0.5
        
        return direction_consistency, volatility

    def _calculate_strength(self) -> float:
        """计算趋势强度"""
        if len(self.price_history) < TradingConfig.TREND_PERIOD:
            return 0.5
            
        price_changes = np.diff(self.price_history)
        trend_direction = np.sum(price_changes) / len(price_changes)
        return min(1.0, abs(trend_direction) * 10)

    def _determine_trend(self, direction_consistency: float,
                        volatility: float, trend_strength: float) -> bool:
        """判断趋势状态"""
        trend_score = (
            0.4 * direction_consistency +
            0.3 * trend_strength +
            0.3 * min(1.0, volatility)
        )
        return trend_score >= 0.5  # True for "趋势", False for "震荡"

#----------------------------------------
# 4. 信号生成模块
#----------------------------------------
class SignalGenerator:
    """交易信号生成器"""
    def __init__(self):
        self.buy_signal = False
        self.sell_signal = False
    
    def generate_signals(self, state: State, is_trending: bool) -> tuple[bool, bool]:
        """生成交易信号"""
        if is_trending:
            return self._trending_signals(state)
        return self._ranging_signals(state)
    
    def _trending_signals(self, state: State) -> tuple[bool, bool]:
        """趋势型信号"""
        buy = (
            state.ema_fast > state.ema_mid > state.ema_slow and
            state.trend_strength > 0.4 and
            state.volatility < TradingConfig.VOLATILITY_THRESHOLD
        )
        sell = (
            state.ema_fast < state.ema_mid < state.ema_slow or
            state.trend_strength < 0.2
        )
        return buy, sell
    
    def _ranging_signals(self, state: State) -> tuple[bool, bool]:
        """震荡型信号"""
        buy = (
            state.ema_fast < state.ema_mid and
            state.ema_mid > state.ema_slow and
            state.volatility < TradingConfig.VOLATILITY_THRESHOLD * 1.5
        )
        sell = (
            state.ema_fast > state.ema_mid and
            state.ema_mid < state.ema_slow
        )
        return buy, sell

#----------------------------------------
# 5. 止盈止损管理模块
#----------------------------------------
class StopManager:
    """止盈止损管理器"""
    def __init__(self):
        self.is_trailing = False
        self.entry_price = 0
        
    def update_stops(self, state: State, current_price: float,
                    position_size: int, params: dict) -> None:
        """更新止盈止损价格"""
        # ...将之前的止盈止损管理相关方法移到这里...

#----------------------------------------
# 6. 主策略类
#----------------------------------------
class OptionStrategy3(BaseStrategy):
    """三周期EMA+ATR买方策略 - 优化为符合交易软件规范"""
    """三周期EMA+ATR买方策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.order_id = None
        self.signal_price = 0

    def on_start(self):
        self.callback = self.callback if hasattr(self, 'callback') else self.default_callback
        self.real_time_callback = self.real_time_callback if hasattr(self, 'real_time_callback') else self.default_real_time_callback
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()
        self.update_status_bar()
        super().on_start()
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None

    def default_callback(self, kline: KLineData) -> None:
        """默认 K 线回调"""
        pass

    def default_real_time_callback(self, kline: KLineData) -> None:
        """默认实时 K 线回调"""
        pass

    def on_stop(self):
        """停止策略时更新状态栏"""
        self.state_map.market_state = "Range"  # 默认初始化为震荡状态
        self.update_status_bar()
        super().on_stop()

def on_tick(self, tick: TickData):
    try:
        super().on_tick(tick)
        self.tick = tick
    except Exception as e:
        # 将错误信息更新到技术指标窗口
        if hasattr(self, 'widget') and self.widget is not None:
            self.widget.recv_kline({
                "error": f"Error in on_tick: {e}"
            })
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

def on_trade(self, trade: TradeData, log: bool = False) -> None:
    try:
        """交易状态更新"""
        super().on_trade(trade, log)
        self.order_id = None
    except Exception as e:
        # 将错误信息更新到技术指标窗口
        if hasattr(self, 'widget') and self.widget is not None:
            self.widget.recv_kline({
                "error": f"Error in on_trade: {e}"
            })
        self.update_status_bar()  # 交易后更新状态

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        self.calc_indicator()
        self.calc_signal(kline)
        self.exec_signal()
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_indicator()
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

    def _throttled_status_update(self) -> None:
        """节流控制的状态更新"""
        current_time = time.time()
        if current_time - self._last_status_update >= self._status_update_interval:
            self.update_status_bar()
            self._last_status_update = current_time

    def update_status_bar(self):
        """确保参数映射模型和状态映射模型初始化成功"""
        try:
            if not hasattr(self, 'widget') or self.widget is None:
                return

            status_data = {
                "market_state": self.state_map.market_state,
                "position_status": self.state_map.position_status,
                "last_signal_time": self.state_map.last_signal_time
            }

            status_maps = {
                "参数映射模型": self.params_map.model_dump(),
                "状态映射模型": self.state_map.model_dump()
            }

            if hasattr(self.widget, 'update_status_maps'):
                self.widget.update_status_maps(status_maps)
            elif hasattr(self.widget, 'update_status_bar'):
                self.widget.update_status_bar(status_maps)
            else:
                self.widget.update_maps(status_maps)
        except Exception as e:
            self.logger.error(f"状态栏更新失败: {e}")
        """优化的状态栏更新"""
        try:
            if not hasattr(self, 'widget') or self.widget is None:
                return

            # 简化状态更新逻辑
            position = self.get_position(self.params_map.instrument_id)
            
            # 更新核心状态
            status_data = {
                "strategy_status": "运行中" if self.trading else "已停止",
                "position_status": (
                    f"{'多头' if position and position.net_position > 0 else '空头'} "
                    f"{abs(position.net_position) if position else 0}手"
                ) if position and position.net_position != 0 else "空仓",
                "last_signal_time": (
                    self.kline_generator.last_kline.datetime.strftime("%Y-%m-%d %H:%M:%S")
                    if hasattr(self.kline_generator, 'last_kline') and
                    self.kline_generator.last_kline else ""
                )
            }
            
            # 更新状态映射模型
            for key, value in status_data.items():
                setattr(self.state_map, key, value)
            
            # 创建状态映射集合
            status_maps = {
                "参数映射模型": self.params_map.model_dump(),
                "状态映射模型": self.state_map.model_dump()
            }
            
            # 更新UI状态 - 使用正确的方法名称
            if hasattr(self.widget, 'update_status_maps'):
                self.widget.update_status_maps(status_maps)
            elif hasattr(self.widget, 'update_status_bar'):
                self.widget.update_status_bar(status_maps)
            else:
                self.widget.update_maps(status_maps)  # 尝试通用方法名
            
        except Exception as e:
            # 简化错误处理，尝试所有可能的方法名
            if hasattr(self, 'widget') and self.widget is not None:
                for method_name in ['update_status_maps', 'update_status_bar', 'update_maps']:
                    try:
                        if hasattr(self.widget, method_name):
                            getattr(self.widget, method_name)({
                                "参数映射模型": {},
                                "状态映射模型": {"strategy_status": "更新错误"}
                            })
                            break
                    except:
                        continue

    def _init_state_map(self):
        """初始化状态映射模型"""
        try:
            self.state_map.market_state = "Range"  # 默认初始化为震荡状态
            self.state_map.last_signal_time = ""
            self.state_map.position_status = "空仓"
            self.logger.info("状态映射模型初始化完成")
        except Exception as e:
            self.logger.error(f"状态映射模型初始化失败: {e}")

    # 主要业务逻辑方法
    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        self.normalize_opening_indicators(kline)
        
        # 分析趋势
        is_trending, trend_strength, volatility = self.trend_analyzer.analyze(
            kline.close, time.time()
        )
        
        if is_trending is not None:
            self._update_trend_state(is_trending, trend_strength, volatility, kline)
        
        # 计算指标
        self.calc_indicator()
        
        # 生成信号
        self.buy_signal, self.sell_signal = self.signal_generator.generate_signals(
            self.state_map, self.state_map.is_trending
        )
        
        # 更新止损
        if self.tick:
            self.stop_manager.update_stops(
                self.state_map, self.tick.last_price,
                self.position_size, self.current_params
            )
    
    def normalize_opening_indicators(self, kline: KLineData) -> None:
        """开盘指标正则化"""
        try:
            current_time = kline.datetime
            market_open_time = current_time.replace(hour=9, minute=30, second=0, microsecond=0)

            if (current_time - market_open_time).total_seconds() / 60 <= 5:
                # EMA平滑处理
                self.state_map.ema_fast = (self.state_map.ema_fast + self.state_map.ema_mid) / 2
                self.state_map.ema_mid = (self.state_map.ema_mid + self.state_map.ema_slow) / 2
                self.state_map.ema_slow = (self.state_map.ema_slow + self.state_map.ema_fast) / 2
                
                # ATR限制
                self.state_map.atr = min(self.state_map.atr, self.state_map.atr * 1.5)
                
                # 趋势指标调整
                self.state_map.trend_strength = max(0.2, self.state_map.trend_strength * 0.8)
                self.state_map.volatility = min(
                    self.state_map.volatility, 
                    TradingConfig.VOLATILITY_THRESHOLD * 0.8
                )
        except Exception as e:
            # 将错误信息更新到技术指标窗口
            if hasattr(self, 'widget') and self.widget is not None:
                self.widget.recv_kline({
                    "error": f"开盘指标正则化失败: {e}"
                })
    
    def calc_indicator(self) -> None:
        """计算技术指标"""
        try:
            if self.current_params is None:
                self.current_params = TradingConfig.PARAM_SETS["A"]
                
            # 计算EMA
            ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
            ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
            ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
            
            self.state_map.ema_fast = round(ema_fast[-1], 2) if len(ema_fast) > 0 else 0.0
            self.state_map.ema_mid = round(ema_mid[-1], 2) if len(ema_mid) > 0 else 0.0
            self.state_map.ema_slow = round(ema_slow[-1], 2) if len(ema_slow) > 0 else 0.0
            
            # 计算ATR
            atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
            self.state_map.atr = round(atr, 2) if atr else 0.0
            
            # 更新止盈止损
            if self.tick:
                current_price = self.tick.last_price
                if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                    self.state_map.stop_loss = self.params_map.fixed_stop_loss
                    self.state_map.take_profit = self.params_map.fixed_take_profit
                else:
                    self.state_map.stop_loss = self._round_to_nearest_tick(
                        current_price - self.state_map.atr * self.current_params["stop_mult"]
                    )
                    self.state_map.take_profit = self._round_to_nearest_tick(
                        current_price + self.state_map.atr * self.current_params["profit_mult"]
                    )
                
                self.state_map.trailing_stop = self._round_to_nearest_tick(
                    current_price - self.state_map.atr * self.current_params["trail_step"]
                )
        except Exception as e:
            # 将错误信息更新到技术指标窗口
            if hasattr(self, 'widget') and self.widget is not None:
                self.widget.recv_kline({
                    "error": f"计算技术指标失败: {e}"
                })
            raise

    def exec_signal(self):
        """交易信号执行"""
        try:
            if self.tick is None:
                self.logger.warning("Tick数据为空，跳过信号执行")
                return

            self.signal_price = 0
            position = self.get_position(self.params_map.instrument_id)
            self.position_size = position.net_position if position else 0

            # 撤销未完成订单
            if self.order_id is not None:
                self.cancel_order(self.order_id)

            # 检查持仓限制和止损条件
            if position:
                if position.net_position >= self.params_map.max_positions:
                    self.buy_signal = False
                
                if (position.net_position > 0 and 
                    self.tick.last_price <= self.state_map.stop_loss):
                    self.sell_signal = True

            # 执行卖出信号
            if position and position.net_position > 0 and self.sell_signal:
                self.signal_price = -self.tick.bid_price1
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=self.tick.bid_price1,
                        volume=position.net_position,
                        order_direction="sell"
                    )
                    self._reset_dynamic_stop_variables()

            # 执行买入信号
            if self.buy_signal:
                self.signal_price = self.tick.ask_price1
                if self.trading:
                    self.order_id = self.send_order(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        volume=self.params_map.order_volume,
                        price=self.tick.ask_price1,
                        order_direction="buy"
                    )
                    self._initialize_entry_variables()
                    
        except Exception as e:
            # 将错误信息更新到技术指标窗口
            if hasattr(self, 'widget') and self.widget is not None:
                self.widget.recv_kline({
                    "error": f"信号执行失败: {e}"
                })

    def _reset_dynamic_stop_variables(self):
        """重置动态止盈止损变量"""
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

    def _round_to_nearest_tick(self, price: float) -> float:
        """将价格四舍五入到最接近的0.50或0.00"""
        return round(price * 2) / 2

    def _initialize_entry_variables(self):
        """初始化入场变量"""
        self.entry_price = self.tick.ask_price1
        self.state_map.highest_price = self.tick.ask_price1
        self.state_map.lowest_price = self.tick.ask_price1

    def _update_trend_state(self, is_trending: bool, trend_strength: float, 
                           volatility: float, kline: KLineData) -> None:
        """更新趋势状态和相关指标"""
        try:
            # 更新基础趋势指标
            self.state_map.market_state = "Trend" if is_trending else "Range"
            self.state_map.trend_type = "A" if is_trending else "B"
            self.state_map.trend_strength = trend_strength
            self.state_map.volatility = volatility

            # 检测趋势反转
            trend_changed = False
            if self.trend_analyzer.trend_history:
                last_trend = self.trend_analyzer.trend_history[-1]
                trend_changed = last_trend != is_trending

            # 更新趋势反转状态
            if trend_changed:
                self.state_map.trend_reversal = True
                self.state_map.trend_reversal_time = kline.datetime.strftime("%Y-%m-%d %H:%M:%S")
                # 更新当前参数组
                self.current_params = TradingConfig.PARAM_SETS[self.state_map.trend_type]

                # 更新状态栏以反映趋势反转
                if hasattr(self, 'widget') and self.widget is not None:
                    self.widget.recv_kline({
                        "trend_update": {
                            "trend_type": "趋势型" if is_trending else "震荡型",
                            "trend_strength": round(trend_strength, 2),
                            "volatility": round(volatility, 2),
                            "reversal_time": kline.datetime.strftime("%Y-%m-%d %H:%M:%S")
                        }
                    })
                self.logger.info(
                    f"趋势反转: {'趋势型' if is_trending else '震荡型'} "
                    f"强度: {trend_strength:.2f} 波动: {volatility:.2f}"
                )
            else:
                self.state_map.trend_reversal = False

            # 更新趋势持续时间
            if is_trending:
                self.state_map.trend_duration += 1
            else:
                self.state_map.trend_duration = 0

            # 存储趋势历史
            self.trend_analyzer.trend_history.append(is_trending)
            if len(self.trend_analyzer.trend_history) > 20:
                self.trend_analyzer.trend_history.pop(0)

            # 更新趋势分析器时间戳
            self.trend_analyzer.last_trend_update = time.time()

        except Exception as e:
            # 将错误信息更新到技术指标窗口
            if hasattr(self, 'widget') and self.widget is not None:
                self.widget.recv_kline({
                    "error": f"更新趋势状态失败: {e}"
                })
            raise

    # ...其他现有方法...
