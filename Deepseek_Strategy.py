# -*- coding: utf-8 -*-
from typing import Literal, Dict, Any
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

class Params(BaseParams):
    """参数映射模型（无限易Pro界面可配置参数）"""
    exchange: str = Field(default="", title="交易所代码", description="需与无限易实时行情窗口显示的交易所代码完全一致")
    instrument_id: str = Field(default="", title="合约代码", description="需与无限易实时行情窗口显示的合约代码完全一致")
    kline_style: str = Field(default="M1", title="K线周期", description="支持TICK/S1/M1/M5/M15等周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式", description="手动模式使用固定参数，自动模式根据行情动态调整")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位", description="D1-盘口一档，D2-盘口二档")
    order_volume: int = Field(default=1, title="报单数量", ge=1, le=100, description="单次委托手数")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10, description="同一合约最大持仓手数")
    
    # ATR参数组
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21, description="ATR计算周期")
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5, description="ATR止损倍数")
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5, description="ATR止盈倍数")
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5, description="追踪止损ATR倍数")
    
    # 固定止盈止损参数组
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0, description="手动模式固定止损价格")
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0, description="手动模式固定止盈价格")
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损", description="启用后将忽略ATR动态止损")
    
    # 指标参数组
    stc_length: int = Field(default=10, title="STC周期长度", ge=5, le=20, description="Schaff趋势周期长度")
    stc_fast_length: int = Field(default=23, title="STC快速长度", ge=10, le=30, description="STC快速EMA周期")
    stc_slow_length: int = Field(default=50, title="STC慢速长度", ge=30, le=60, description="STC慢速EMA周期")
    hull_period: int = Field(default=9, title="HULL均线周期", ge=5, le=20, description="Hull移动平均线周期")


class State(BaseState):
    """状态映射模型（策略运行时状态变量）"""
    # 指标状态
    ut_signal: bool = Field(default=False, title="UT指标信号")
    stc_value: float = Field(default=0, title="STC指标值")
    stc_trend: Literal["up", "down", "neutral"] = Field(default="neutral", title="STC趋势方向")
    hull_ma: float = Field(default=0, title="HULL均线值")
    atr: float = Field(default=0, title="ATR值")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式", description="A-趋势型，B-震荡型")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈状态
    stop_loss: float = Field(default=0, title="动态止损价")
    take_profit: float = Field(default=0, title="动态止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损状态
    highest_price: float = Field(default=0, title="持仓最高价")
    lowest_price: float = Field(default=0, title="持仓最低价")
    current_profit: float = Field(default=0, title="当前浮动盈亏")
    max_profit: float = Field(default=0, title="最大浮动盈利")
    key_kline_low: float = Field(default=0, title="关键K线低点")


class DeepseekStrategy2(BaseStrategy):
    """
    UT+STC+HULL组合策略（无限易Pro适配版）
    策略特性：
    1. 支持手动/自动两种模式切换
    2. 动态参数调整机制
    3. 多重趋势过滤条件
    4. 智能止盈止损体系
    """
    author: str = "DeepSeek"
    version: str = "1.0.0"
    
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.long_price: float = 0
        
        # 行情分析
        self.trend_period: int = 15
        self.price_history: list = []
        self.trend_history: list = []
        self.volatility_history: list = []
        
        # 趋势参数
        self.trend_threshold: float = 0.6
        self.volatility_threshold: float = 0.5
        self.min_trend_duration: int = 5
        self.max_trend_duration: int = 30
        
        # 参数组合
        self.param_sets: Dict[str, Dict[str, Any]] = {
            "A": {"atr_period": 14, "stop_mult": 2.2, "profit_mult": 3.0, "trail_step": 1.0},
            "B": {"atr_period": 21, "stop_mult": 1.8, "profit_mult": 2.5, "trail_step": 0.5}
        }
        
        # 交易状态
        self.current_params: Dict[str, Any] = None
        self.tick: TickData = None
        self.order_id: str = None
        self.signal_price: float = 0
        self.entry_price: float = 0
        self.position_size: int = 0
        self.is_trailing: bool = False
        self.kline_generator: KLineGenerator = None

    # ------------------------ 无限易Pro标准接口 ------------------------
    def on_start(self) -> None:
        """策略启动回调（无限易Pro要求必须实现）"""
        self._initialize_kline_generator()
        self._reset_trading_state()
        
        if self.params_map.mode == "manual":
            self.current_params = {
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        
        self.write_log("策略启动完成", level="INFO")

    def on_stop(self) -> None:
        """策略停止回调（无限易Pro要求必须实现）"""
        self.write_log("策略停止运行", level="INFO")

    def on_tick(self, tick: TickData) -> None:
        """Tick行情回调（无限易Pro要求必须实现）"""
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order(self, order: OrderData) -> None:
        """订单状态回调（无限易Pro要求必须实现）"""
        super().on_order(order)
        if order.status == "cancelled":
            self.order_id = None
            self.write_log(f"订单取消: {order.order_id}", level="WARNING")

    def on_trade(self, trade: TradeData) -> None:
        """成交回报回调（无限易Pro要求必须实现）"""
        super().on_trade(trade)
        self.order_id = None
        self.write_log(f"成交回报: {trade.trade_id} 价格:{trade.price} 数量:{trade.volume}", level="INFO")

    def callback(self, kline: KLineData) -> None:
        """K线闭合回调（无限易Pro要求必须实现）"""
        self._process_kline_signal(kline)
        self._update_widget_indicator(kline)

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线推送回调（无限易Pro要求必须实现）"""
        self._process_kline_signal(kline)
        self._update_widget_indicator(kline)

    # ------------------------ 核心逻辑实现 ------------------------
    def _initialize_kline_generator(self) -> None:
        """初始化K线生成器（符合无限易Pro最新规范）"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

    def _reset_trading_state(self) -> None:
        """重置交易状态变量"""
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.price_history = []
        self.trend_history = []
        self.volatility_history = []
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        
        # 重置状态映射
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        self.state_map.key_kline_low = 0

    def _process_kline_signal(self, kline: KLineData) -> None:
        """处理K线信号（主逻辑入口）"""
        self._analyze_market_trend(kline)
        self._calculate_technical_indicators()
        
        if self.tick:
            self._update_dynamic_stops(self.tick.last_price)
        
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position
        self._generate_trading_signals(kline, position)
        self._execute_trading_signals(position)

    def _analyze_market_trend(self, kline: KLineData) -> None:
        """市场趋势分析（自动模式核心）"""
        self._update_price_history(kline.close)
        
        if len(self.price_history) == self.trend_period:
            price_changes = np.diff(self.price_history)
            direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
            volatility = np.std(price_changes)
            
            self._update_volatility_history(volatility)
            self._calculate_trend_strength()
            self._update_trend_duration()
            
            is_trending = self._determine_trend_state(direction_consistency, volatility)
            self._update_trend_status(is_trending)
            self.state_map.volatility = volatility
            self._adjust_parameters_on_reversal()

    def _calculate_technical_indicators(self) -> None:
        """计算技术指标（符合无限易Pro指标计算规范）"""
        if not self.current_params:
            self.current_params = self.param_sets["A"]
            
        self._calculate_hull_moving_average()
        self._calculate_schaff_trend_cycle()
        self._calculate_atr_value()
        
        if self.tick:
            self._update_stop_profit_levels()

    def _generate_trading_signals(self, kline: KLineData, position) -> None:
        """生成交易信号（买卖条件判断）"""
        # 做多条件
        long_conditions = (
            self.state_map.ut_signal and
            self.state_map.stc_value < 25 and
            self.state_map.stc_trend == "up" and
            kline.close > self.state_map.hull_ma and
            kline.close > kline.open
        )
        
        if long_conditions and position.net_position == 0:
            self.state_map.key_kline_low = kline.low
        
        # 信号生成
        self.buy_signal = (
            long_conditions and 
            position.net_position < self.params_map.max_positions
        )
        
        self.sell_signal = (
            position.net_position > 0 and (
                (self.tick and self.tick.last_price <= self.state_map.stop_loss) or
                (self.state_map.stc_value > 75) or
                (self.state_map.stc_trend == "down") or
                (kline.close < self.state_map.hull_ma)
            )
        )
        
        self._update_price_levels()

    def _execute_trading_signals(self, position) -> None:
        """执行交易信号（符合无限易Pro下单规范）"""
        if self.order_id:
            self.cancel_order(self.order_id)

        # 平仓逻辑
        if position.net_position > 0 and self.sell_signal:
            self._close_position(position)
        
        # 开仓逻辑
        if self.buy_signal and position.net_position < self.params_map.max_positions:
            self._open_position()

    # ------------------------ 指标计算细节 ------------------------
    def _calculate_hull_moving_average(self) -> None:
        """HULL移动平均线计算"""
        hull_ma = self.kline_generator.producer.hull_moving_average(
            self.params_map.hull_period, 
            array=True
        )
        self.state_map.hull_ma = round(hull_ma[-1], 2) if hull_ma else 0
        
        # UT信号判断
        close_prices = self.kline_generator.producer.close(array=True)
        self.state_map.ut_signal = (
            len(hull_ma) >= 3 and 
            len(close_prices) > 0 and 
            close_prices[-1] > hull_ma[-1] and 
            hull_ma[-1] > hull_ma[-2] and 
            hull_ma[-2] > hull_ma[-3]
        )

    def _calculate_schaff_trend_cycle(self) -> None:
        """STC指标计算"""
        stc = self.kline_generator.producer.schaff_trend_cycle(
            self.params_map.stc_length,
            self.params_map.stc_fast_length,
            self.params_map.stc_slow_length,
            array=True
        )
        if not stc:
            return
            
        self.state_map.stc_value = round(stc[-1], 2)
        
        if len(stc) >= 2:
            self.state_map.stc_trend = (
                "up" if stc[-1] > stc[-2] else 
                "down" if stc[-1] < stc[-2] else 
                "neutral"
            )

    def _calculate_atr_value(self) -> None:
        """ATR指标计算"""
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state_map.atr = round(atr, 2)

    # ------------------------ 动态止盈止损 ------------------------
    def _update_dynamic_stops(self, current_price: float) -> None:
        """动态止盈止损更新"""
        if self.position_size <= 0:
            return
            
        self._update_price_extremes(current_price)
        self._update_profit_status(current_price)
        self._adjust_stop_loss_level(current_price)

    def _update_price_extremes(self, current_price: float) -> None:
        """更新价格极值"""
        self.state_map.highest_price = max(self.state_map.highest_price, current_price)
        self.state_map.lowest_price = min(self.state_map.lowest_price, current_price)

    def _update_profit_status(self, current_price: float) -> None:
        """更新盈利状态"""
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)
        
        if not self.is_trailing and self.state_map.current_profit > self.state_map.atr:
            self.is_trailing = True

    def _adjust_stop_loss_level(self, current_price: float) -> None:
        """调整止损水平"""
        if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
            return
            
        if self.is_trailing:
            self.state_map.stop_loss = round(
                self.state_map.highest_price - self.state_map.atr * self.current_params["stop_mult"], 
                2
            )
        else:
            initial_stop = round(self.entry_price - self.state_map.atr * self.current_params["stop_mult"], 2)
            self.state_map.stop_loss = (
                min(initial_stop, self.state_map.key_kline_low) 
                if self.state_map.key_kline_low > 0 
                else initial_stop
            )

    # ------------------------ 辅助方法 ------------------------
    def _update_price_history(self, price: float) -> None:
        """维护价格历史数据"""
        self.price_history.append(price)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)

    def _update_volatility_history(self, volatility: float) -> None:
        """维护波动率历史数据"""
        self.volatility_history.append(volatility)
        if len(self.volatility_history) > 10:
            self.volatility_history.pop(0)

    def _calculate_trend_strength(self) -> None:
        """计算趋势强度"""
        hull_ma = self.kline_generator.producer.hull_moving_average(
            self.params_map.hull_period, 
            array=True
        )
        if len(hull_ma) >= 2:
            slope = (hull_ma[-1] - hull_ma[-2]) / hull_ma[-2]
            self.state_map.trend_strength = min(1.0, abs(slope))

    def _update_trend_duration(self) -> None:
        """更新趋势持续时间"""
        if self.state_map.is_trending:
            self.state_map.trend_duration = min(
                self.state_map.trend_duration + 1, 
                self.max_trend_duration
            )
            if self.state_map.trend_duration >= self.max_trend_duration:
                self.state_map.is_trending = False
        else:
            self.state_map.trend_duration = 0

    def _determine_trend_state(self, direction_consistency: float, volatility: float) -> bool:
        """判断趋势状态"""
        return (
            direction_consistency > self.trend_threshold and
            volatility > self.volatility_threshold and
            self.state_map.trend_strength > 0.3 and
            (self.state_map.trend_duration >= self.min_trend_duration or 
             direction_consistency > 0.8)
        )

    def _update_trend_status(self, is_trending: bool) -> None:
        """更新趋势状态"""
        if is_trending == self.state_map.is_trending:
            return
            
        self.state_map.is_trending = is_trending
        self.state_map.trend_type = "A" if is_trending else "B"
        self.trend_history.append(is_trending)
        
        if len(self.trend_history) > 20:
            self.trend_history.pop(0)
        
        self.current_params = self.param_sets[self.state_map.trend_type]

    def _adjust_parameters_on_reversal(self) -> None:
        """趋势反转时参数调整"""
        if len(self.trend_history) < 3:
            return
            
        recent_trends = self.trend_history[-3:]
        if recent_trends.count(True) == 2 and recent_trends[-1] == False:
            mult = 0.9 if self.state_map.trend_type == "A" else 1.1
            self.current_params["stop_mult"] *= mult
            self.current_params["profit_mult"] *= mult

    def _update_stop_profit_levels(self) -> None:
        """更新止盈止损水平"""
        current_price = self.tick.last_price
        
        if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
            self.state_map.stop_loss = self.params_map.fixed_stop_loss
            self.state_map.take_profit = self.params_map.fixed_take_profit
        else:
            self.state_map.stop_loss = round(
                current_price - self.state_map.atr * self.current_params["stop_mult"], 
                2
            )
            self.state_map.take_profit = round(
                current_price + self.state_map.atr * self.current_params["profit_mult"], 
                2
            )
        
        self.state_map.trailing_stop = round(
            current_price - self.state_map.atr * self.current_params["trail_step"], 
            2
        )

    def _update_price_levels(self) -> None:
        """更新价格水平"""
        if not self.tick:
            return
            
        self.long_price = (
            self.tick.ask_price2 
            if self.params_map.price_type == "D2" 
            else self.tick.ask_price1
        )

    def _close_position(self, position) -> None:
        """平仓操作"""
        self.signal_price = -self.long_price
        
        if not self.trading:
            return
            
        self.order_id = self.auto_close_position(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            price=self.long_price,
            volume=position.net_position,
            order_direction="sell"
        )
        self._reset_trading_state()
        self.write_log(f"平仓委托: 价格{self.long_price} 数量{position.net_position}", level="INFO")

    def _open_position(self) -> None:
        """开仓操作"""
        self.signal_price = self.long_price
        
        if not self.trading:
            return
            
        self.order_id = self.send_order(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            volume=self.params_map.order_volume,
            price=self.long_price,
            order_direction="buy"
        )
        self.entry_price = self.long_price
        self.state_map.highest_price = self.long_price
        self.state_map.lowest_price = self.long_price
        
        if self.state_map.key_kline_low > 0:
            self.state_map.stop_loss = self.state_map.key_kline_low - (self.state_map.atr * 0.5)
        
        self.write_log(f"开仓委托: 价格{self.long_price} 数量{self.params_map.order_volume}", level="INFO")

    def _update_widget_indicator(self, kline: KLineData) -> None:
        """更新界面指标显示"""
        if not hasattr(self, "widget"):
            return
            
        indicator_data = {
            "kline": kline,
            "signal_price": self.signal_price,
            "HULL_MA": self.state_map.hull_ma,
            "ATR": self.state_map.atr,
            "STC": self.state_map.stc_value,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop
        }
        self.widget.recv_kline(indicator_data)
        self.update_status_bar()


# 策略注册（无限易Pro要求）
strategy_class = DeepseekStrategy
strategy_name = "UT_STC_HULL组合策略"
strategy_desc = "UT信号+STC指标+HULL均线的多因子趋势策略"