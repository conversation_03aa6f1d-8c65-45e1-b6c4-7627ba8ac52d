"""
策略问题深度分析
分析当前策略的根本问题并提出改进方案
"""

import numpy as np
import pandas as pd
from fixed_original_strategy import FixedOriginalStrategy, FixedOptimizedStrategy
from strategy_backtester import StrategyBacktester


def analyze_strategy_problems():
    """分析策略问题"""
    print("=" * 60)
    print("策略问题深度分析")
    print("=" * 60)
    
    # 生成多种市场环境的测试数据
    test_scenarios = generate_market_scenarios()
    
    strategies = {
        "原策略": FixedOriginalStrategy(),
        "优化策略": FixedOptimizedStrategy()
    }
    
    backtester = StrategyBacktester()
    
    for scenario_name, data in test_scenarios.items():
        print(f"\n{scenario_name} 市场环境:")
        print("-" * 40)
        
        for strategy_name, strategy in strategies.items():
            result = backtester.run_backtest(strategy, data)
            closed_trades = [t for t in result.trades if not t.is_open]
            
            print(f"{strategy_name}:")
            print(f"  交易次数: {len(closed_trades)}")
            print(f"  胜率: {result.win_rate:.2%}")
            print(f"  总收益: {result.total_return:.2%}")
            print(f"  最大回撤: {result.max_drawdown:.2%}")
            print(f"  夏普比率: {result.sharpe_ratio:.3f}")
            
            # 分析交易分布
            if closed_trades:
                wins = [t for t in closed_trades if t.pnl > 0]
                losses = [t for t in closed_trades if t.pnl < 0]
                
                print(f"  盈利交易: {len(wins)}, 亏损交易: {len(losses)}")
                
                if wins:
                    avg_win = np.mean([t.pnl for t in wins])
                    max_win = max([t.pnl for t in wins])
                    print(f"  平均盈利: {avg_win:.2f}, 最大盈利: {max_win:.2f}")
                
                if losses:
                    avg_loss = np.mean([t.pnl for t in losses])
                    max_loss = min([t.pnl for t in losses])
                    print(f"  平均亏损: {avg_loss:.2f}, 最大亏损: {max_loss:.2f}")
                
                # 分析交易持续时间
                durations = []
                for trade in closed_trades:
                    if trade.exit_time and trade.entry_time:
                        duration = (trade.exit_time - trade.entry_time).total_seconds() / 3600
                        durations.append(duration)
                
                if durations:
                    print(f"  平均持仓时间: {np.mean(durations):.1f}小时")
                    print(f"  最长持仓: {max(durations):.1f}小时")
            
            print()


def generate_market_scenarios():
    """生成不同市场环境的测试数据"""
    scenarios = {}
    
    # 1. 趋势上涨市场
    dates = pd.date_range(start="2023-01-01", periods=2000, freq="1h")
    n = len(dates)
    np.random.seed(42)
    
    # 强趋势上涨
    trend = np.linspace(0, 0.3, n)
    noise = np.random.normal(0, 0.008, n)
    prices = 100 * (1 + trend + noise)
    
    data = create_ohlc_data(dates, prices)
    scenarios["强趋势上涨"] = data
    
    # 2. 震荡市场
    np.random.seed(123)
    oscillation = np.sin(np.arange(n) * 2 * np.pi / 200) * 0.05
    noise = np.random.normal(0, 0.015, n)
    prices = 100 * (1 + oscillation + noise)
    
    data = create_ohlc_data(dates, prices)
    scenarios["震荡市场"] = data
    
    # 3. 趋势下跌市场
    np.random.seed(456)
    trend = np.linspace(0, -0.2, n)
    noise = np.random.normal(0, 0.012, n)
    prices = 100 * (1 + trend + noise)
    
    data = create_ohlc_data(dates, prices)
    scenarios["趋势下跌"] = data
    
    # 4. 高波动市场
    np.random.seed(789)
    volatility_spikes = np.random.choice([1, 3], n, p=[0.9, 0.1])
    noise = np.random.normal(0, 0.01, n) * volatility_spikes
    trend = np.sin(np.arange(n) * 2 * np.pi / 500) * 0.1
    prices = 100 * (1 + trend + noise)
    
    data = create_ohlc_data(dates, prices)
    scenarios["高波动市场"] = data
    
    return scenarios


def create_ohlc_data(dates, prices):
    """创建OHLC数据"""
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    
    # 生成更真实的高低价
    volatility = np.random.uniform(0.003, 0.012, len(prices))
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + volatility)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - volatility)
    data['volume'] = np.random.randint(1000, 10000, len(prices))
    
    return data


def identify_key_problems():
    """识别关键问题"""
    print("\n" + "=" * 60)
    print("关键问题识别")
    print("=" * 60)
    
    problems = [
        "1. 信号质量问题:",
        "   - EMA交叉信号滞后性严重",
        "   - 缺乏多时间框架确认",
        "   - 没有市场状态识别",
        "   - 信号过滤机制不足",
        "",
        "2. 风险管理问题:",
        "   - 固定止损倍数不适应市场波动",
        "   - 缺乏动态止损调整",
        "   - 没有仓位管理",
        "   - 风险收益比不合理",
        "",
        "3. 市场适应性问题:",
        "   - 策略参数固定，无法适应不同市场",
        "   - 缺乏趋势强度判断",
        "   - 没有波动率制度识别",
        "   - 忽略市场微观结构",
        "",
        "4. 执行效率问题:",
        "   - 交易频率过高或过低",
        "   - 缺乏交易成本考虑",
        "   - 没有滑点保护",
        "   - 信号执行延迟",
    ]
    
    for problem in problems:
        print(problem)


def propose_optimization_solutions():
    """提出优化方案"""
    print("\n" + "=" * 60)
    print("优化方案")
    print("=" * 60)
    
    solutions = [
        "1. 信号质量优化:",
        "   ✓ 多指标融合：EMA + RSI + MACD + 布林带",
        "   ✓ 多时间框架确认：1H + 4H + 1D",
        "   ✓ 市场状态识别：趋势/震荡/突破",
        "   ✓ 信号强度评分系统",
        "",
        "2. 智能风险管理:",
        "   ✓ 基于ATR的动态止损",
        "   ✓ 追踪止损机制",
        "   ✓ 分批止盈策略",
        "   ✓ 波动率调整仓位",
        "",
        "3. 自适应参数系统:",
        "   ✓ 市场状态自动识别",
        "   ✓ 参数动态调整",
        "   ✓ 波动率制度切换",
        "   ✓ 趋势强度量化",
        "",
        "4. 执行优化:",
        "   ✓ 交易成本模型",
        "   ✓ 滑点控制",
        "   ✓ 信号确认机制",
        "   ✓ 风险预算管理",
    ]
    
    for solution in solutions:
        print(solution)


if __name__ == "__main__":
    analyze_strategy_problems()
    identify_key_problems()
    propose_optimization_solutions()
