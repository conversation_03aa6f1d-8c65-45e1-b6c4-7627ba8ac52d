from typing import Literal
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    trade_direction: Literal["buy", "sell"] = Field(default="buy", title="交易方向")
    order_volume: int = Field(default=1, title="报单数量")
    max_orders: int = Field(default=5, title="最大报单数", ge=1, le=5)
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=99)