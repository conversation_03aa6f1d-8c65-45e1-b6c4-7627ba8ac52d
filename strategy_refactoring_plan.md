# Strategy3.py 重构计划

## 1. 代码结构和样式分析

### DemoKC.py 样式特点
1. **Params类**:
   - 继承BaseParams
   - 使用Field定义参数，包含default和title属性
   - 参数类型使用类型注解，包括Literal类型
   - 代码简洁，结构清晰

2. **State类**:
   - 继承BaseState
   - 使用Field定义状态，包含default和title属性
   - 状态变量使用类型注解

3. **策略类**:
   - 初始化方法简洁，直接实例化Params和State
   - 属性定义清晰，符合PythOnGo框架标准

### Strategy3.py 当前结构
1. **Params类**:
   - 使用传统的__init__方法初始化参数
   - 没有使用Field定义参数
   - 缺少title属性

2. **State类**:
   - 使用传统的__init__方法初始化状态
   - 没有使用Field定义状态
   - 缺少title属性

3. **策略类**:
   - 初始化包含大量属性和复杂设置
   - 与DemoKC.py样式不一致

## 2. 重构计划

### 2.1 调整Params类
将Strategy3.py中的Params类调整为与DemoKC.py一致的样式：
- 继承BaseParams
- 使用Field定义参数，添加default和title属性
- 添加类型注解，包括Literal类型

### 2.2 调整State类
将Strategy3.py中的State类调整为与DemoKC.py一致的样式：
- 继承BaseState
- 使用Field定义状态，添加default和title属性
- 添加类型注解

### 2.3 调整Strategy3类初始化
修改Strategy3类的初始化方法以符合DemoKC.py的样式：
- 简化初始化逻辑
- 保持原有功能不变

## 3. 具体修改方案

### 3.1 Params类修改
```python
class Params(BaseParams):
    """参数映射模型 - 兼容无限易Pro架构"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    # 主技术指标参数
    hull_period: int = Field(default=9, title="HULL周期")
    stc_fast_period: int = Field(default=23, title="STC快线周期")
    stc_slow_period: int = Field(default=50, title="STC慢线周期")
    stc_cycle_period: int = Field(default=10, title="STC周期")
    # 交易参数
    order_volume: int = Field(default=1, title="报单数量")
    price_type: str = Field(default="D1", title="价格档位")
    trade_direction: str = Field(default="buy", title="交易方向")
    # 模块开关
    enable_fuzzy: bool = Field(default=True, title="启用模糊推理")
    enable_control: bool = Field(default=True, title="启用控制论")
    enable_ml: bool = Field(default=True, title="启用机器学习")
    # 风险管理参数
    stop_loss_pct: float = Field(default=0.02, title="止损百分比")
    take_profit_pct: float = Field(default=0.04, title="止盈百分比")
    # 信号阈值
    signal_threshold: float = Field(default=0.3, title="信号阈值")
```

### 3.2 State类修改
```python
class State(BaseState):
    """状态映射模型 - 兼容无限易Pro架构"""
    # 主图指标状态
    hull_ma: float = Field(default=0.0, title="HULL均线")
    stc_value: float = Field(default=50.0, title="STC值")
    stc_signal: float = Field(default=50.0, title="STC信号线")
    # 副图指标状态
    fuzzy_signal: float = Field(default=0.0, title="模糊信号")
    control_signal: float = Field(default=0.0, title="控制论信号")
    ml_signal: float = Field(default=0.0, title="机器学习信号")
    final_signal: float = Field(default=0.0, title="最终信号")
    # 系统状态
    volatility: float = Field(default=0.0, title="波动率")
    trend_strength: float = Field(default=0.0, title="趋势强度")
    system_stability: float = Field(default=1.0, title="系统稳定性")
    # 交易状态
    position_size: float = Field(default=0.0, title="持仓数量")
    entry_price: float = Field(default=0.0, title="入场价格")
    unrealized_pnl: float = Field(default=0.0, title="未实现盈亏")
    # 性能指标
    total_trades: int = Field(default=0, title="总交易数")
    winning_trades: int = Field(default=0, title="盈利交易数")
    total_profit: float = Field(default=0.0, title="总利润")
    win_rate: float = Field(default=0.0, title="胜率")
```

### 3.3 Strategy3类初始化修改
简化Strategy3类的初始化方法，使其与DemoKC.py样式一致，同时保持原有功能。

## 4. 验证计划
1. 确保修改后的代码能够正常运行
2. 验证所有功能保持不变
3. 检查与PythOnGo框架的兼容性