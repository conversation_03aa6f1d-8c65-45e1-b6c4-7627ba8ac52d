import numpy as np
from numpy import linregress, diff, log, std, sign, mean, percentile
from datetime import datetime, time
from typing import Literal, Dict, List
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData, PositionData
from pythongo.template import CtaTemplate
from pythongo.logger import logger

class Params(BaseParams):
    """策略参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期")
    mode: Literal["manual", "auto"] = Field(default="auto", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量", ge=1, le=100)
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=20)
    
    # ATR参数
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.0, le=3.0)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=1.5, le=4.0)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=2.0)
    
    # 固定止盈止损
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 交易时间
    trade_start: str = Field(default="09:00:00", title="交易开始时间")
    trade_end: str = Field(default="15:00:00", title="交易结束时间")
    no_trade_periods: List[str] = Field(
        default=["11:30:00-13:00:00"], 
        title="非交易时段"
    )

class State(BaseState):
    """策略状态映射模型"""
    # 指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    atr: float = Field(default=0, title="ATR")
    atr_percent: float = Field(default=0, title="ATR百分比")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    volatility: float = Field(default=0, title="波动率")
    
    # 交易状态
    signal_price: float = Field(default=0, title="信号价格")
    entry_price: float = Field(default=0, title="入场价格")
    position_size: int = Field(default=0, title="持仓数量")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 绩效
    today_trades: int = Field(default=0, title="今日交易次数")
    today_profit: float = Field(default=0, title="今日盈亏")
    total_trades: int = Field(default=0, title="总交易次数")
    win_rate: float = Field(default=0, title="胜率", ge=0, le=1)

class EmaAtrStrategy2(CtaTemplate):
    """向量化优化的EMA+ATR策略"""
    className = 'VectorizedEmaAtrStrategy'
    author = 'QuantBot'
    
    paramMap = Params.__annotations__
    varMap = State.__annotations__

    def __init__(self, ctaEngine=None, setting=None):
        super().__init__(ctaEngine, setting)
        self.params = Params()
        self.state = State()
        
        # 数据容器
        self.price_history = np.zeros(100)
        self.volatility_history = np.zeros(20)
        self.trend_history = np.zeros(20, dtype=bool)
        
        # 初始化参数
        self.param_sets = {
            "A": {"ema": [5, 15, 30], "atr_period": 14, "stop_mult": 2.2, "profit_mult": 3.0, "trail_step": 1.0},
            "B": {"ema": [3, 10, 20], "atr_period": 21, "stop_mult": 1.8, "profit_mult": 2.5, "trail_step": 0.5}
        }
        
        # 解析时间
        self.trade_start_time = datetime.strptime(self.params.trade_start, "%H:%M:%S").time()
        self.trade_end_time = datetime.strptime(self.params.trade_end, "%H:%M:%S").time()
        self.no_trade_ranges = [
            (datetime.strptime(s, "%H:%M:%S").time(), datetime.strptime(e, "%H:%M:%S").time())
            for s, e in [p.split('-') for p in self.params.no_trade_periods]
        ]

    def onInit(self):
        """策略初始化"""
        self.loadBar(10)

    def onStart(self):
        """策略启动"""
        self.subscribe(self.params.exchange, self.params.instrument_id)
        self.queryPosition()
        self.resetState()

    def onTick(self, tick: TickData):
        """Tick处理"""
        if not self._isValidTick(tick):
            return
            
        # 向量化更新价格历史
        self.price_history = np.roll(self.price_history, -1)
        self.price_history[-1] = tick.last_price
        
        # 更新动态止损
        self._updateDynamicStops(tick.last_price)

    def _isValidTick(self, tick: TickData) -> bool:
        """向量化验证Tick"""
        tick_time = datetime.fromtimestamp(tick.datetime).time()
        price_valid = all([tick.last_price > 0, tick.ask_price1 > 0, tick.bid_price1 > 0])
        time_valid = self.trade_start_time <= tick_time <= self.trade_end_time
        no_trade = any(start <= tick_time <= end for start, end in self.no_trade_ranges)
        return price_valid and time_valid and not no_trade

    def onBar(self, bar: KLineData):
        """K线处理"""
        # 向量化计算指标
        self._calcIndicators()
        
        # 向量化趋势判断
        self._calcTrendState()
        
        # 生成信号
        signals = self._generateSignals()
        self.buy_signal, self.sell_signal = signals
        
        # 执行交易
        self._executeTrade()

    def _calcIndicators(self):
        """向量化计算指标"""
        # 动态参数选择
        params = self.param_sets[self.state.trend_type] if self.params.mode == "auto" else {
            "ema": [5, 15, 30],
            "atr_period": self.params.atr_period,
            "stop_mult": self.params.stop_mult,
            "profit_mult": self.params.profit_mult,
            "trail_step": self.params.trail_step
        }
        
        # 计算EMA
        ema_values = [self.kline_generator.producer.ema(p) for p in params["ema"]]
        self.state.ema_fast, self.state.ema_mid, self.state.ema_slow = [v[-1] if len(v) > 0 else 0 for v in ema_values]
        
        # 计算ATR
        atr, _ = self.kline_generator.producer.atr(params["atr_period"])
        self.state.atr = atr[-1] if len(atr) > 0 else 0
        self.state.atr_percent = self.state.atr / self.price_history[-1] * 100 if self.price_history[-1] > 0 else 0
        
        # 计算止损止盈
        current_price = self.price_history[-1]
        self.state.stop_loss = (
            self.params.fixed_stop_loss if self.params.use_fixed_stops 
            else current_price - self.state.atr * params["stop_mult"]
        )
        self.state.take_profit = (
            self.params.fixed_take_profit if self.params.use_fixed_stops 
            else current_price + self.state.atr * params["profit_mult"]
        )
        self.state.trailing_stop = current_price - self.state.atr * params["trail_step"]

    def _calcTrendState(self):
        """向量化趋势判断"""
        if len(self.price_history) < 30:
            return
            
        # 线性回归斜率
        x = np.arange(len(self.price_history))
        slope, _, _, _, _ = linregress(x, self.price_history)
        
        # ADX指标
        adx = self.kline_generator.producer.adx(14)
        self.state.trend_strength = adx[-1]/100 if len(adx) > 0 else 0
        
        # 波动率计算
        returns = diff(log(self.price_history))
        self.state.volatility = std(returns) * np.sqrt(252)
        self.volatility_history = np.roll(self.volatility_history, -1)
        self.volatility_history[-1] = self.state.volatility
        
        # 趋势判断
        is_trending = (
            (abs(slope) > 0.001) & 
            (self.state.trend_strength > 0.25) & 
            (self.state.volatility > percentile(self.volatility_history, 50))
        )
        
        # 更新趋势状态
        self.state.is_trending = is_trending
        self.state.trend_type = "A" if is_trending else "B"
        self.trend_history = np.roll(self.trend_history, -1)
        self.trend_history[-1] = is_trending

    def _generateSignals(self) -> tuple[bool, bool]:
        """向量化信号生成"""
        current_price = self.price_history[-1]
        ema_condition = np.array([
            current_price > self.state.ema_fast,
            self.state.ema_fast > self.state.ema_mid,
            self.state.ema_mid > self.state.ema_slow
        ])
        
        # 趋势信号
        trend_signal = (
            ema_condition.all() & 
            (self.state.trend_strength > 0.3) & 
            ((current_price - self.state.ema_fast) < self.state.atr * 0.5)
        )
        
        # 震荡信号
        range_signal = (
            (current_price < self.state.ema_slow) & 
            (self.state.volatility < percentile(self.volatility_history, 75)) & 
            (abs(self.state.ema_fast - self.state.ema_slow) < self.state.atr * 0.3)
        )
        
        # 综合信号
        buy_signal = np.where(self.state.is_trending, trend_signal, range_signal)
        sell_signal = (
            (current_price <= self.state.stop_loss) | 
            (~ema_condition[0] if self.state.is_trending else 
             (current_price > self.state.ema_slow) & 
             (self.state.volatility > percentile(self.volatility_history, 75)))
        )
        
        # 设置信号价格
        self.state.signal_price = (
            self.tick.ask_price1 if self.params.price_type == "D1" else self.tick.ask_price2
        ) if buy_signal else (
            self.tick.bid_price1 if self.params.price_type == "D1" else self.tick.bid_price2
        )
        
        return buy_signal, sell_signal

    def _updateDynamicStops(self, current_price: float):
        """向量化动态止损更新"""
        if self.state.position_size == 0:
            return
            
        # 更新价格极值
        self.state.highest_price = max(self.state.highest_price, current_price)
        self.state.lowest_price = min(self.state.lowest_price, current_price)
        
        # 计算盈亏
        self.state.current_profit = (current_price - self.state.entry_price) * self.state.position_size
        self.state.max_profit = max(self.state.max_profit, self.state.current_profit)
        
        # 分阶段止损
        profit_pct = (current_price - self.state.entry_price) / self.state.entry_price
        atr_pct = self.state.atr / self.state.entry_price
        
        # 使用向量化条件选择止损位置
        stop_levels = np.select(
            [
                profit_pct < atr_pct,
                profit_pct < 2 * atr_pct,
                profit_pct >= 2 * atr_pct
            ],
            [
                self.state.entry_price - self.state.atr * 1.5,
                self.state.entry_price,
                current_price - self.state.atr * (1.0 - 0.5 * (profit_pct / (3 * atr_pct)))
            ],
            default=self.state.stop_loss
        )
        self.state.stop_loss = max(self.state.stop_loss, stop_levels)

    def _executeTrade(self):
        """向量化交易执行"""
        position = self.getPosition(self.params.instrument_id)
        self.state.position_size = position.position if position else 0
        
        # 平仓条件
        close_condition = (self.state.position_size > 0) & self.sell_signal
        if close_condition:
            self.sell(self.state.signal_price, self.state.position_size, "SELL")
            self.resetState()
            
        # 开仓条件
        open_condition = (self.state.position_size == 0) & self.buy_signal & (self.state.position_size < self.params.max_positions)
        if open_condition:
            self.buy(self.state.signal_price, self.params.order_volume, "BUY")
            self.state.entry_price = self.state.signal_price
            self.state.highest_price = self.state.signal_price
            self.state.lowest_price = self.state.signal_price

    def resetState(self):
        """重置状态"""
        self.state.__dict__.update({k: 0 for k in self.state.__annotations__ if isinstance(getattr(self.state, k), (int, float))})
        self.buy_signal = False
        self.sell_signal = False