"""
模糊理论配置管理模块
支持热更新和参数优化
"""

import json
import yaml
import numpy as np
from datetime import datetime
from typing import Dict, Any, List, Optional
import threading
import time
import os

class FuzzyConfigManager:
    """模糊系统配置管理器"""
    
    def __init__(self, config_file="fuzzy_config.yaml"):
        self.config_file = config_file
        self.config = {}
        self.watchers = []
        self.lock = threading.Lock()
        self.auto_save = True
        self.last_modified = None
        
        # 默认配置
        self.default_config = {
            "fuzzy_sets": {
                "stability": {
                    "Low": {"a": 0.0, "b": 0.1, "c": 0.3, "d": 0.4},
                    "Medium": {"a": 0.3, "b": 0.4, "c": 0.6, "d": 0.7},
                    "High": {"a": 0.6, "b": 0.7, "c": 0.9, "d": 1.0}
                },
                "volatility": {
                    "Low": {"a": 0.0, "b": 0.01, "c": 0.03, "d": 0.05},
                    "Medium": {"a": 0.03, "b": 0.05, "c": 0.07, "d": 0.09},
                    "High": {"a": 0.07, "b": 0.09, "c": 0.12, "d": 0.15}
                },
                "profit": {
                    "Negative": {"a": -0.1, "b": -0.08, "c": -0.03, "d": 0.0},
                    "Low": {"a": -0.02, "b": 0.0, "c": 0.02, "d": 0.04},
                    "Medium": {"a": 0.02, "b": 0.04, "c": 0.06, "d": 0.08},
                    "High": {"a": 0.06, "b": 0.08, "c": 0.12, "d": 0.15}
                }
            },
            "learning_parameters": {
                "learning_rate": 0.01,
                "adaptation_enabled": True,
                "performance_threshold": 0.6,
                "max_rules": 30,
                "rule_pruning_threshold": 0.01
            },
            "fusion_weights": {
                "technical": 0.4,
                "sentiment": 0.3,
                "fundamental": 0.2,
                "news": 0.1
            },
            "defuzzification": {
                "method": "centroid",  # centroid, area_center, maximum, mean_of_maximum
                "q_rofs_q": 2
            },
            "time_series": {
                "window_size": 20,
                "n_intervals": 7,
                "prediction_steps": 3
            },
            "clustering": {
                "n_clusters": 5,
                "fuzzy_index": 2.0,
                "max_iter": 100,
                "tolerance": 1e-4
            }
        }
        
        self.load_config()
        
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                        self.config = yaml.safe_load(f)
                    else:
                        self.config = json.load(f)
                
                self.last_modified = os.path.getmtime(self.config_file)
                print(f"配置文件加载成功: {self.config_file}")
            else:
                self.config = self.default_config.copy()
                self.save_config()
                print(f"使用默认配置并保存到: {self.config_file}")
                
        except Exception as e:
            print(f"配置文件加载失败: {e}")
            self.config = self.default_config.copy()
    
    def save_config(self):
        """保存配置文件"""
        try:
            with self.lock:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    if self.config_file.endswith('.yaml') or self.config_file.endswith('.yml'):
                        yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                    else:
                        json.dump(self.config, f, indent=2, ensure_ascii=False)
                
                self.last_modified = os.path.getmtime(self.config_file)
                print(f"配置文件保存成功: {self.config_file}")
                
        except Exception as e:
            print(f"配置文件保存失败: {e}")
    
    def get(self, key_path: str, default=None):
        """获取配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key_path: str, value: Any):
        """设置配置值，支持点分隔的路径"""
        keys = key_path.split('.')
        config = self.config
        
        # 导航到父级
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 设置值
        config[keys[-1]] = value
        
        if self.auto_save:
            self.save_config()
        
        # 通知观察者
        self._notify_watchers(key_path, value)
    
    def update(self, updates: Dict[str, Any]):
        """批量更新配置"""
        for key_path, value in updates.items():
            self.set(key_path, value)
    
    def watch(self, callback):
        """添加配置变化监听器"""
        self.watchers.append(callback)
    
    def _notify_watchers(self, key_path: str, value: Any):
        """通知配置变化监听器"""
        for callback in self.watchers:
            try:
                callback(key_path, value)
            except Exception as e:
                print(f"配置监听器回调失败: {e}")
    
    def start_file_watcher(self):
        """启动文件变化监控"""
        def watch_file():
            while True:
                try:
                    if os.path.exists(self.config_file):
                        current_modified = os.path.getmtime(self.config_file)
                        if self.last_modified and current_modified > self.last_modified:
                            print("检测到配置文件变化，重新加载...")
                            self.load_config()
                            self._notify_watchers("file_changed", None)
                    
                    time.sleep(1)  # 每秒检查一次
                    
                except Exception as e:
                    print(f"文件监控错误: {e}")
                    time.sleep(5)
        
        watcher_thread = threading.Thread(target=watch_file, daemon=True)
        watcher_thread.start()
    
    def optimize_parameters(self, performance_data: List[Dict]):
        """基于性能数据优化参数"""
        try:
            if len(performance_data) < 10:
                return
            
            # 分析性能数据
            successful_configs = [
                data for data in performance_data 
                if data.get('performance', 0) > 0.7
            ]
            
            if not successful_configs:
                return
            
            # 计算最优参数
            optimal_params = {}
            
            # 学习率优化
            learning_rates = [data.get('learning_rate', 0.01) for data in successful_configs]
            optimal_params['learning_parameters.learning_rate'] = np.mean(learning_rates)
            
            # 融合权重优化
            if 'fusion_weights' in successful_configs[0]:
                for weight_type in ['technical', 'sentiment', 'fundamental', 'news']:
                    weights = [
                        data.get('fusion_weights', {}).get(weight_type, 0.25) 
                        for data in successful_configs
                    ]
                    optimal_params[f'fusion_weights.{weight_type}'] = np.mean(weights)
            
            # 应用优化参数
            self.update(optimal_params)
            print(f"参数优化完成，更新了 {len(optimal_params)} 个参数")
            
        except Exception as e:
            print(f"参数优化失败: {e}")
    
    def export_config(self, filename: str):
        """导出配置到指定文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    yaml.dump(self.config, f, default_flow_style=False, allow_unicode=True)
                else:
                    json.dump(self.config, f, indent=2, ensure_ascii=False)
            print(f"配置导出成功: {filename}")
        except Exception as e:
            print(f"配置导出失败: {e}")
    
    def import_config(self, filename: str):
        """从指定文件导入配置"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                if filename.endswith('.yaml') or filename.endswith('.yml'):
                    imported_config = yaml.safe_load(f)
                else:
                    imported_config = json.load(f)
            
            self.config.update(imported_config)
            if self.auto_save:
                self.save_config()
            
            print(f"配置导入成功: {filename}")
            self._notify_watchers("config_imported", imported_config)
            
        except Exception as e:
            print(f"配置导入失败: {e}")
    
    def reset_to_default(self):
        """重置为默认配置"""
        self.config = self.default_config.copy()
        if self.auto_save:
            self.save_config()
        print("配置已重置为默认值")
        self._notify_watchers("config_reset", None)
    
    def get_config_summary(self):
        """获取配置摘要"""
        return {
            "fuzzy_sets_count": sum(len(sets) for sets in self.config.get("fuzzy_sets", {}).values()),
            "learning_enabled": self.config.get("learning_parameters", {}).get("adaptation_enabled", False),
            "max_rules": self.config.get("learning_parameters", {}).get("max_rules", 30),
            "defuzz_method": self.config.get("defuzzification", {}).get("method", "centroid"),
            "last_modified": datetime.fromtimestamp(self.last_modified) if self.last_modified else None
        }

# 全局配置管理器实例
fuzzy_config = FuzzyConfigManager()
