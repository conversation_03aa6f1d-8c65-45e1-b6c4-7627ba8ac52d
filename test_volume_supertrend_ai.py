#!/usr/bin/env python3
"""
Volume SuperTrend AI 测试脚本
测试新实现的Volume SuperTrend AI算法和机器学习功能
"""

import sys
import numpy as np
import time
from collections import deque

# 模拟导入（因为实际的pythongo框架可能不可用）
class MockKLineData:
    def __init__(self, close, volume, high=None, low=None):
        self.close = close
        self.volume = volume
        self.high = high or close * 1.01
        self.low = low or close * 0.99

class MockTickData:
    def __init__(self, last_price, bid_price=None, ask_price=None):
        self.last_price = last_price
        self.bid_price = bid_price or last_price * 0.999
        self.ask_price = ask_price or last_price * 1.001

def test_volume_supertrend_ai():
    """测试Volume SuperTrend AI核心算法"""
    print("=== Volume SuperTrend AI 测试开始 ===")
    
    try:
        # 导入我们的Volume SuperTrend AI类
        sys.path.append('.')
        from OptionStrategy5 import VolumeSuperTrendAI, AdaptiveParameterOptimizer
        
        # 初始化Volume SuperTrend AI
        vst_ai = VolumeSuperTrendAI(
            k_neighbors=5,
            data_points=20,
            price_trend_len=30,
            prediction_len=100
        )
        
        print("✓ Volume SuperTrend AI 初始化成功")
        
        # 生成模拟数据
        print("\n--- 生成模拟市场数据 ---")
        np.random.seed(42)
        
        # 模拟价格走势（上升趋势 + 噪音）
        base_price = 100.0
        prices = []
        volumes = []
        
        for i in range(200):
            # 基础趋势
            trend = 0.01 * i  # 上升趋势
            noise = np.random.normal(0, 0.5)  # 随机噪音
            price = base_price + trend + noise
            
            # 成交量（价格上涨时成交量增加）
            volume_base = 1000
            volume_trend = max(0, (price - base_price) * 10)
            volume_noise = np.random.normal(0, 100)
            volume = max(100, volume_base + volume_trend + volume_noise)
            
            prices.append(price)
            volumes.append(volume)
        
        print(f"✓ 生成了 {len(prices)} 个数据点")
        print(f"  价格范围: {min(prices):.2f} - {max(prices):.2f}")
        print(f"  成交量范围: {min(volumes):.0f} - {max(volumes):.0f}")
        
        # 测试VWMA计算
        print("\n--- 测试VWMA计算 ---")
        vwma_10 = vst_ai.calculate_vwma(prices[-10:], volumes[-10:], 10)
        simple_ma_10 = sum(prices[-10:]) / 10
        
        print(f"✓ VWMA(10): {vwma_10:.2f}")
        print(f"  简单MA(10): {simple_ma_10:.2f}")
        print(f"  差异: {abs(vwma_10 - simple_ma_10):.2f}")
        
        # 测试SuperTrend计算
        print("\n--- 测试SuperTrend计算 ---")
        atr_values = [1.0] * len(prices)  # 模拟ATR值
        
        supertrend_values = []
        directions = []
        
        for i in range(20, len(prices)):
            price_slice = prices[:i+1]
            volume_slice = volumes[:i+1]
            atr_slice = atr_values[:i+1]
            
            supertrend, direction = vst_ai.calculate_supertrend(
                price_slice, volume_slice, atr_slice, factor=3.0, period=10
            )
            
            supertrend_values.append(supertrend)
            directions.append(direction)
        
        print(f"✓ 计算了 {len(supertrend_values)} 个SuperTrend值")
        print(f"  最后5个SuperTrend值: {[f'{v:.2f}' for v in supertrend_values[-5:]]}")
        print(f"  最后5个方向: {directions[-5:]}")
        
        # 测试数据更新和AI预测
        print("\n--- 测试AI预测功能 ---")
        
        # 逐步更新数据
        for i in range(50, 150):  # 使用部分数据进行测试
            vst_ai.update_data(prices[i], volumes[i], atr_values[i])
        
        print(f"✓ 更新了 {len(vst_ai.price_history)} 个数据点到AI模型")
        
        # 尝试训练模型
        if vst_ai.train_model():
            print("✓ AI模型训练成功")
            
            # 测试预测
            for i in range(5):
                test_price = prices[150 + i]
                test_supertrend = supertrend_values[-10] if supertrend_values else test_price
                
                signal_strength, signal_type = vst_ai.predict_signal(test_supertrend)
                print(f"  预测 {i+1}: 强度={signal_strength:.3f}, 类型={signal_type}")
        else:
            print("⚠ AI模型训练失败，使用简单规则")
            
            # 测试简单规则预测
            test_supertrend = supertrend_values[-1] if supertrend_values else prices[-1]
            signal_strength, signal_type = vst_ai.predict_signal(test_supertrend)
            print(f"  简单规则预测: 强度={signal_strength:.3f}, 类型={signal_type}")
        
        # 测试自适应参数优化器
        print("\n--- 测试自适应参数优化器 ---")
        optimizer = AdaptiveParameterOptimizer()
        
        # 添加一些性能数据
        test_params = [
            {"ema": [5, 13, 34], "stop_mult": 1.8, "profit_mult": 2.5},
            {"ema": [8, 21, 55], "stop_mult": 2.0, "profit_mult": 3.0},
            {"ema": [12, 26, 50], "stop_mult": 1.5, "profit_mult": 2.0},
        ]
        
        test_performances = [0.65, 0.78, 0.72]  # 模拟性能分数
        
        for params, perf in zip(test_params, test_performances):
            optimizer.add_performance_data(params, perf)
        
        print(f"✓ 添加了 {len(test_performances)} 组性能数据")
        
        # 测试参数优化
        current_params = {"ema": [10, 20, 40], "stop_mult": 2.2, "profit_mult": 2.8}
        optimized_params = optimizer.optimize_parameters(current_params)
        
        print(f"  当前参数: {current_params}")
        print(f"  优化后参数: {optimized_params}")
        
        print("\n=== Volume SuperTrend AI 测试完成 ===")
        print("✓ 所有核心功能测试通过")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signal_generation():
    """测试信号生成逻辑"""
    print("\n=== 信号生成测试 ===")
    
    try:
        # 模拟不同市场条件下的信号生成
        test_scenarios = [
            {
                "name": "强势上涨",
                "price": 105.0,
                "supertrend": 102.0,
                "direction": -1,
                "ai_signal": (0.85, "bullish"),
                "volume_ratio": 1.8
            },
            {
                "name": "弱势下跌", 
                "price": 98.0,
                "supertrend": 101.0,
                "direction": 1,
                "ai_signal": (0.25, "bearish"),
                "volume_ratio": 0.8
            },
            {
                "name": "震荡整理",
                "price": 100.5,
                "supertrend": 100.0,
                "direction": -1,
                "ai_signal": (0.55, "neutral"),
                "volume_ratio": 1.1
            }
        ]
        
        for scenario in test_scenarios:
            print(f"\n--- {scenario['name']} ---")
            
            # 模拟信号计算
            vst_signal = 0.0
            
            # SuperTrend方向信号
            if scenario['direction'] == -1:
                vst_signal += 0.4
            
            # 价格位置信号
            if scenario['price'] > scenario['supertrend']:
                price_ratio = scenario['price'] / scenario['supertrend']
                if price_ratio > 1.01:
                    vst_signal += 0.3 * min((price_ratio - 1) * 10, 1.0)
            
            # AI信号
            ai_strength, ai_type = scenario['ai_signal']
            if ai_type == "bullish":
                vst_signal += 0.3 * ai_strength
            elif ai_type == "bearish":
                vst_signal -= 0.3 * (1 - ai_strength)
            
            # 成交量确认
            volume_signal = min(scenario['volume_ratio'] / 3.0, 1.0) if scenario['volume_ratio'] > 1.5 else 0.5
            
            # 综合信号
            combined_signal = vst_signal * 0.5 + 0.7 * 0.3 + volume_signal * 0.2  # 假设传统信号为0.7
            
            # 生成交易决策
            if combined_signal > 0.7 and ai_type == "bullish" and scenario['direction'] == -1:
                decision = "买入"
            elif combined_signal < 0.3 or ai_type == "bearish" or scenario['direction'] == 1:
                decision = "卖出/观望"
            else:
                decision = "观望"
            
            print(f"  VST AI信号: {vst_signal:.3f}")
            print(f"  成交量信号: {volume_signal:.3f}")
            print(f"  综合信号: {combined_signal:.3f}")
            print(f"  交易决策: {decision}")
        
        print("\n✓ 信号生成测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 信号生成测试失败: {e}")
        return False

if __name__ == "__main__":
    print("Volume SuperTrend AI 策略测试")
    print("=" * 50)
    
    # 运行测试
    test1_result = test_volume_supertrend_ai()
    test2_result = test_signal_generation()
    
    print("\n" + "=" * 50)
    if test1_result and test2_result:
        print("🎉 所有测试通过！Volume SuperTrend AI策略已准备就绪")
    else:
        print("❌ 部分测试失败，请检查实现")
