from typing import Literal, Dict, List
from datetime import datetime, time
import numpy as np
from numpy import linregress
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData, PositionData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator
from pythongo.template import CtaTemplate
from pythongo.vtObject import VtObject
from pythongo.logger import logger

class Params(BaseParams):
    """策略参数映射模型 - 无限易Pro界面可配置参数"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K线周期", 
                           description="支持M1/M5/M15/M30/H1/D1等周期")
    mode: Literal["manual", "auto"] = Field(default="auto", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量", ge=1, le=100)
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=20)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.0, le=3.0)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=1.5, le=4.0)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=2.0)
    
    # 固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 交易时间过滤
    trade_start: str = Field(default="09:00:00", title="交易开始时间")
    trade_end: str = Field(default="15:00:00", title="交易结束时间")
    no_trade_periods: List[str] = Field(
        default=["11:30:00-13:00:00"], 
        title="非交易时段",
        description="格式: HH:MM:SS-HH:MM:SS,多个时段用分号分隔"
    )

class State(BaseState):
    """策略状态映射模型 - 无限易Pro界面可显示变量"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    atr_percent: float = Field(default=0, title="ATR百分比", description="ATR/当前价格")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="动态止损价")
    take_profit: float = Field(default=0, title="动态止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 交易状态
    signal_price: float = Field(default=0, title="信号价格")
    entry_price: float = Field(default=0, title="入场价格")
    position_size: int = Field(default=0, title="持仓数量")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 绩效统计
    today_trades: int = Field(default=0, title="今日交易次数")
    today_profit: float = Field(default=0, title="今日盈亏")
    total_trades: int = Field(default=0, title="总交易次数")
    win_rate: float = Field(default=0, title="胜率", ge=0, le=1)

class EmaAtrStrategy(CtaTemplate):
    """三周期EMA+ATR趋势策略 - 无限易Pro适配版"""
    className = 'EmaAtrStrategy'
    author = 'QuantBot'
    
    # 参数映射表 - 用于无限易Pro界面显示
    paramMap = {
        'exchange': '交易所',
        'instrument_id': '合约代码',
        'kline_style': 'K线周期',
        'mode': '工作模式',
        'price_type': '价格档位',
        'order_volume': '报单数量',
        'max_positions': '最大持仓',
        'atr_period': 'ATR周期',
        'stop_mult': '止损倍数',
        'profit_mult': '止盈倍数',
        'trail_step': '追踪步长',
        'fixed_stop_loss': '固定止损',
        'fixed_take_profit': '固定止盈',
        'use_fixed_stops': '使用固定止损',
        'trade_start': '交易开始时间',
        'trade_end': '交易结束时间',
        'no_trade_periods': '非交易时段'
    }
    
    # 状态映射表 - 用于无限易Pro界面显示
    varMap = {
        'ema_fast': '快线EMA',
        'ema_mid': '中线EMA',
        'ema_slow': '慢线EMA',
        'atr': 'ATR值',
        'atr_percent': 'ATR%',
        'trend_type': '行情模式',
        'is_trending': '趋势状态',
        'trend_strength': '趋势强度',
        'volatility': '波动率',
        'stop_loss': '动态止损',
        'take_profit': '动态止盈',
        'trailing_stop': '追踪止损',
        'signal_price': '信号价格',
        'entry_price': '入场价格',
        'position_size': '持仓数量',
        'current_profit': '当前盈亏',
        'max_profit': '最大盈亏',
        'today_trades': '今日交易',
        'today_profit': '今日盈亏',
        'total_trades': '总交易',
        'win_rate': '胜率%'
    }
    
    def __init__(self, ctaEngine=None, setting=None):
        """初始化策略"""
        super().__init__(ctaEngine, setting)
        
        # 初始化参数
        self.params = Params()
        self.state = State()
        
        # 交易信号
        self.buy_signal = False
        self.sell_signal = False
        
        # 行情数据容器
        self.price_history = []
        self.trend_history = []
        self.volatility_history = []
        
        # 交易控制
        self.tick: TickData = None
        self.kline: KLineData = None
        self.order_id = None
        self.last_trade_time = None
        
        # 绩效统计
        self.win_count = 0
        self.loss_count = 0
        self.trade_records = []
        
        # 初始化K线生成器
        self.kline_generator = None
        
        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "ema": [5, 15, 30],
                "atr_period": 14,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "ema": [3, 10, 20],
                "atr_period": 21,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        self.current_params = None
        
        # 解析非交易时段
        self.no_trade_ranges = []
        for period in self.params.no_trade_periods:
            start_str, end_str = period.split('-')
            start_time = datetime.strptime(start_str, "%H:%M:%S").time()
            end_time = datetime.strptime(end_str, "%H:%M:%S").time()
            self.no_trade_ranges.append((start_time, end_time))
        
        # 解析交易时间
        self.trade_start_time = datetime.strptime(self.params.trade_start, "%H:%M:%S").time()
        self.trade_end_time = datetime.strptime(self.params.trade_end, "%H:%M:%S").time()

    def onInit(self):
        """策略初始化回调"""
        logger.info("策略初始化")
        self.loadBar(10)  # 加载10天历史数据
        
    def onStart(self):
        """策略启动回调"""
        logger.info("策略启动")
        
        # 初始化K线生成器
        self.kline_generator = KLineGenerator(
            callback=self.onBar,
            exchange=self.params.exchange,
            instrument_id=self.params.instrument_id,
            style=self.params.kline_style
        )
        
        # 订阅行情
        self.subscribe(
            exchange=self.params.exchange,
            symbol=self.params.instrument_id
        )
        
        # 查询持仓
        self.queryPosition()
        
        # 重置状态
        self.resetState()
        
    def onStop(self):
        """策略停止回调"""
        logger.info("策略停止")
        if self.kline_generator:
            self.kline_generator.stop()
        
    def onTick(self, tick: TickData):
        """Tick行情回调"""
        super().onTick(tick)
        self.tick = tick
        
        # 过滤无效行情
        if not self.isValidTick(tick):
            return
            
        # 更新K线
        if self.kline_generator:
            self.kline_generator.updateTick(tick)
            
        # 更新动态止盈止损
        self.updateDynamicStops(tick.last_price)
        
        # 记录行情数据
        self.recordMarketData(tick)
        
    def onBar(self, bar: KLineData):
        """K线回调"""
        self.kline = bar
        
        # 计算指标
        self.calcIndicators()
        
        # 计算趋势状态
        self.calcTrendState()
        
        # 生成交易信号
        self.generateSignal()
        
        # 执行交易
        self.executeTrade()
        
        # 更新界面
        self.updateUI()
        
    def onOrder(self, order: OrderData):
        """委托回报回调"""
        super().onOrder(order)
        if order.status == "ALLTRADED":
            logger.info(f"委托全部成交: {order.orderID} {order.direction} {order.volume}")
        elif order.status == "CANCELLED":
            logger.info(f"委托已撤销: {order.orderID}")
            
    def onTrade(self, trade: TradeData):
        """成交回报回调"""
        super().onTrade(trade)
        logger.info(f"成交回报: {trade.tradeID} {trade.direction} {trade.volume}@{trade.price}")
        
        # 更新绩效统计
        self.updatePerformance(trade)
        
    def onPosition(self, position: PositionData):
        """持仓回报回调"""
        super().onPosition(position)
        self.state.position_size = position.position
        if position.position > 0:
            self.state.entry_price = position.price
            
    def resetState(self):
        """重置策略状态"""
        self.buy_signal = False
        self.sell_signal = False
        self.state.signal_price = 0
        self.state.entry_price = 0
        self.state.position_size = 0
        self.state.current_profit = 0
        self.state.max_profit = 0
        self.state.highest_price = 0
        self.state.lowest_price = 0
        
    def isValidTick(self, tick: TickData) -> bool:
        """检查Tick是否有效"""
        if (tick.last_price <= 0 or 
            tick.ask_price1 <= 0 or 
            tick.bid_price1 <= 0):
            return False
            
        # 检查交易时间
        tick_time = datetime.fromtimestamp(tick.datetime).time()
        if not (self.trade_start_time <= tick_time <= self.trade_end_time):
            return False
            
        # 检查非交易时段
        for start, end in self.no_trade_ranges:
            if start <= tick_time <= end:
                return False
                
        return True
        
    def recordMarketData(self, tick: TickData):
        """记录行情数据"""
        self.price_history.append(tick.last_price)
        if len(self.price_history) > 100:
            self.price_history.pop(0)
            
    def calcIndicators(self):
        """计算技术指标"""
        if not self.kline:
            return
            
        # 动态选择参数集
        if self.params.mode == "auto" and not self.current_params:
            self.current_params = self.param_sets["A"]  # 默认参数
            
        # 计算EMA指标
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
        
        self.state.ema_fast = ema_fast[-1] if len(ema_fast) > 0 else 0
        self.state.ema_mid = ema_mid[-1] if len(ema_mid) > 0 else 0
        self.state.ema_slow = ema_slow[-1] if len(ema_slow) > 0 else 0
        
        # 计算ATR指标
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state.atr = atr[-1] if len(atr) > 0 else 0
        self.state.atr_percent = self.state.atr / self.kline.close * 100 if self.kline.close > 0 else 0
        
        # 计算动态止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            if self.params.use_fixed_stops:
                self.state.stop_loss = self.params.fixed_stop_loss
                self.state.take_profit = self.params.fixed_take_profit
            else:
                self.state.stop_loss = current_price - self.state.atr * self.current_params["stop_mult"]
                self.state.take_profit = current_price + self.state.atr * self.current_params["profit_mult"]
                
            self.state.trailing_stop = current_price - self.state.atr * self.current_params["trail_step"]
            
    def calcTrendState(self):
        """计算趋势状态"""
        if len(self.price_history) < 30:
            return
            
        # 使用线性回归判断趋势方向
        x = np.arange(len(self.price_history))
        y = np.array(self.price_history)
        slope, _, _, _, _ = linregress(x, y)
        
        # 计算ADX指标判断趋势强度
        adx = self.kline_generator.producer.adx(14)
        self.state.trend_strength = adx[-1]/100 if len(adx) > 0 else 0
        
        # 计算波动率
        returns = np.diff(np.log(self.price_history))
        self.state.volatility = np.std(returns) * np.sqrt(252)  # 年化波动率
        self.volatility_history.append(self.state.volatility)
        if len(self.volatility_history) > 20:
            self.volatility_history.pop(0)
            
        # 判断趋势状态
        is_trending = (
            abs(slope) > 0.001 and 
            self.state.trend_strength > 0.25 and 
            self.state.volatility > np.percentile(self.volatility_history, 50)
        )
        
        # 更新趋势状态
        if is_trending != self.state.is_trending:
            self.state.is_trending = is_trending
            self.state.trend_type = "A" if is_trending else "B"
            self.trend_history.append(is_trending)
            
            # 更新参数集
            self.current_params = self.param_sets[self.state.trend_type]
            
    def generateSignal(self):
        """生成交易信号"""
        if not self.tick:
            return
            
        current_price = self.tick.last_price
        price_above_fast_ema = current_price > self.state.ema_fast
        
        if self.state.is_trending:
            # 趋势行情信号
            self.buy_signal = (
                price_above_fast_ema and
                self.state.ema_fast > self.state.ema_mid > self.state.ema_slow and
                self.state.trend_strength > 0.3 and
                (current_price - self.state.ema_fast) < self.state.atr * 0.5
            )
            
            self.sell_signal = (
                not price_above_fast_ema or
                self.state.ema_fast < self.state.ema_mid or
                self.state.trend_strength < 0.2 or
                current_price <= self.state.stop_loss
            )
        else:
            # 震荡行情信号
            price_below_slow_ema = current_price < self.state.ema_slow
            
            self.buy_signal = (
                price_below_slow_ema and
                self.state.volatility < np.percentile(self.volatility_history, 75) and
                abs(self.state.ema_fast - self.state.ema_slow) < self.state.atr * 0.3
            )
            
            self.sell_signal = (
                not price_below_slow_ema and
                self.state.volatility > np.percentile(self.volatility_history, 75)
            )
            
        # 设置信号价格
        if self.params.price_type == "D1":
            self.state.signal_price = self.tick.ask_price1 if self.buy_signal else self.tick.bid_price1
        else:
            self.state.signal_price = self.tick.ask_price2 if self.buy_signal else self.tick.bid_price2
            
    def executeTrade(self):
        """执行交易"""
        position = self.getPosition(self.params.instrument_id)
        self.state.position_size = position.position if position else 0
        
        # 检查是否有未完成订单
        if self.order_id:
            self.cancelOrder(self.order_id)
            self.order_id = None
            
        # 检查最大持仓限制
        if self.state.position_size >= self.params.max_positions:
            self.buy_signal = False
            
        # 平仓逻辑
        if self.state.position_size > 0 and self.sell_signal:
            logger.info("触发平仓信号")
            self.order_id = self.sell(
                price=self.state.signal_price,
                volume=self.state.position_size,
                orderType="SELL"
            )
            self.resetState()
            
        # 开仓逻辑
        elif self.buy_signal and self.state.position_size == 0:
            logger.info("触发开仓信号")
            self.order_id = self.buy(
                price=self.state.signal_price,
                volume=self.params.order_volume,
                orderType="BUY"
            )
            self.state.entry_price = self.state.signal_price
            self.state.highest_price = self.state.signal_price
            self.state.lowest_price = self.state.signal_price
            
    def updateDynamicStops(self, current_price: float):
        """更新动态止盈止损"""
        if self.state.position_size == 0:
            return
            
        # 更新最高价和最低价
        self.state.highest_price = max(self.state.highest_price, current_price)
        self.state.lowest_price = min(self.state.lowest_price, current_price)
        
        # 计算当前盈亏
        self.state.current_profit = (current_price - self.state.entry_price) * self.state.position_size
        self.state.max_profit = max(self.state.max_profit, self.state.current_profit)
        
        # 分阶段调整止损
        profit_pct = (current_price - self.state.entry_price) / self.state.entry_price
        atr_pct = self.state.atr / self.state.entry_price
        
        if profit_pct > 0:
            if profit_pct < atr_pct:
                # 第一阶段：盈利0-1ATR，使用初始止损
                pass
            elif profit_pct < 2 * atr_pct:
                # 第二阶段：盈利1-2ATR，移动止损至保本
                self.state.stop_loss = self.state.entry_price
            else:
                # 第三阶段：盈利>2ATR，使用追踪止损
                trail_distance = self.state.atr * (
                    1.0 - 0.5 * (profit_pct / (3 * atr_pct))
                )
                self.state.stop_loss = max(
                    self.state.stop_loss,
                    current_price - trail_distance
                )
                
    def updatePerformance(self, trade: TradeData):
        """更新绩效统计"""
        # 更新交易次数
        trade_date = datetime.fromtimestamp(trade.datetime).date()
        today = datetime.now().date()
        
        if trade_date == today:
            self.state.today_trades += 1
        self.state.total_trades += 1
        
        # 更新盈亏统计
        if trade.direction == "BUY":
            self.state.entry_price = trade.price
        else:
            profit = (trade.price - self.state.entry_price) * trade.volume
            self.state.today_profit += profit
            
            if profit > 0:
                self.win_count += 1
            else:
                self.loss_count += 1
                
            # 计算胜率
            if self.win_count + self.loss_count > 0:
                self.state.win_rate = self.win_count / (self.win_count + self.loss_count)
                
            # 记录交易
            self.trade_records.append({
                "entry_time": self.last_trade_time,
                "exit_time": datetime.fromtimestamp(trade.datetime),
                "entry_price": self.state.entry_price,
                "exit_price": trade.price,
                "volume": trade.volume,
                "profit": profit
            })
            
    def updateUI(self):
        """更新界面显示"""
        # 主图指标
        main_indicators = {
            "EMA_FAST": self.state.ema_fast,
            "EMA_MID": self.state.ema_mid,
            "EMA_SLOW": self.state.ema_slow,
            "ATR": self.state.atr
        }
        
        # 副图指标
        sub_indicators = {
            "STOP_LOSS": self.state.stop_loss,
            "TAKE_PROFIT": self.state.take_profit,
            "TRAILING_STOP": self.state.trailing_stop
        }
        
        # 更新K线面板
        if self.kline:
            self.putEvent({
                "kline": self.kline,
                "signal_price": self.state.signal_price,
                **main_indicators,
                **sub_indicators
            })
            
        # 更新状态栏
        self.updateStatusBar()