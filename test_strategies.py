"""
策略测试脚本
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta

from strategy_backtester import StrategyBacktester
from original_strategy_adapter import OriginalStrategyAdapter
from optimized_strategy import OptimizedStrategy


def generate_test_data():
    """生成测试数据"""
    dates = pd.date_range(start="2023-01-01", end="2024-01-01", freq="1H")
    n = len(dates)
    
    np.random.seed(42)
    
    # 简单的价格生成
    base_price = 100
    returns = np.random.normal(0, 0.02, n)
    
    # 添加趋势
    trend = np.sin(np.arange(n) * 2 * np.pi / 100) * 0.01
    returns += trend
    
    # 生成价格序列
    prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLC数据
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    
    # 简单的高低价
    noise = np.random.uniform(0.005, 0.015, n)
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + noise)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - noise)
    data['volume'] = np.random.randint(1000, 10000, n)
    
    return data


def main():
    print("策略对比测试")
    print("=" * 50)
    
    # 生成数据
    data = generate_test_data()
    print(f"数据点: {len(data)}")
    
    # 创建策略
    original = OriginalStrategyAdapter()
    optimized = OptimizedStrategy()
    
    # 回测
    backtester = StrategyBacktester()
    
    print("\n回测原策略...")
    result1 = backtester.run_backtest(original, data)
    
    print("回测优化策略...")
    result2 = backtester.run_backtest(optimized, data)
    
    # 对比结果
    print(f"\n结果对比:")
    print(f"原策略 - 交易次数: {len([t for t in result1.trades if not t.is_open])}")
    print(f"原策略 - 胜率: {result1.win_rate:.2%}")
    print(f"原策略 - 总收益: {result1.total_return:.2%}")
    print(f"原策略 - 最大回撤: {result1.max_drawdown:.2%}")
    
    print(f"\n优化策略 - 交易次数: {len([t for t in result2.trades if not t.is_open])}")
    print(f"优化策略 - 胜率: {result2.win_rate:.2%}")
    print(f"优化策略 - 总收益: {result2.total_return:.2%}")
    print(f"优化策略 - 最大回撤: {result2.max_drawdown:.2%}")


if __name__ == "__main__":
    main()
