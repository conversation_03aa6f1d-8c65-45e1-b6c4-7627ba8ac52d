"""
最终策略对比测试
比较原策略、优化策略、Elite策略和实用优化策略
"""

import pandas as pd
import numpy as np
from strategy_backtester import StrategyBacktester
from fixed_original_strategy import FixedOriginalStrategy, FixedOptimizedStrategy
from elite_trading_strategy import EliteTradingStrategy
from optimized_practical_strategy import OptimizedPracticalStrategy
from final_enhanced_strategy import FinalEnhancedStrategy


def generate_realistic_market_data(length: int = 2000, scenario: str = "mixed") -> pd.DataFrame:
    """生成真实市场数据"""
    np.random.seed(42)
    
    base_price = 100
    returns = np.random.normal(0.0005, 0.015, length)  # 调整波动率
    
    if scenario == "trending_up":
        trend = np.linspace(0, 0.25, length)
        returns += trend / length
    elif scenario == "trending_down":
        trend = np.linspace(0, -0.15, length)
        returns += trend / length
    elif scenario == "volatile":
        returns *= 1.8
    elif scenario == "ranging":
        cycle = np.sin(np.linspace(0, 6*np.pi, length)) * 0.008
        returns += cycle / length
    
    # 生成价格
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    prices = np.array(prices[1:])
    
    # 生成OHLC
    data = pd.DataFrame()
    data['close'] = prices
    
    # 开盘价
    open_changes = np.random.normal(0, 0.003, length)
    data.loc[0, 'open'] = base_price
    for i in range(1, length):
        data.loc[i, 'open'] = data.loc[i-1, 'close'] * (1 + open_changes[i])
    
    # 高低价
    high_changes = np.random.uniform(0.002, 0.015, length)
    low_changes = np.random.uniform(-0.015, -0.002, length)
    
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + high_changes)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 + low_changes)
    
    # 成交量
    base_volume = 1000000
    volume_changes = np.random.lognormal(0, 0.4, length)
    data['volume'] = base_volume * volume_changes
    
    # 时间索引
    data.index = pd.date_range(start='2023-01-01', periods=length, freq='h')
    
    return data


def run_final_comparison():
    """运行最终对比测试"""
    print("=" * 100)
    print("最终策略性能对比测试")
    print("=" * 100)
    
    # 测试场景
    scenarios = {
        "综合市场": "mixed",
        "上涨趋势": "trending_up",
        "下跌趋势": "trending_down", 
        "高波动": "volatile",
        "震荡市场": "ranging"
    }
    
    # 策略列表
    strategies = {
        "原策略": FixedOriginalStrategy(),
        "优化策略": FixedOptimizedStrategy(),
        "Elite策略": EliteTradingStrategy(),
        "实用优化策略": OptimizedPracticalStrategy(),
        "最终增强策略": FinalEnhancedStrategy()
    }
    
    backtester = StrategyBacktester()
    all_results = {}
    
    # 运行测试
    for scenario_name, scenario_type in scenarios.items():
        print(f"\n{scenario_name} 测试结果:")
        print("-" * 80)
        
        test_data = generate_realistic_market_data(1800, scenario_type)
        scenario_results = {}
        
        for strategy_name, strategy in strategies.items():
            try:
                result = backtester.run_backtest(strategy, test_data)
                scenario_results[strategy_name] = result
                
                print(f"\n{strategy_name}:")
                print(f"  总收益: {result.total_return:.2f}%")
                print(f"  年化收益: {result.annual_return:.2f}%")
                print(f"  胜率: {result.win_rate:.2f}%")
                print(f"  夏普比率: {result.sharpe_ratio:.3f}")
                print(f"  最大回撤: {result.max_drawdown:.2f}%")
                print(f"  盈亏比: {result.profit_factor:.3f}")
                print(f"  交易次数: {result.total_trades}")
                print(f"  平均交易收益: {result.avg_trade_return:.3f}%")
                
            except Exception as e:
                print(f"\n{strategy_name}: 测试失败 - {str(e)}")
                scenario_results[strategy_name] = None
        
        all_results[scenario_name] = scenario_results
    
    # 综合评分
    print("\n" + "=" * 100)
    print("综合性能评分与排名")
    print("=" * 100)
    
    strategy_scores = calculate_comprehensive_scores(all_results)
    
    # 排名
    ranked_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
    
    print("\n策略综合排名:")
    print("-" * 50)
    for i, (strategy_name, score) in enumerate(ranked_strategies, 1):
        grade = get_performance_grade(score)
        print(f"{i}. {strategy_name}: {score:.1f}分 ({grade})")
    
    # 最佳策略分析
    best_strategy = ranked_strategies[0][0]
    print(f"\n🏆 最佳策略: {best_strategy}")
    print("-" * 50)
    
    analyze_best_strategy(all_results, best_strategy)
    
    # 改进建议
    print("\n" + "=" * 100)
    print("策略改进建议")
    print("=" * 100)
    
    provide_improvement_suggestions(all_results, strategy_scores)
    
    return all_results, strategy_scores


def calculate_comprehensive_scores(all_results):
    """计算综合评分"""
    strategy_scores = {}
    
    for strategy_name in ["原策略", "优化策略", "Elite策略", "实用优化策略", "最终增强策略"]:
        total_score = 0
        valid_scenarios = 0
        
        for scenario_results in all_results.values():
            if scenario_results[strategy_name] is not None:
                result = scenario_results[strategy_name]
                score = calculate_single_score(result)
                total_score += score
                valid_scenarios += 1
        
        avg_score = total_score / valid_scenarios if valid_scenarios > 0 else 0
        strategy_scores[strategy_name] = avg_score
    
    return strategy_scores


def calculate_single_score(result):
    """计算单个结果的评分"""
    score = 0
    
    # 收益评分 (35%)
    if result.total_return > 15:
        score += 35
    elif result.total_return > 10:
        score += 30
    elif result.total_return > 5:
        score += 25
    elif result.total_return > 0:
        score += 15
    elif result.total_return > -5:
        score += 5
    
    # 胜率评分 (25%)
    if result.win_rate > 60:
        score += 25
    elif result.win_rate > 50:
        score += 22
    elif result.win_rate > 45:
        score += 18
    elif result.win_rate > 40:
        score += 12
    elif result.win_rate > 30:
        score += 8
    
    # 夏普比率评分 (25%)
    if result.sharpe_ratio > 2.0:
        score += 25
    elif result.sharpe_ratio > 1.5:
        score += 22
    elif result.sharpe_ratio > 1.0:
        score += 18
    elif result.sharpe_ratio > 0.5:
        score += 12
    elif result.sharpe_ratio > 0:
        score += 8
    
    # 回撤评分 (15%)
    if result.max_drawdown < 3:
        score += 15
    elif result.max_drawdown < 5:
        score += 12
    elif result.max_drawdown < 8:
        score += 10
    elif result.max_drawdown < 12:
        score += 6
    elif result.max_drawdown < 20:
        score += 3
    
    return score


def get_performance_grade(score):
    """获取性能等级"""
    if score >= 80:
        return "优秀"
    elif score >= 70:
        return "良好"
    elif score >= 60:
        return "中等"
    elif score >= 50:
        return "及格"
    else:
        return "需改进"


def analyze_best_strategy(all_results, best_strategy):
    """分析最佳策略"""
    print("最佳策略特点分析:")
    
    total_return = 0
    total_trades = 0
    win_rates = []
    sharpe_ratios = []
    
    for scenario_name, scenario_results in all_results.items():
        if scenario_results[best_strategy] is not None:
            result = scenario_results[best_strategy]
            total_return += result.total_return
            total_trades += result.total_trades
            win_rates.append(result.win_rate)
            sharpe_ratios.append(result.sharpe_ratio)
    
    print(f"• 平均收益率: {total_return/len(all_results):.2f}%")
    print(f"• 平均胜率: {np.mean(win_rates):.2f}%")
    print(f"• 平均夏普比率: {np.mean(sharpe_ratios):.3f}")
    print(f"• 总交易次数: {total_trades}")
    print(f"• 平均每场景交易: {total_trades/len(all_results):.1f}次")


def provide_improvement_suggestions(all_results, strategy_scores):
    """提供改进建议"""
    best_score = max(strategy_scores.values())
    
    if best_score < 70:
        print("🔧 所有策略都需要进一步优化:")
        print("1. 加强信号过滤机制")
        print("2. 优化风险管理参数")
        print("3. 增加市场状态识别")
        print("4. 改进止损止盈逻辑")
    elif best_score < 80:
        print("📈 策略表现良好，建议微调:")
        print("1. 细化参数优化")
        print("2. 增加异常市场过滤")
        print("3. 优化交易频率")
    else:
        print("🎯 策略表现优秀，可考虑:")
        print("1. 实盘测试验证")
        print("2. 风险控制加强")
        print("3. 多品种适应性测试")
    
    print("\n通用建议:")
    print("• 建议在模拟环境中进行更长时间的测试")
    print("• 考虑加入更多市场条件的测试")
    print("• 实盘前进行小资金验证")


if __name__ == "__main__":
    try:
        results, scores = run_final_comparison()
        print(f"\n✅ 测试完成！推荐使用评分最高的策略进行实盘测试。")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
