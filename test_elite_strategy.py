"""
Elite Strategy 测试脚本
对比测试原策略、优化策略和新的Elite策略
"""

import pandas as pd
import numpy as np
from strategy_backtester import StrategyBacktester
from fixed_original_strategy import FixedOriginalStrategy, FixedOptimizedStrategy
from elite_trading_strategy import EliteTradingStrategy


def generate_realistic_test_data(length: int = 2000, scenario: str = "mixed") -> pd.DataFrame:
    """生成更真实的测试数据"""
    np.random.seed(42)
    
    # 基础价格序列
    base_price = 100
    returns = np.random.normal(0.0005, 0.02, length)  # 日收益率
    
    if scenario == "trending_up":
        trend = np.linspace(0, 0.3, length)
        returns += trend / length
    elif scenario == "trending_down":
        trend = np.linspace(0, -0.2, length)
        returns += trend / length
    elif scenario == "volatile":
        returns *= 2  # 增加波动率
    elif scenario == "ranging":
        # 添加周期性波动
        cycle = np.sin(np.linspace(0, 4*np.pi, length)) * 0.01
        returns += cycle / length
    
    # 生成价格序列
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    
    prices = np.array(prices[1:])
    
    # 生成OHLC数据
    data = pd.DataFrame()
    data['close'] = prices
    
    # 生成开盘价（前一日收盘价的小幅变动）
    open_changes = np.random.normal(0, 0.005, length)
    data['open'] = data['close'].shift(1) * (1 + open_changes)
    data['open'].iloc[0] = base_price
    
    # 生成高低价
    high_changes = np.random.uniform(0.001, 0.02, length)
    low_changes = np.random.uniform(-0.02, -0.001, length)
    
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + high_changes)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 + low_changes)
    
    # 生成成交量
    base_volume = 1000000
    volume_changes = np.random.lognormal(0, 0.5, length)
    data['volume'] = base_volume * volume_changes
    
    # 生成时间索引
    data.index = pd.date_range(start='2023-01-01', periods=length, freq='H')
    
    return data


def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 80)
    print("Elite Strategy 综合性能测试")
    print("=" * 80)
    
    # 测试场景
    scenarios = {
        "混合市场": "mixed",
        "上涨趋势": "trending_up", 
        "下跌趋势": "trending_down",
        "高波动": "volatile",
        "震荡市场": "ranging"
    }
    
    # 初始化策略
    strategies = {
        "原策略": FixedOriginalStrategy(),
        "优化策略": FixedOptimizedStrategy(),
        "Elite策略": EliteTradingStrategy()
    }
    
    backtester = StrategyBacktester()
    
    # 存储所有结果
    all_results = {}
    
    for scenario_name, scenario_type in scenarios.items():
        print(f"\n{scenario_name} 测试结果:")
        print("-" * 60)
        
        # 生成测试数据
        test_data = generate_realistic_test_data(1500, scenario_type)
        
        scenario_results = {}
        
        for strategy_name, strategy in strategies.items():
            try:
                result = backtester.run_backtest(strategy, test_data)
                scenario_results[strategy_name] = result
                
                print(f"\n{strategy_name}:")
                print(f"  总收益: {result.total_return:.2f}%")
                print(f"  胜率: {result.win_rate:.2f}%")
                print(f"  夏普比率: {result.sharpe_ratio:.3f}")
                print(f"  最大回撤: {result.max_drawdown:.2f}%")
                print(f"  盈亏比: {result.profit_factor:.3f}")
                print(f"  交易次数: {result.total_trades}")
                
            except Exception as e:
                print(f"\n{strategy_name}: 测试失败 - {str(e)}")
                scenario_results[strategy_name] = None
        
        all_results[scenario_name] = scenario_results
    
    # 生成综合评分
    print("\n" + "=" * 80)
    print("综合性能评分")
    print("=" * 80)
    
    strategy_scores = {}
    
    for strategy_name in strategies.keys():
        scores = []
        
        for scenario_name, scenario_results in all_results.items():
            if scenario_results[strategy_name] is not None:
                result = scenario_results[strategy_name]
                
                # 计算综合评分 (0-100)
                score = 0
                
                # 收益评分 (30%)
                if result.total_return > 20:
                    score += 30
                elif result.total_return > 10:
                    score += 20
                elif result.total_return > 0:
                    score += 10
                
                # 胜率评分 (25%)
                if result.win_rate > 55:
                    score += 25
                elif result.win_rate > 45:
                    score += 20
                elif result.win_rate > 35:
                    score += 15
                elif result.win_rate > 25:
                    score += 10
                
                # 夏普比率评分 (25%)
                if result.sharpe_ratio > 1.5:
                    score += 25
                elif result.sharpe_ratio > 1.0:
                    score += 20
                elif result.sharpe_ratio > 0.5:
                    score += 15
                elif result.sharpe_ratio > 0:
                    score += 10
                
                # 回撤评分 (20%)
                if result.max_drawdown < 5:
                    score += 20
                elif result.max_drawdown < 10:
                    score += 15
                elif result.max_drawdown < 15:
                    score += 10
                elif result.max_drawdown < 25:
                    score += 5
                
                scores.append(score)
        
        avg_score = np.mean(scores) if scores else 0
        strategy_scores[strategy_name] = avg_score
    
    # 打印评分结果
    sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
    
    print("\n策略排名:")
    for i, (strategy_name, score) in enumerate(sorted_strategies, 1):
        print(f"{i}. {strategy_name}: {score:.1f}分")
    
    # 性能改进分析
    print("\n" + "=" * 80)
    print("性能改进分析")
    print("=" * 80)
    
    if "Elite策略" in strategy_scores and "原策略" in strategy_scores:
        improvement = strategy_scores["Elite策略"] - strategy_scores["原策略"]
        print(f"Elite策略相比原策略改进: {improvement:.1f}分")
        
        if improvement > 20:
            print("✓ 显著改进 - 达到预期优化目标")
        elif improvement > 10:
            print("✓ 明显改进 - 优化效果良好")
        elif improvement > 0:
            print("△ 轻微改进 - 需要进一步优化")
        else:
            print("✗ 性能下降 - 需要重新设计")
    
    return all_results, strategy_scores


def analyze_best_scenarios():
    """分析最佳表现场景"""
    print("\n" + "=" * 80)
    print("最佳实践建议")
    print("=" * 80)
    
    recommendations = [
        "1. 信号质量优化:",
        "   • 提高信号评分阈值到0.7以上",
        "   • 增加多时间框架确认",
        "   • 加强成交量过滤",
        "",
        "2. 风险管理优化:",
        "   • 根据波动率动态调整止损",
        "   • 实施分批止盈策略",
        "   • 加强连续亏损控制",
        "",
        "3. 市场适应性:",
        "   • 强化市场状态识别",
        "   • 优化不同市场的参数",
        "   • 增加异常市场过滤",
        "",
        "4. 执行优化:",
        "   • 降低交易频率",
        "   • 优化入场时机",
        "   • 加强信号确认机制"
    ]
    
    for rec in recommendations:
        print(rec)


if __name__ == "__main__":
    try:
        results, scores = run_comprehensive_test()
        analyze_best_scenarios()
        
        print(f"\n测试完成！Elite策略综合评分: {scores.get('Elite策略', 0):.1f}分")
        
    except Exception as e:
        print(f"测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
