# 无限易Pro(PythonGO)量化交易策略框架

## 项目概述
这是一个基于无限易Pro(PythonGO)平台的量化交易策略框架，集成了机器学习增强功能。框架提供了完整的策略开发、回测和执行环境，特别适用于期货和衍生品交易。

## 核心功能
- 机器学习模型集成（随机森林）
- 实时特征工程和预测
- 多种技术指标计算（EMA、ATR、MACD、RSI等）
- 动态风险管理和仓位控制
- 高频Tick处理和K线分析
- 策略状态管理和持久化

## 文件结构
```
.
├── state.py                # 主策略实现(MLTrendStrategy)
├── strategy_utils.py       # 策略通用组件和工具函数
├── DemoStrategy.py         # 基础策略演示
├── DemoKC.py               # K线生成演示
├── CombinedStrategy.py     # 组合策略实现
├── EmaAtrStrategy.py       # EMA+ATR策略
├── HullMAStrategy.py       # Hull移动平均策略
├── OptionStrategy*.py      # 期权交易策略系列
└── DeepseekStrategy*.py    # 深度搜索策略系列
```

## 使用说明
1. 安装依赖：
```bash
pip install numpy pandas scikit-learn pandas-ta
```

2. 在无限易Pro平台中加载策略：
```python
from state import MLTrendStrategy

# 创建策略实例
strategy = MLTrendStrategy(ctaEngine, "MyStrategy")
```

3. 配置策略参数：
```python
# 通过无限易Pro界面配置或直接修改paramMap
strategy.paramMap = {
    'symbol': 'rb2405.SHFE',
    'timeframe': '15min',
    'ema_fast': 5,
    'ema_slow': 20,
    'ml_enabled': True
}
```

4. 启动策略：
```python
strategy.onStart()
```

## 依赖要求
- Python 3.8+
- 无限易Pro(PythonGO)平台
- numpy>=1.20
- pandas>=1.3
- scikit-learn>=1.0
- pandas-ta>=0.3

## 开发建议
1. 继承`MLTrendStrategy`类实现自定义策略
2. 使用`strategy_utils.py`中的通用组件
3. 通过重写`prepare_features()`方法添加自定义特征
4. 使用`generate_signals()`方法实现交易逻辑

## 注意事项
- 机器学习模型需要足够的历史数据训练
- 实盘前需充分回测
- 注意风险管理参数配置