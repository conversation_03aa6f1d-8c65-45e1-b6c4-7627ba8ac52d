# -*- coding: utf-8 -*-
"""
无限易Pro(PythonGO)机器学习增强版趋势策略
策略名称：ML-TrendPro
版本：2.6.1
规范：PythonGO API v3
"""

import numpy as np
import pandas as pd
from collections import deque
from dataclasses import dataclass
import pickle
import logging
from typing import Dict, List, Any, Union
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from infinipy.ctaTemplate import CtaTemplate
from infinipy.vtObject import TickData, KLineData, OrderData, TradeData
from infinipy.vtConstant import *

@dataclass
class MLModelConfig:
    """机器学习模型配置"""
    feature_window: int = 20      # 特征窗口大小
    retrain_interval: int = 100   # 重训练间隔(K线数)
    predict_period: int = 3       # 预测周期
    model_type: str = "randomforest"  # 模型类型
    confidence_threshold: float = 0.7  # 预测置信度阈值

class MLTrendStrategy(CtaTemplate):
    """机器学习增强趋势策略(无限易Pro v3规范)"""
    className = 'MLTrendStrategy'
    author = 'QuantBot AI'
    version = '2.6.1'

    # 参数映射表(无限易Pro界面配置)
    paramMap = {
        'symbol': 'rb2405.SHFE',    # 交易标的
        'timeframe': '15min',       # K线周期
        'ema_fast': 5,              # 快速EMA
        'ema_slow': 20,             # 慢速EMA
        'atr_period': 14,           # ATR周期
        'ml_enabled': True,         # 启用机器学习
        'max_pos': 10,              # 最大持仓
        'risk_ratio': 0.02          # 单笔风险比例
    }

    # 变量映射表(监控面板显示)
    varMap = {
        'trend': '趋势方向',
        'ml_signal': 'ML信号',
        'position': '持仓',
        'atr': '波动率(ATR)',
        'confidence': '预测置信度'
    }

    def __init__(self, ctaEngine, name):
        """初始化策略(严格遵循无限易Pro规范)"""
        super().__init__(ctaEngine, name)
        
        # 初始化参数
        self.ml_config = MLModelConfig()
        self.last_bar = None
        self.order_list = []
        
        # 数据容器(优化内存管理)
        self.price_queue = deque(maxlen=1000)
        self.feature_window = deque(maxlen=self.ml_config.feature_window)
        
        # 机器学习模型
        self.model = None
        self.scaler = StandardScaler()
        self.model_path = f"models/{self.name}_model.pkl"
        
        # 状态变量
        self.prediction = 0
        self.confidence = 0
        self.trend = 0
        
        # 初始化ML模型
        self.init_ml_model()

    # ------------------------ 无限易Pro必需接口 ------------------------
    def onInit(self):
        """策略初始化回调"""
        self.loadBar(self.paramMap['timeframe'])
        self.output(f"{self.name} 初始化完成 | 机器学习状态: {self.paramMap['ml_enabled']}")

    def onStart(self):
        """策略启动回调"""
        self.output(f"{self.name} 启动 | 交易标的: {self.paramMap['symbol']}")

    def onStop(self):
        """策略停止回调"""
        self.save_ml_model()
        self.output(f"{self.name} 停止 | 模型已保存")

    def onTick(self, tick: TickData):
        """Tick处理(优化版)"""
        if tick.symbol != self.paramMap['symbol']:
            return
            
        # 高频特征更新
        self.update_realtime_features(tick)
        
        # 每5个tick执行一次预测
        if len(self.price_queue) % 5 == 0 and self.paramMap['ml_enabled']:
            self.run_ml_inference()

    def onBar(self, bar: KLineData):
        """K线处理(严格遵循规范)"""
        self.last_bar = bar
        self.price_queue.append(bar.close)
        
        # 技术指标计算
        self.calculate_technical_indicators()
        
        # 机器学习训练
        if (len(self.feature_window) % self.ml_config.retrain_interval == 0 and 
            len(self.feature_window) >= self.ml_config.feature_window):
            self.train_ml_model()
            
        # 生成交易信号
        signals = self.generate_signals()
        self.execute_trading(signals)
        
        self.putEvent()  # 更新UI

    def onOrder(self, order: OrderData):
        """订单回调处理"""
        if order.status == STATUS_ALLTRADED:
            self.order_list.append(order)
        self.putEvent()

    def onTrade(self, trade: TradeData):
        """成交回调处理"""
        self.output(f"成交: {trade.direction} {trade.volume}@{trade.price}")
        self.putEvent()

    # ------------------------ 机器学习核心模块 ------------------------
    def init_ml_model(self):
        """初始化机器学习模型"""
        try:
            with open(self.model_path, 'rb') as f:
                model_data = pickle.load(f)
                self.model = model_data['model']
                self.scaler = model_data['scaler']
            self.output(f"已加载预训练模型 from {self.model_path}")
        except:
            self.model = RandomForestClassifier(
                n_estimators=100,
                max_depth=5,
                random_state=42
            )
            self.output("新建随机森林模型")

    def save_ml_model(self):
        """保存模型到文件"""
        model_data = {
            'model': self.model,
            'scaler': self.scaler,
            'config': self.ml_config
        }
        with open(self.model_path, 'wb') as f:
            pickle.dump(model_data, f)

    def prepare_features(self) -> np.ndarray:
        """准备特征数据(优化性能版)"""
        if len(self.price_queue) < self.ml_config.feature_window:
            return None
            
        # 基础价格特征
        prices = np.array(self.price_queue)[-self.ml_config.feature_window:]
        returns = np.diff(prices) / prices[:-1]
        
        # 技术指标特征
        ema_fast = ta.ema(prices, length=self.paramMap['ema_fast'])
        ema_slow = ta.ema(prices, length=self.paramMap['ema_slow'])
        macd = ta.macd(prices)
        rsi = ta.rsi(prices)
        
        # 构建特征向量
        features = np.column_stack([
            returns[-5:].mean(),    # 5周期收益率
            returns[-10:].std(),    # 10周期波动率
            ema_fast[-1] - ema_slow[-1],  # 均线差值
            macd[-1],               # MACD值
            rsi[-1]                # RSI值
        ])
        
        # 标准化处理
        return self.scaler.transform(features.reshape(1, -1))

    def train_ml_model(self):
        """训练机器学习模型(在线学习)"""
        try:
            # 准备特征和标签
            X = []
            y = []
            window_size = self.ml_config.feature_window
            predict_ahead = self.ml_config.predict_period
            
            for i in range(len(self.price_queue) - window_size - predict_ahead):
                features = self.prepare_features()
                label = 1 if (self.price_queue[i+window_size+predict_ahead] > 
                             self.price_queue[i+window_size]) else 0
                X.append(features)
                y.append(label)
            
            # 数据预处理
            X_train = np.vstack(X)
            y_train = np.array(y)
            self.scaler.fit(X_train)
            X_train_scaled = self.scaler.transform(X_train)
            
            # 模型训练
            self.model.fit(X_train_scaled, y_train)
            self.output(f"模型训练完成 | 样本数: {len(X_train)} 准确率: {self.model.score(X_train_scaled, y_train):.2f}")
            
        except Exception as e:
            self.writeError(f"模型训练失败: {str(e)}")

    def run_ml_inference(self):
        """运行机器学习预测"""
        features = self.prepare_features()
        if features is None:
            return
            
        try:
            # 预测概率
            proba = self.model.predict_proba(features)[0]
            self.prediction = np.argmax(proba)
            self.confidence = np.max(proba)
            
            # 更新状态
            self.varMap['ml_signal'] = '买入' if self.prediction == 1 else '卖出'
            self.varMap['confidence'] = f"{self.confidence:.0%}"
            
        except Exception as e:
            self.writeError(f"预测失败: {str(e)}")

    # ------------------------ 交易逻辑模块 ------------------------
    def calculate_technical_indicators(self):
        """计算技术指标(性能优化版)"""
        prices = np.array(self.price_queue)
        
        # 计算EMA
        self.ema_fast = ta.ema(prices, self.paramMap['ema_fast'])[-1]
        self.ema_slow = ta.ema(prices, self.paramMap['ema_slow'])[-1]
        
        # 计算ATR
        highs = np.array([bar.high for bar in self.barList[-self.paramMap['atr_period']:]])
        lows = np.array([bar.low for bar in self.barList[-self.paramMap['atr_period']:]])
        closes = np.array([bar.close for bar in self.barList[-self.paramMap['atr_period']:]])
        self.atr = ta.atr(highs, lows, closes, self.paramMap['atr_period'])[-1]
        
        # 更新监控变量
        self.varMap['atr'] = round(self.atr, 2)
        self.trend = 1 if self.ema_fast > self.ema_slow else -1
        self.varMap['trend'] = '上涨' if self.trend > 0 else '下跌'

    def generate_signals(self) -> Dict[str, bool]:
        """生成综合交易信号"""
        signals = {
            'buy': False,
            'sell': False,
            'stop_loss': False
        }
        
        # 机器学习信号条件
        ml_condition = (
            self.paramMap['ml_enabled'] and 
            self.confidence > self.ml_config.confidence_threshold
        )
        
        # 买入信号(趋势+ML确认)
        signals['buy'] = all([
            self.trend > 0,
            ml_condition and self.prediction == 1,
            len(self.position) < self.paramMap['max_pos']
        ])
        
        # 卖出信号(趋势反转或ML预测)
        signals['sell'] = any([
            self.trend < 0,
            ml_condition and self.prediction == 0,
            len(self.position) > 0 and self.check_stop_loss()
        ])
        
        return signals

    def execute_trading(self, signals: Dict[str, bool]):
        """执行交易(严格遵循风控规则)"""
        symbol = self.paramMap['symbol']
        exchange, instrument = symbol.split('.')
        
        # 买入逻辑
        if signals['buy'] and not self.position.get(symbol, 0):
            size = self.calculate_position_size()
            order_id = self.buy(exchange, instrument, self.last_bar.close, size)
            self.output(f"买入信号 | 价格:{self.last_bar.close} 数量:{size} 订单ID:{order_id}")
        
        # 卖出逻辑
        elif signals['sell'] and self.position.get(symbol, 0) > 0:
            position_size = self.position[symbol]
            order_id = self.sell(exchange, instrument, self.last_bar.close, position_size)
            self.output(f"卖出信号 | 价格:{self.last_bar.close} 数量:{position_size} 订单ID:{order_id}")

    def calculate_position_size(self) -> int:
        """计算动态仓位(基于风险控制)"""
        account = self.getAccount(self.paramMap['symbol'].split('.')[0])
        if not account:
            return 1
            
        risk_capital = account.available * self.paramMap['risk_ratio']
        risk_per_unit = self.atr * 2  # 2倍ATR作为风险单位
        size = int(risk_capital / risk_per_unit)
        
        return min(size, self.paramMap['max_pos'])

    def check_stop_loss(self) -> bool:
        """检查止损条件"""
        if not self.position or not self.last_bar:
            return False
            
        entry_price = self.position[self.paramMap['symbol']]['price']
        current_price = self.last_bar.close
        return current_price < entry_price - 2 * self.atr

    # ------------------------ 策略管理接口 ------------------------
    def getStrategyConfig(self) -> Dict[str, Any]:
        """获取策略配置(无限易Pro规范)"""
        return {
            'base': {
                'className': self.className,
                'author': self.author,
                'version': self.version,
                'desc': '机器学习增强趋势策略',
                'category': 'ML/Trend'
            },
            'params': self.paramMap,
            'vars': self.varMap,
            'requirements': [
                'numpy>=1.20',
                'pandas>=1.3',
                'scikit-learn>=1.0',
                'pandas-ta>=0.3'
            ]
        }

    def backtestReport(self) -> Dict[str, Any]:
        """生成回测报告(无限易Pro规范)"""
        return {
            'performance': {
                'total_return': self.bt_results.get('total_return', 0),
                'sharpe': self.bt_results.get('sharpe', 0),
                'max_dd': self.bt_results.get('max_drawdown', 0)
            },
            'trades': self.bt_results.get('trades', []),
            'ml_metrics': {
                'accuracy': self.bt_results.get('ml_accuracy', 0),
                'feature_importance': self.get_feature_importance()
            }
        }

    def get_feature_importance(self) -> Dict[str, float]:
        """获取特征重要性"""
        if hasattr(self.model, 'feature_importances_'):
            return {
                'returns': self.model.feature_importances_[0],
                'volatility': self.model.feature_importances_[1],
                'ema_diff': self.model.feature_importances_[2],
                'macd': self.model.feature_importances_[3],
                'rsi': self.model.feature_importances_[4]
            }
        return {}

# 策略工厂函数(无限易Pro规范)
def createStrategy(ctaEngine, name):
    return MLTrendStrategy(ctaEngine, name)