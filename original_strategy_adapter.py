"""
原策略回测适配器
将OptionStrategy2转换为可回测的格式
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from strategy_backtester import BaseBacktestStrategy
from datetime import datetime


class OriginalStrategyAdapter(BaseBacktestStrategy):
    """原策略回测适配器"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 策略参数 (对应OptionStrategy2的参数)
        self.atr_period = 14
        self.stop_mult = 2.2
        self.profit_mult = 3.0
        self.trail_step = 1.0
        self.max_positions = 5
        self.order_volume = 1
        
        # 趋势判断参数
        self.trend_period = 15
        self.trend_threshold = 0.6
        self.volatility_threshold = 0.5
        self.min_trend_duration = 5
        self.max_trend_duration = 30
        
        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "ema": [5, 15, 30],
                "atr_period": 14,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "ema": [3, 10, 20],
                "atr_period": 21,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        
        # 状态变量
        self.current_params = self.param_sets["A"]
        self.trend_type = "A"
        self.is_trending = False
        self.trend_strength = 0.0
        self.trend_duration = 0
        self.price_history = []
        self.trend_history = []
        self.volatility_history = []
        
        # 动态止盈止损
        self.entry_price = 0
        self.highest_price = 0
        self.lowest_price = 0
        self.is_trailing = False
        self.stop_loss = 0
        self.take_profit = 0
        
    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """计算EMA"""
        return data.ewm(span=period, adjust=False).mean()
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def calc_trend(self, data: pd.DataFrame, index: int) -> None:
        """计算趋势状态"""
        if index < self.trend_period:
            return
            
        # 获取价格历史
        close_prices = data['close'].iloc[max(0, index-self.trend_period):index+1].values
        
        if len(close_prices) == self.trend_period + 1:
            # 计算价格变化率
            price_changes = np.diff(close_prices)
            
            # 计算方向一致性
            direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
            
            # 计算波动率
            volatility = np.std(price_changes)
            
            # 计算趋势强度 (使用EMA斜率)
            ema_slopes = []
            for period in [5, 10, 20]:
                if index >= period:
                    ema_values = data[f'ema_{period}'].iloc[index-1:index+1].values
                    if len(ema_values) == 2 and ema_values[0] > 0:
                        slope = (ema_values[1] - ema_values[0]) / ema_values[0]
                        ema_slopes.append(slope)
            
            if ema_slopes:
                self.trend_strength = min(1.0, abs(np.mean(ema_slopes)))
            
            # 判断趋势
            is_trending = (
                direction_consistency > self.trend_threshold and
                volatility > self.volatility_threshold and
                self.trend_strength > 0.3
            )
            
            # 更新趋势状态
            if is_trending != self.is_trending:
                self.is_trending = is_trending
                self.trend_type = "A" if is_trending else "B"
                self.current_params = self.param_sets[self.trend_type]
                self.trend_duration = 0
            else:
                self.trend_duration += 1
    
    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损"""
        if len([t for t in self.trades if t.is_open]) > 0:
            # 更新最高价和最低价
            if current_price > self.highest_price:
                self.highest_price = current_price
            if current_price < self.lowest_price or self.lowest_price == 0:
                self.lowest_price = current_price
            
            # 计算当前盈亏
            open_trade = [t for t in self.trades if t.is_open][-1]
            current_profit = (current_price - open_trade.entry_price) * open_trade.quantity
            
            # 动态调整止损
            atr_value = self.current_atr if hasattr(self, 'current_atr') else 0
            
            if current_profit > 0:
                # 当盈利超过ATR的1倍时，启动追踪止损
                if not self.is_trailing and current_profit > atr_value:
                    self.is_trailing = True
                
                if self.is_trailing:
                    # 使用最高价回撤ATR的倍数作为止损
                    self.stop_loss = self.highest_price - atr_value * self.current_params["stop_mult"]
            else:
                # 未盈利时使用初始止损
                self.stop_loss = open_trade.entry_price - atr_value * self.current_params["stop_mult"]
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算技术指标
        data['ema_5'] = self.calculate_ema(data['close'], 5)
        data['ema_15'] = self.calculate_ema(data['close'], 15)
        data['ema_30'] = self.calculate_ema(data['close'], 30)
        data['ema_3'] = self.calculate_ema(data['close'], 3)
        data['ema_10'] = self.calculate_ema(data['close'], 10)
        data['ema_20'] = self.calculate_ema(data['close'], 20)
        
        data['atr'] = self.calculate_atr(data['high'], data['low'], data['close'], self.atr_period)
        
        # 初始化信号列
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        signals['confidence'] = 0.0
        
        # 逐行生成信号
        for i in range(len(data)):
            if i < max(30, self.trend_period):  # 确保有足够的历史数据
                continue
                
            # 计算趋势
            self.calc_trend(data, i)
            
            # 获取当前指标值
            if self.trend_type == "A":  # 趋势型
                ema_fast = data['ema_5'].iloc[i]
                ema_mid = data['ema_15'].iloc[i]
                ema_slow = data['ema_30'].iloc[i]
            else:  # 震荡型
                ema_fast = data['ema_3'].iloc[i]
                ema_mid = data['ema_10'].iloc[i]
                ema_slow = data['ema_20'].iloc[i]
            
            self.current_atr = data['atr'].iloc[i]
            current_price = data['close'].iloc[i]
            
            # 更新动态止损
            self.update_dynamic_stops(current_price)
            
            # 检查当前持仓
            open_positions = len([t for t in self.trades if t.is_open])
            
            # 生成交易信号
            if self.is_trending:
                # 趋势型交易信号
                buy_condition = (
                    ema_fast > ema_mid > ema_slow and
                    self.trend_strength > 0.4 and
                    open_positions < self.max_positions
                )
                
                sell_condition = (
                    ema_fast < ema_mid < ema_slow or
                    self.trend_strength < 0.2 or
                    (open_positions > 0 and current_price <= self.stop_loss)
                )
            else:
                # 震荡型交易信号
                buy_condition = (
                    ema_fast < ema_mid and
                    ema_mid > ema_slow and
                    open_positions < self.max_positions
                )
                
                sell_condition = (
                    ema_fast > ema_mid and
                    ema_mid < ema_slow or
                    (open_positions > 0 and current_price <= self.stop_loss)
                )
            
            # 设置信号
            if buy_condition and open_positions == 0:
                signals.loc[data.index[i], 'action'] = 'buy'
                signals.loc[data.index[i], 'reason'] = f'{self.trend_type}_trend_buy'
                signals.loc[data.index[i], 'confidence'] = self.trend_strength

                # 设置止盈止损
                self.entry_price = current_price
                self.highest_price = current_price
                self.lowest_price = current_price
                self.is_trailing = False
                self.stop_loss = current_price - self.current_atr * self.current_params["stop_mult"]
                self.take_profit = current_price + self.current_atr * self.current_params["profit_mult"]

            elif sell_condition and open_positions > 0:
                signals.loc[data.index[i], 'action'] = 'sell'
                if current_price <= self.stop_loss:
                    signals.loc[data.index[i], 'reason'] = 'stop_loss'
                elif current_price >= self.take_profit:
                    signals.loc[data.index[i], 'reason'] = 'take_profit'
                else:
                    signals.loc[data.index[i], 'reason'] = f'{self.trend_type}_trend_sell'
                signals.loc[data.index[i], 'confidence'] = 1.0

            # 强制止盈止损检查（确保有退出机制）
            elif open_positions > 0:
                if (current_price <= self.stop_loss or
                    current_price >= self.take_profit):
                    signals.loc[data.index[i], 'action'] = 'sell'
                    if current_price <= self.stop_loss:
                        signals.loc[data.index[i], 'reason'] = 'force_stop_loss'
                    else:
                        signals.loc[data.index[i], 'reason'] = 'force_take_profit'
                    signals.loc[data.index[i], 'confidence'] = 1.0
        
        return signals
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            # 简单的固定仓位
            return self.order_volume
        return 0


def generate_sample_data(start_date: str = "2023-01-01", end_date: str = "2024-01-01", 
                        freq: str = "1H") -> pd.DataFrame:
    """生成示例数据用于测试"""
    dates = pd.date_range(start=start_date, end=end_date, freq=freq)
    
    # 生成模拟价格数据
    np.random.seed(42)
    n = len(dates)
    
    # 基础价格趋势
    base_price = 100
    trend = np.cumsum(np.random.normal(0, 0.001, n))
    
    # 添加波动性
    volatility = np.random.normal(0, 0.02, n)
    
    # 生成价格序列
    close_prices = base_price * (1 + trend + volatility)
    
    # 生成OHLC数据
    data = pd.DataFrame(index=dates)
    data['close'] = close_prices
    
    # 简单的OHLC生成
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + np.random.uniform(0, 0.01, n))
    data['low'] = data[['open', 'close']].min(axis=1) * (1 - np.random.uniform(0, 0.01, n))
    data['volume'] = np.random.randint(1000, 10000, n)
    
    return data


if __name__ == "__main__":
    # 测试代码
    from strategy_backtester import StrategyBacktester
    
    # 生成测试数据
    data = generate_sample_data()
    print(f"生成测试数据: {len(data)} 条记录")
    
    # 创建策略实例
    original_strategy = OriginalStrategyAdapter()
    
    # 创建回测器
    backtester = StrategyBacktester()
    
    # 运行回测
    print("开始回测原策略...")
    result = backtester.run_backtest(original_strategy, data)
    
    # 输出结果
    print(f"总交易次数: {len([t for t in result.trades if not t.is_open])}")
    print(f"胜率: {result.win_rate:.2%}")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.3f}")
