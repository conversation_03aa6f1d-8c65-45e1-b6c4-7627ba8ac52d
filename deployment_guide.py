"""
策略部署指南和使用示例
Production Ready Strategy 部署和使用说明
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from production_ready_strategy import ProductionReadyStrategy


def create_sample_data():
    """创建示例数据"""
    # 生成示例OHLCV数据
    np.random.seed(42)
    length = 1000
    base_price = 100
    
    # 生成价格序列
    returns = np.random.normal(0.0005, 0.015, length)
    prices = [base_price]
    for ret in returns:
        prices.append(prices[-1] * (1 + ret))
    prices = np.array(prices[1:])
    
    # 创建OHLCV数据
    data = pd.DataFrame()
    data['close'] = prices
    
    # 生成开盘价
    data['open'] = data['close'].shift(1).fillna(base_price)
    
    # 生成高低价
    high_noise = np.random.uniform(0.002, 0.01, length)
    low_noise = np.random.uniform(-0.01, -0.002, length)
    data['high'] = data[['open', 'close']].max(axis=1) * (1 + high_noise)
    data['low'] = data[['open', 'close']].min(axis=1) * (1 + low_noise)
    
    # 生成成交量
    base_volume = 1000000
    volume_noise = np.random.lognormal(0, 0.3, length)
    data['volume'] = base_volume * volume_noise
    
    # 设置时间索引
    start_time = datetime.now() - timedelta(hours=length)
    data.index = pd.date_range(start=start_time, periods=length, freq='H')
    
    return data


def run_strategy_demo():
    """运行策略演示"""
    print("=" * 80)
    print("策略部署演示")
    print("=" * 80)
    
    # 创建策略实例
    strategy = ProductionReadyStrategy(initial_capital=100000)
    
    # 获取示例数据
    data = create_sample_data()
    print(f"✅ 加载数据: {len(data)}条记录")
    
    # 计算技术指标
    data = strategy.calculate_indicators(data)
    print("✅ 计算技术指标完成")
    
    # 模拟实时交易
    print("\n开始模拟交易...")
    print("-" * 50)
    
    signals_generated = 0
    trades_executed = 0
    
    # 从第30条数据开始（确保指标计算完整）
    for i in range(30, len(data)):
        current_data = data.iloc[i]
        timestamp = data.index[i]
        current_price = current_data['close']
        
        # 生成信号
        signal = strategy.generate_signal(current_data, timestamp)
        
        if signal['action'] != 'hold':
            signals_generated += 1
            print(f"[{timestamp.strftime('%Y-%m-%d %H:%M')}] 信号: {signal['action']} @ {current_price:.2f} - {signal['reason']}")
            
            # 执行交易
            if strategy.execute_trade(signal, current_price, timestamp):
                trades_executed += 1
        
        # 更新资金曲线
        strategy.equity_curve.append(strategy.current_capital)
        strategy.timestamps.append(timestamp)
    
    print(f"\n✅ 模拟完成")
    print(f"信号生成: {signals_generated}次")
    print(f"交易执行: {trades_executed}次")
    
    # 显示性能统计
    stats = strategy.get_performance_stats()
    if stats:
        print("\n" + "=" * 50)
        print("性能统计")
        print("=" * 50)
        print(f"总收益率: {stats['total_return']:.2f}%")
        print(f"总交易次数: {stats['total_trades']}")
        print(f"获利交易: {stats['winning_trades']}")
        print(f"胜率: {stats['win_rate']:.2f}%")
        print(f"盈亏比: {stats['profit_factor']:.2f}")
        print(f"当前资金: {stats['current_capital']:.2f}")
        print(f"平均盈利: {stats['avg_profit']:.2f}")
        print(f"平均亏损: {stats['avg_loss']:.2f}")
    
    return strategy


def print_deployment_guide():
    """打印部署指南"""
    print("\n" + "=" * 80)
    print("实盘部署指南")
    print("=" * 80)
    
    guide = [
        "📋 部署前准备:",
        "1. 确保有稳定的数据源（实时OHLCV数据）",
        "2. 配置交易接口（券商API或交易平台）",
        "3. 设置监控和报警系统",
        "4. 准备日志存储和分析工具",
        "",
        "🔧 配置步骤:",
        "1. 调整initial_capital为实际资金量",
        "2. 根据风险承受能力调整max_position_size",
        "3. 设置合适的max_daily_trades限制",
        "4. 配置emergency_stop_loss紧急止损",
        "",
        "⚠️ 风险控制:",
        "• 建议先用小资金（1-5万）进行实盘验证",
        "• 密切监控前100笔交易的表现",
        "• 设置资金回撤警戒线（如20%）",
        "• 定期检查和调整参数",
        "",
        "📊 监控指标:",
        "• 实时胜率和盈亏比",
        "• 最大回撤和连续亏损",
        "• 交易频率和信号质量",
        "• 滑点和手续费影响",
        "",
        "🚨 异常处理:",
        "• 网络中断时的持仓保护",
        "• 数据异常时的策略暂停",
        "• 大幅波动时的风险控制",
        "• 系统故障时的应急预案",
        "",
        "📈 优化建议:",
        "• 根据实盘表现微调参数",
        "• 考虑加入更多市场过滤条件",
        "• 优化入场和出场时机",
        "• 定期回测和策略评估"
    ]
    
    for line in guide:
        print(line)


def create_integration_example():
    """创建集成示例代码"""
    example_code = '''
# 实盘集成示例代码
from production_ready_strategy import ProductionReadyStrategy
import pandas as pd
from datetime import datetime

class TradingBot:
    def __init__(self):
        self.strategy = ProductionReadyStrategy(initial_capital=50000)  # 5万初始资金
        self.is_running = True
        
    def get_market_data(self):
        """获取实时市场数据 - 需要连接实际数据源"""
        # 这里需要连接你的数据源
        # 返回包含OHLCV的DataFrame
        pass
    
    def execute_order(self, action, price, shares):
        """执行实际交易 - 需要连接交易接口"""
        # 这里需要连接你的交易接口
        # 执行买入或卖出操作
        pass
    
    def run_trading_loop(self):
        """主交易循环"""
        while self.is_running:
            try:
                # 获取最新数据
                current_data = self.get_market_data()
                
                if current_data is not None:
                    # 计算指标
                    data_with_indicators = self.strategy.calculate_indicators(current_data)
                    latest_data = data_with_indicators.iloc[-1]
                    
                    # 生成信号
                    signal = self.strategy.generate_signal(latest_data, datetime.now())
                    
                    # 执行交易
                    if signal['action'] in ['buy', 'sell']:
                        current_price = latest_data['close']
                        if self.strategy.execute_trade(signal, current_price, datetime.now()):
                            # 执行实际订单
                            shares = self.strategy.current_position['shares'] if signal['action'] == 'sell' else 1
                            self.execute_order(signal['action'], current_price, shares)
                
                # 等待下一个周期
                time.sleep(60)  # 每分钟检查一次
                
            except Exception as e:
                print(f"交易循环错误: {e}")
                # 记录错误并继续运行
    
    def stop_trading(self):
        """停止交易"""
        self.is_running = False
        print("交易已停止")

# 使用示例
if __name__ == "__main__":
    bot = TradingBot()
    bot.run_trading_loop()
'''
    
    print("\n" + "=" * 80)
    print("集成示例代码")
    print("=" * 80)
    print(example_code)


def main():
    """主函数"""
    print("Production Ready Strategy - 部署指南和演示")
    
    # 运行策略演示
    strategy = run_strategy_demo()
    
    # 显示部署指南
    print_deployment_guide()
    
    # 显示集成示例
    create_integration_example()
    
    print("\n" + "=" * 80)
    print("✅ 策略已准备就绪，可以开始实盘部署！")
    print("⚠️  请务必先进行小资金验证，确保策略稳定性。")
    print("=" * 80)


if __name__ == "__main__":
    main()
