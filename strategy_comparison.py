"""
策略对比测试脚本
对比原策略和优化策略的性能表现
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

from strategy_backtester import StrategyBacktester
from original_strategy_adapter import OriginalStrategyAdapter, generate_sample_data
from optimized_strategy import OptimizedStrategy


def generate_realistic_data(start_date: str = "2023-01-01", end_date: str = "2024-01-01", 
                           freq: str = "1H") -> pd.DataFrame:
    """生成更真实的市场数据"""
    dates = pd.date_range(start=start_date, end=end_date, freq=freq)
    n = len(dates)
    
    np.random.seed(42)
    
    # 创建多种市场状态
    base_price = 100
    
    # 趋势组件
    trend_segments = max(1, n//100)
    trend_changes = np.random.choice([-1, 0, 1], size=trend_segments, p=[0.3, 0.4, 0.3])
    trend = np.repeat(trend_changes, n//trend_segments)
    # 确保长度匹配
    if len(trend) < n:
        trend = np.concatenate([trend, np.repeat(trend[-1], n - len(trend))])
    elif len(trend) > n:
        trend = trend[:n]
    trend = np.cumsum(trend) * 0.001

    # 波动率组件（时变）
    vol_segments = max(1, n//50)
    volatility_regime = np.random.choice([0.5, 1.0, 2.0], size=vol_segments, p=[0.4, 0.4, 0.2])
    volatility = np.repeat(volatility_regime, n//vol_segments)
    # 确保长度匹配
    if len(volatility) < n:
        volatility = np.concatenate([volatility, np.repeat(volatility[-1], n - len(volatility))])
    elif len(volatility) > n:
        volatility = volatility[:n]
    
    # 价格变化
    returns = np.random.normal(0, 1, n) * volatility * 0.01 + trend
    
    # 添加跳跃（模拟突发事件）
    jump_prob = 0.001
    jumps = np.random.binomial(1, jump_prob, n) * np.random.normal(0, 0.05, n)
    returns += jumps
    
    # 生成价格序列
    prices = base_price * np.exp(np.cumsum(returns))
    
    # 生成OHLC数据
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    
    # 更真实的OHLC生成
    intraday_vol = np.random.uniform(0.005, 0.02, n)
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    
    # 高低价基于开盘和收盘价
    for i in range(n):
        if i == 0:
            data.loc[data.index[i], 'high'] = data['close'].iloc[i] * (1 + intraday_vol[i])
            data.loc[data.index[i], 'low'] = data['close'].iloc[i] * (1 - intraday_vol[i])
        else:
            open_price = data['open'].iloc[i]
            close_price = data['close'].iloc[i]
            high_price = max(open_price, close_price) * (1 + intraday_vol[i] * 0.5)
            low_price = min(open_price, close_price) * (1 - intraday_vol[i] * 0.5)
            data.loc[data.index[i], 'high'] = high_price
            data.loc[data.index[i], 'low'] = low_price
    
    data['volume'] = np.random.lognormal(8, 1, n).astype(int)
    
    return data


def run_comprehensive_comparison():
    """运行全面的策略对比测试"""
    print("=" * 60)
    print("策略对比测试开始")
    print("=" * 60)
    
    # 生成测试数据
    print("生成测试数据...")
    data = generate_realistic_data(start_date="2023-01-01", end_date="2024-01-01", freq="1H")
    print(f"数据范围: {data.index[0]} 到 {data.index[-1]}")
    print(f"总数据点: {len(data)}")
    print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
    
    # 创建策略实例
    print("\n创建策略实例...")
    strategies = {
        "原策略": OriginalStrategyAdapter(initial_capital=100000),
        "优化策略": OptimizedStrategy(initial_capital=100000)
    }
    
    # 创建回测器
    backtester = StrategyBacktester(commission=0.0002, slippage=0.0001)
    
    # 运行回测
    print("\n开始回测...")
    results = {}
    
    for name, strategy in strategies.items():
        print(f"回测 {name}...")
        try:
            result = backtester.run_backtest(strategy, data)
            results[name] = result
            print(f"  - 完成，交易次数: {len([t for t in result.trades if not t.is_open])}")
        except Exception as e:
            print(f"  - 错误: {e}")
            continue
    
    if not results:
        print("没有成功的回测结果")
        return
    
    # 生成详细报告
    print("\n" + "=" * 60)
    print("回测结果分析")
    print("=" * 60)
    
    report = backtester.generate_report(results)
    print(report)
    
    # 详细对比分析
    print("\n" + "=" * 60)
    print("详细对比分析")
    print("=" * 60)
    
    if len(results) >= 2:
        original_result = results["原策略"]
        optimized_result = results["优化策略"]
        
        print("关键指标对比:")
        print("-" * 40)
        
        metrics = [
            ("总收益率", "total_return", "%"),
            ("年化收益率", "annual_return", "%"),
            ("夏普比率", "sharpe_ratio", ""),
            ("最大回撤", "max_drawdown", "%"),
            ("胜率", "win_rate", "%"),
            ("盈亏比", "profit_factor", ""),
            ("Calmar比率", "calmar_ratio", ""),
            ("Sortino比率", "sortino_ratio", ""),
        ]
        
        for metric_name, metric_attr, unit in metrics:
            orig_val = getattr(original_result, metric_attr)
            opt_val = getattr(optimized_result, metric_attr)
            
            if unit == "%":
                orig_str = f"{orig_val:.2%}"
                opt_str = f"{opt_val:.2%}"
                improvement = (opt_val - orig_val) * 100
                imp_str = f"{improvement:+.2f}pp"
            else:
                orig_str = f"{orig_val:.3f}"
                opt_str = f"{opt_val:.3f}"
                if orig_val != 0:
                    improvement = (opt_val - orig_val) / abs(orig_val) * 100
                    imp_str = f"{improvement:+.1f}%"
                else:
                    imp_str = "N/A"
            
            print(f"{metric_name:12s}: 原策略 {orig_str:>8s} | 优化策略 {opt_str:>8s} | 改进 {imp_str:>8s}")
        
        # 交易分析
        print("\n交易行为分析:")
        print("-" * 40)
        
        orig_trades = [t for t in original_result.trades if not t.is_open]
        opt_trades = [t for t in optimized_result.trades if not t.is_open]
        
        print(f"交易次数:     原策略 {len(orig_trades):>8d} | 优化策略 {len(opt_trades):>8d}")
        
        if orig_trades:
            orig_avg_duration = np.mean([t.duration.total_seconds()/3600 for t in orig_trades if t.duration])
            orig_win_trades = [t for t in orig_trades if t.pnl > 0]
            orig_loss_trades = [t for t in orig_trades if t.pnl < 0]
            orig_avg_win = np.mean([t.pnl for t in orig_win_trades]) if orig_win_trades else 0
            orig_avg_loss = np.mean([t.pnl for t in orig_loss_trades]) if orig_loss_trades else 0
            
            print(f"平均持仓时间: 原策略 {orig_avg_duration:>8.1f}h")
            print(f"平均盈利:     原策略 {orig_avg_win:>8.2f}")
            print(f"平均亏损:     原策略 {orig_avg_loss:>8.2f}")
        
        if opt_trades:
            opt_avg_duration = np.mean([t.duration.total_seconds()/3600 for t in opt_trades if t.duration])
            opt_win_trades = [t for t in opt_trades if t.pnl > 0]
            opt_loss_trades = [t for t in opt_trades if t.pnl < 0]
            opt_avg_win = np.mean([t.pnl for t in opt_win_trades]) if opt_win_trades else 0
            opt_avg_loss = np.mean([t.pnl for t in opt_loss_trades]) if opt_loss_trades else 0
            
            print(f"                     | 优化策略 {opt_avg_duration:>8.1f}h")
            print(f"                     | 优化策略 {opt_avg_win:>8.2f}")
            print(f"                     | 优化策略 {opt_avg_loss:>8.2f}")
    
    # 绘制对比图表
    print("\n生成对比图表...")
    try:
        backtester.plot_results(results, save_path="strategy_comparison.png")
        print("图表已保存为 strategy_comparison.png")
    except Exception as e:
        print(f"绘图错误: {e}")
    
    # 风险分析
    print("\n" + "=" * 60)
    print("风险分析")
    print("=" * 60)
    
    for name, result in results.items():
        print(f"\n{name}:")
        print(f"  95% VaR: {result.var_95:.2%}")
        print(f"  最大连续亏损: {result.max_consecutive_losses}")
        
        # 计算月度收益率分布
        if len(result.equity_curve) > 30:
            monthly_returns = []
            equity_array = np.array(result.equity_curve)
            for i in range(0, len(equity_array)-30, 30):
                if i+30 < len(equity_array):
                    monthly_ret = (equity_array[i+30] - equity_array[i]) / equity_array[i]
                    monthly_returns.append(monthly_ret)
            
            if monthly_returns:
                print(f"  月度收益率标准差: {np.std(monthly_returns):.2%}")
                print(f"  最差月度收益: {min(monthly_returns):.2%}")
                print(f"  最佳月度收益: {max(monthly_returns):.2%}")
    
    print("\n" + "=" * 60)
    print("测试完成")
    print("=" * 60)
    
    return results


def analyze_trade_patterns(results):
    """分析交易模式"""
    print("\n交易模式分析:")
    print("-" * 40)
    
    for name, result in results.items():
        trades = [t for t in result.trades if not t.is_open]
        if not trades:
            continue
            
        print(f"\n{name}:")
        
        # 按退出原因分类
        exit_reasons = {}
        for trade in trades:
            reason = trade.exit_reason
            if reason not in exit_reasons:
                exit_reasons[reason] = []
            exit_reasons[reason].append(trade)
        
        for reason, reason_trades in exit_reasons.items():
            count = len(reason_trades)
            avg_pnl = np.mean([t.pnl for t in reason_trades])
            win_rate = len([t for t in reason_trades if t.pnl > 0]) / count
            print(f"  {reason}: {count}次, 平均盈亏: {avg_pnl:.2f}, 胜率: {win_rate:.1%}")


if __name__ == "__main__":
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 运行对比测试
    results = run_comprehensive_comparison()
    
    if results:
        # 分析交易模式
        analyze_trade_patterns(results)
        
        print("\n建议:")
        print("-" * 40)
        
        if len(results) >= 2:
            original_result = results["原策略"]
            optimized_result = results["优化策略"]
            
            if optimized_result.sharpe_ratio > original_result.sharpe_ratio:
                print("✓ 优化策略在风险调整收益方面表现更好")
            
            if optimized_result.max_drawdown < original_result.max_drawdown:
                print("✓ 优化策略的最大回撤更小，风险控制更好")
            
            if optimized_result.win_rate > original_result.win_rate:
                print("✓ 优化策略的胜率更高")
            
            if optimized_result.profit_factor > original_result.profit_factor:
                print("✓ 优化策略的盈亏比更好")
            
            print("\n进一步优化建议:")
            print("1. 考虑加入更多市场状态识别指标")
            print("2. 优化仓位管理策略")
            print("3. 加入更精细的风险控制机制")
            print("4. 考虑交易成本的影响")
            print("5. 在不同市场环境下测试策略稳健性")
