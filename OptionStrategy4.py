from typing import Literal

import numpy as np

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # VWAP参数
    vwap_period: int = Field(default=30, title="VWAP周期", ge=20, le=60)  # 新增VWAP参数
    vwap_deviation: float = Field(default=1.0, title="VWAP偏差阈值", ge=0.5, le=2.0)  # 新增VWAP偏差参数


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # VWAP指标
    vwap: float = Field(default=0, title="成交量加权平均价")
    vwap_upper: float = Field(default=0, title="VWAP上轨")
    vwap_lower: float = Field(default=0, title="VWAP下轨")


class OptionStrategy4(BaseStrategy):
    """三周期EMA+ATR买方策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 15  # 趋势判断周期
        self.trend_count = 0
        self.price_history = []
        self.trend_history = []  # 记录历史趋势状态
        self.volatility_history = []  # 记录历史波动率
        
        # 趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.5  # 波动率阈值
        self.min_trend_duration = 5  # 最小趋势持续周期
        self.max_trend_duration = 30  # 最大趋势持续周期
        
        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "ema": [5, 15, 30],
                "atr_period": 14,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "ema": [3, 10, 20],
                "atr_period": 21,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "EMA_FAST": self.state_map.ema_fast,
            "EMA_MID": self.state_map.ema_mid,
            "EMA_SLOW": self.state_map.ema_slow,
            "ATR": self.state_map.atr
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop,
            "VWAP": self.state_map.vwap,
            "VWAP_UPPER": self.state_map.vwap_upper,
            "VWAP_LOWER": self.state_map.vwap_lower
        }

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None

    def on_start(self):
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.price_history = []
        self.trend_count = 0
        
        # 初始化动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        
        # 初始化参数
        if self.params_map.mode == "manual":
            self.current_params = {
                "ema": [5, 15, 30],  # 默认EMA参数
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = None  # 自动模式等待趋势判断

        self.update_status_bar()

    def on_stop(self):
        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标和信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_signal(kline)

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        # 更新价格历史
        self.price_history.append(kline.close)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)
            
        if len(self.price_history) == self.trend_period:
            # 1. 计算价格变化率
            price_changes = np.diff(self.price_history)
            
            # 2. 计算方向一致性 - 增加权重系统
            recent_changes = price_changes[-5:]  # 最近5个周期的变化
            older_changes = price_changes[:-5]   # 较早的周期变化
            
            # 赋予最近变化更高的权重
            direction_consistency = (
                0.7 * abs(np.sum(np.sign(recent_changes))) / len(recent_changes) +
                0.3 * abs(np.sum(np.sign(older_changes))) / len(older_changes)
            )
            
            # 3. 计算波动率 - 使用指数加权标准差
            weights = np.exp(np.linspace(-1, 0, len(price_changes)))
            weights /= weights.sum()
            weighted_variance = np.sum(weights * (price_changes - np.mean(price_changes))**2)
            volatility = np.sqrt(weighted_variance)
            
            self.volatility_history.append(volatility)
            if len(self.volatility_history) > 10:
                self.volatility_history.pop(0)
            
            # 4. 计算趋势强度 - 增强版
            ema_slopes = []
            volumes = self.kline_generator.producer.volume[-20:]  # 获取成交量数据
            
            for period in [5, 10, 20]:
                ema = self.kline_generator.producer.ema(period, array=True)
                if len(ema) >= 2:
                    # 考虑价格斜率和成交量变化
                    price_slope = (ema[-1] - ema[-2]) / ema[-2]
                    volume_change = np.mean(volumes[-5:]) / np.mean(volumes[-20:])  # 成交量比率
                    weighted_slope = price_slope * (volume_change ** 0.5)  # 加入成交量权重
                    ema_slopes.append(weighted_slope)
            
            # 计算趋势强度（考虑成交量）
            if ema_slopes:
                trend_strength = abs(np.mean(ema_slopes))
                self.state_map.trend_strength = min(1.0, trend_strength)
            
            # 5. 趋势持续性评估
            if self.state_map.is_trending:
                self.state_map.trend_duration += 1
                # 动态调整最大趋势持续期
                max_duration = min(
                    self.max_trend_duration,
                    int(20 + 10 * self.state_map.trend_strength)  # 强趋势可以持续更长
                )
                if self.state_map.trend_duration > max_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
            else:
                self.state_map.trend_duration = 0
            
            # 6. 综合判断趋势 - 动态阈值
            volatility_ratio = volatility / np.mean(self.volatility_history)
            dynamic_threshold = self.trend_threshold * (1 - 0.2 * (volatility_ratio - 1))
            
            is_trending = (
                direction_consistency > dynamic_threshold and  # 动态方向一致性阈值
                volatility > self.volatility_threshold * 0.8 and  # 降低波动率要求
                self.state_map.trend_strength > 0.25 and  # 降低趋势强度要求
                (self.state_map.trend_duration >= self.min_trend_duration or  # 趋势持续性
                 direction_consistency > 0.85)  # 强趋势可以降低持续性要求
            )
            
            # 7. 更新趋势状态
            if is_trending != self.state_map.is_trending:
                self.state_map.is_trending = is_trending
                self.state_map.trend_type = "A" if is_trending else "B"
                self.trend_history.append(is_trending)
                if len(self.trend_history) > 20:
                    self.trend_history.pop(0)
                
                # 更新参数 - 动态调整
                base_params = self.param_sets[self.state_map.trend_type].copy()
                if is_trending:
                    # 趋势模式下根据强度调整参数
                    strength_factor = self.state_map.trend_strength
                    base_params["stop_mult"] *= (1 + 0.2 * strength_factor)
                    base_params["profit_mult"] *= (1 + 0.3 * strength_factor)
                else:
                    # 震荡模式下提高灵活性
                    base_params["stop_mult"] *= 0.9
                    base_params["profit_mult"] *= 0.85
                    base_params["trail_step"] *= 0.8
                
                self.current_params = base_params
            
            # 8. 更新状态映射
            self.state_map.volatility = volatility
            
            # 9. 趋势反转预警 - 增强版
            if len(self.trend_history) >= 5:
                recent_trends = self.trend_history[-5:]
                trend_changes = sum(1 for i in range(1, len(recent_trends)) if recent_trends[i] != recent_trends[i-1])
                
                if trend_changes >= 2:  # 频繁的趋势变化
                    # 更保守的参数调整
                    self.current_params["stop_mult"] *= 0.85
                    self.current_params["profit_mult"] *= 0.85
                    self.current_params["trail_step"] *= 0.75

    def calc_indicator(self) -> None:
        """计算技术指标"""
        if self.current_params is None:
            self.current_params = self.param_sets["A"]  # 默认使用趋势型参数
            
        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        # 计算ATR
        atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
        self.state_map.atr = round(atr, 2)
        
        # 计算VWAP
        # 使用K线生成器的历史数据计算典型价格
        closes = self.kline_generator.producer.close
        highs = self.kline_generator.producer.high
        lows = self.kline_generator.producer.low
        volumes = self.kline_generator.producer.volume
        
        if len(closes) >= self.params_map.vwap_period:
            typical_prices = [(highs[i] + lows[i] + closes[i])/3 for i in range(-self.params_map.vwap_period, 0)]
            total_volume = sum(volumes[-self.params_map.vwap_period:])
            
            if total_volume > 0:
                vwap = sum(tp * vol for tp, vol in zip(typical_prices, volumes[-self.params_map.vwap_period:])) / total_volume
            else:
                vwap = typical_prices[-1]
        else:
            # 当数据不足时使用最新K线的典型价格
            current_typical = (highs[-1] + lows[-1] + closes[-1])/3
            vwap = current_typical
        
        self.state_map.vwap = round(vwap, 2)
        self.state_map.vwap_upper = round(vwap * (1 + self.params_map.vwap_deviation / 100), 2)
        self.state_map.vwap_lower = round(vwap * (1 - self.params_map.vwap_deviation / 100), 2)
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            # 根据模式选择止损止盈计算方式
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                # 使用固定止盈止损
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                # 使用ATR动态止盈止损
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * self.current_params["trail_step"], 2
            )

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损"""
        if self.position_size > 0:
            # 如果使用固定止盈止损，则不更新动态止损
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                return
                
            # 更新最高价和最低价
            if current_price > self.state_map.highest_price:
                self.state_map.highest_price = current_price
            if current_price < self.state_map.lowest_price or self.state_map.lowest_price == 0:
                self.state_map.lowest_price = current_price
            
            # 计算当前盈亏
            self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
            
            # 更新最大盈亏
            if self.state_map.current_profit > self.state_map.max_profit:
                self.state_map.max_profit = self.state_map.current_profit
            
            # 动态调整止损 - 增强版
            profit_ratio = self.state_map.current_profit / (self.entry_price * self.position_size)
            atr_multiple = self.state_map.atr * self.current_params["stop_mult"]
            
            if self.state_map.current_profit > 0:
                # 根据盈利情况动态调整追踪止损
                if not self.is_trailing:
                    # 当盈利超过ATR的0.8倍时启动追踪止损
                    if self.state_map.current_profit > self.state_map.atr * 0.8:
                        self.is_trailing = True
                
                if self.is_trailing:
                    # 根据盈利水平动态调整追踪距离
                    if profit_ratio < 0.02:  # 小盈利
                        trail_distance = atr_multiple * 0.8
                    elif profit_ratio < 0.05:  # 中等盈利
                        trail_distance = atr_multiple * 1.0
                    else:  # 大盈利
                        trail_distance = atr_multiple * 1.2
                    
                    # 使用VWAP作为参考
                    vwap_based_stop = max(
                        self.state_map.vwap,
                        self.state_map.highest_price - trail_distance
                    )
                    
                    # 结合趋势强度调整止损位
                    trend_factor = 1 + (self.state_map.trend_strength - 0.5) * 0.4
                    final_stop = max(
                        self.entry_price,  # 不低于入场价
                        min(
                            vwap_based_stop,
                            self.state_map.highest_price - trail_distance * trend_factor
                        )
                    )
                    
                    self.state_map.stop_loss = round(final_stop, 2)
                    
                    # 动态调整止盈目标
                    if self.state_map.trend_strength > 0.6:  # 强趋势
                        profit_target = self.entry_price + atr_multiple * self.current_params["profit_mult"] * 1.2
                    else:  # 弱趋势
                        profit_target = self.entry_price + atr_multiple * self.current_params["profit_mult"] * 0.9
                    
                    self.state_map.take_profit = round(profit_target, 2)
            else:
                # 未盈利时的保护性止损
                if self.state_map.trend_strength < 0.3:  # 趋势减弱时收紧止损
                    protection_stop = self.entry_price - atr_multiple * 0.8
                else:  # 正常止损
                    protection_stop = self.entry_price - atr_multiple
                
                # 确保止损不会太远
                max_loss_distance = atr_multiple * 1.5
                protection_stop = max(
                    protection_stop,
                    self.entry_price - max_loss_distance
                )
                
                self.state_map.stop_loss = round(protection_stop, 2)

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        # 计算趋势
        self.calc_trend(kline)
        
        # 计算指标
        self.calc_indicator()
        
        # 更新动态止盈止损
        if self.tick:
            self.update_dynamic_stops(self.tick.last_price)
        
        # 生成交易信号 - 增强版
        if self.state_map.is_trending:
            # 趋势型交易信号 - 增加过滤条件
            ema_alignment = (
                self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
                (self.state_map.ema_fast - self.state_map.ema_slow) / self.state_map.ema_slow > 0.001  # 确保足够的价差
            )
            
            volume_confirmation = True
            if len(self.kline_generator.producer.volume) >= 20:
                recent_volume = np.mean(self.kline_generator.producer.volume[-5:])
                avg_volume = np.mean(self.kline_generator.producer.volume[-20:])
                volume_confirmation = recent_volume > avg_volume * 1.1  # 要求成交量放大
            
            vwap_confirmation = (
                self.tick.last_price > self.state_map.vwap and  # 价格在VWAP上方
                self.tick.last_price < self.state_map.vwap_upper  # 但不要追高
            )
            
            self.buy_signal = (
                ema_alignment and
                self.state_map.trend_strength > 0.35 and  # 适当降低趋势强度要求
                volume_confirmation and
                vwap_confirmation
            )
            
            self.sell_signal = (
                self.state_map.ema_fast < self.state_map.ema_mid < self.state_map.ema_slow or
                self.state_map.trend_strength < 0.25 or  # 趋势减弱时考虑平仓
                self.tick.last_price < self.state_map.vwap  # 跌破VWAP考虑平仓
            )
        else:
            # 震荡型交易信号 - 更谨慎的入场条件
            price_momentum = (
                self.state_map.ema_fast < self.state_map.ema_mid and
                self.state_map.ema_mid > self.state_map.ema_slow
            )
            
            volatility_filter = (
                self.state_map.volatility < self.volatility_threshold * 1.2 and  # 控制波动率
                self.state_map.volatility > self.volatility_threshold * 0.5  # 确保足够的波动
            )
            
            vwap_bounce = (
                self.tick.last_price > self.state_map.vwap_lower and  # 价格在下轨上方
                self.tick.last_price < self.state_map.vwap  # 价格在VWAP下方
            )
            
            self.buy_signal = (
                price_momentum and
                volatility_filter and
                vwap_bounce
            )
            
            self.sell_signal = (
                self.state_map.ema_fast > self.state_map.ema_mid and
                self.state_map.ema_mid < self.state_map.ema_slow or
                self.tick.last_price > self.state_map.vwap_upper  # 突破上轨考虑获利了结
            )
        
        # 更新价格
        self.long_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2

    def exec_signal(self):
        """简易交易信号执行"""
        self.signal_price = 0

        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        if self.order_id is not None:
            # 挂单未成交
            self.cancel_order(self.order_id)

        # 检查是否达到最大持仓数
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False

        # 检查动态止损
        if self.tick and position.net_position > 0:
            if self.tick.last_price <= self.state_map.stop_loss:
                self.sell_signal = True

        if position.net_position > 0 and self.sell_signal:
            self.signal_price = -self.long_price

            if self.trading:
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=self.long_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                # 重置动态止盈止损相关变量
                self.entry_price = 0
                self.position_size = 0
                self.is_trailing = False
                self.state_map.highest_price = 0
                self.state_map.lowest_price = 0
                self.state_map.current_profit = 0
                self.state_map.max_profit = 0

        # 买开
        if self.buy_signal:
            self.signal_price = self.long_price

            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )
                # 记录入场价格
                self.entry_price = self.long_price
                self.state_map.highest_price = self.long_price
                self.state_map.lowest_price = self.long_price
