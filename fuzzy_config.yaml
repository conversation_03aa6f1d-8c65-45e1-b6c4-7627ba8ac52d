# 模糊理论交易策略配置文件
# 支持热更新和参数优化

# 模糊集定义
fuzzy_sets:
  stability:
    Low:
      a: 0.0
      b: 0.1
      c: 0.3
      d: 0.4
    Medium:
      a: 0.3
      b: 0.4
      c: 0.6
      d: 0.7
    High:
      a: 0.6
      b: 0.7
      c: 0.9
      d: 1.0
  
  volatility:
    Low:
      a: 0.0
      b: 0.01
      c: 0.03
      d: 0.05
    Medium:
      a: 0.03
      b: 0.05
      c: 0.07
      d: 0.09
    High:
      a: 0.07
      b: 0.09
      c: 0.12
      d: 0.15
  
  profit:
    Negative:
      a: -0.1
      b: -0.08
      c: -0.03
      d: 0.0
    Low:
      a: -0.02
      b: 0.0
      c: 0.02
      d: 0.04
    Medium:
      a: 0.02
      b: 0.04
      c: 0.06
      d: 0.08
    High:
      a: 0.06
      b: 0.08
      c: 0.12
      d: 0.15

# 学习参数
learning_parameters:
  learning_rate: 0.01
  adaptation_enabled: true
  performance_threshold: 0.6
  max_rules: 30
  rule_pruning_threshold: 0.01
  incremental_learning_enabled: true
  reinforcement_learning_enabled: true

# 多源信息融合权重
fusion_weights:
  technical: 0.4
  sentiment: 0.3
  fundamental: 0.2
  news: 0.1

# 去模糊化方法配置
defuzzification:
  method: "centroid"  # centroid, area_center, maximum, mean_of_maximum
  q_rofs_q: 2
  use_advanced_methods: true

# 时间序列预测配置
time_series:
  window_size: 20
  n_intervals: 7
  prediction_steps: 3
  linguistic_variables:
    - "Very Low"
    - "Low" 
    - "Medium Low"
    - "Medium"
    - "Medium High"
    - "High"
    - "Very High"

# 模糊聚类配置
clustering:
  n_clusters: 5
  fuzzy_index: 2.0
  max_iter: 100
  tolerance: 0.0001
  auto_cluster_selection: true
  min_clusters: 3
  max_clusters: 8

# 模糊神经网络配置
fuzzy_neural_network:
  hidden_layers: [10, 8, 6]
  activation_function: "gaussian"
  learning_rate: 0.001
  momentum: 0.9
  epochs: 100
  batch_size: 32
  dropout_rate: 0.2

# 异步处理配置
async_processing:
  max_workers: 4
  batch_size: 10
  queue_timeout: 1.0
  performance_monitoring_interval: 10
  auto_optimization: true

# 可视化配置
visualization:
  plot_style: "seaborn-v0_8"
  color_palette: ["#FF6B6B", "#4ECDC4", "#45B7D1", "#96CEB4", "#FFEAA7", "#DDA0DD"]
  figure_size: [12, 8]
  dpi: 300
  save_format: "png"
  interactive_plots: true

# 性能监控配置
performance_monitoring:
  enable_logging: true
  log_level: "INFO"
  metrics_collection_interval: 60  # 秒
  performance_history_length: 1000
  auto_performance_tuning: true
  
# 风险管理配置
risk_management:
  max_position_size: 0.1
  stop_loss_multiplier: 2.0
  take_profit_multiplier: 3.0
  risk_free_rate: 0.02
  confidence_threshold: 0.7

# 市场情绪分析配置
sentiment_analysis:
  fear_greed_weight: 0.3
  volatility_sentiment_weight: 0.25
  volume_sentiment_weight: 0.25
  momentum_sentiment_weight: 0.2
  sentiment_smoothing_window: 5

# 高级特征提取配置
feature_extraction:
  trend_weight: 0.3
  momentum_weight: 0.3
  volatility_weight: 0.2
  volume_weight: 0.2
  feature_normalization: true
  feature_selection_enabled: true

# 规则库管理配置
rule_management:
  static_rules_enabled: true
  dynamic_rules_enabled: true
  rule_conflict_resolution: "weighted_average"
  rule_validation_enabled: true
  rule_backup_enabled: true
  rule_backup_interval: 3600  # 秒

# 数据管理配置
data_management:
  cache_size: 10000
  data_compression: true
  backup_enabled: true
  backup_interval: 86400  # 秒
  data_validation: true

# 系统配置
system:
  debug_mode: false
  profiling_enabled: false
  memory_optimization: true
  cpu_optimization: true
  gpu_acceleration: false

# 通知配置
notifications:
  email_enabled: false
  webhook_enabled: false
  log_notifications: true
  performance_alerts: true
  error_notifications: true

# 实验性功能
experimental:
  quantum_fuzzy_logic: false
  neural_fuzzy_hybrid: true
  adaptive_membership_functions: true
  meta_learning: false
  federated_learning: false
