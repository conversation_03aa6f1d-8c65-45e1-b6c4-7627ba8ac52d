"""
异步模糊决策处理模块
支持并发模糊推理、批量决策处理和实时适应
"""

import asyncio
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any, Callable
import threading
import queue
import time
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging

class AsyncFuzzyProcessor:
    """异步模糊决策处理器"""
    
    def __init__(self, max_workers=4, batch_size=10):
        self.max_workers = max_workers
        self.batch_size = batch_size
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 任务队列
        self.decision_queue = asyncio.Queue()
        self.result_queue = asyncio.Queue()
        
        # 批处理缓冲区
        self.batch_buffer = []
        self.batch_lock = threading.Lock()
        
        # 性能监控
        self.processing_times = []
        self.throughput_counter = 0
        self.last_throughput_time = time.time()
        
        # 状态管理
        self.is_running = False
        self.background_tasks = []
        
        # 日志
        self.logger = logging.getLogger(__name__)
        
    async def start(self):
        """启动异步处理器"""
        if self.is_running:
            return
        
        self.is_running = True
        
        # 启动后台任务
        self.background_tasks = [
            asyncio.create_task(self._decision_processor()),
            asyncio.create_task(self._batch_processor()),
            asyncio.create_task(self._performance_monitor())
        ]
        
        self.logger.info("异步模糊处理器已启动")
    
    async def stop(self):
        """停止异步处理器"""
        self.is_running = False
        
        # 取消后台任务
        for task in self.background_tasks:
            task.cancel()
        
        # 等待任务完成
        await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        self.logger.info("异步模糊处理器已停止")
    
    async def submit_decision_request(self, fuzzy_system, input_data, priority=1):
        """提交模糊决策请求"""
        request = {
            'id': f"req_{datetime.now().timestamp()}",
            'fuzzy_system': fuzzy_system,
            'input_data': input_data,
            'priority': priority,
            'timestamp': datetime.now(),
            'future': asyncio.Future()
        }
        
        await self.decision_queue.put(request)
        return await request['future']
    
    async def submit_batch_request(self, fuzzy_system, input_batch, priority=1):
        """提交批量模糊决策请求"""
        batch_request = {
            'id': f"batch_{datetime.now().timestamp()}",
            'fuzzy_system': fuzzy_system,
            'input_batch': input_batch,
            'priority': priority,
            'timestamp': datetime.now(),
            'future': asyncio.Future()
        }
        
        # 添加到批处理缓冲区
        with self.batch_lock:
            self.batch_buffer.append(batch_request)
        
        return await batch_request['future']
    
    async def _decision_processor(self):
        """决策处理器主循环"""
        while self.is_running:
            try:
                # 获取决策请求
                request = await asyncio.wait_for(
                    self.decision_queue.get(), timeout=1.0
                )
                
                # 异步处理决策
                asyncio.create_task(self._process_single_decision(request))
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"决策处理器错误: {e}")
    
    async def _process_single_decision(self, request):
        """处理单个决策请求"""
        try:
            start_time = time.time()
            
            # 在线程池中执行模糊推理
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                self.executor,
                self._execute_fuzzy_inference,
                request['fuzzy_system'],
                request['input_data']
            )
            
            # 记录处理时间
            processing_time = time.time() - start_time
            self.processing_times.append(processing_time)
            
            # 保持性能数据在合理范围内
            if len(self.processing_times) > 1000:
                self.processing_times.pop(0)
            
            # 设置结果
            request['future'].set_result({
                'decision': result,
                'processing_time': processing_time,
                'request_id': request['id']
            })
            
            self.throughput_counter += 1
            
        except Exception as e:
            self.logger.error(f"单个决策处理失败: {e}")
            request['future'].set_exception(e)
    
    async def _batch_processor(self):
        """批处理器主循环"""
        while self.is_running:
            try:
                await asyncio.sleep(0.1)  # 批处理间隔
                
                # 检查批处理缓冲区
                with self.batch_lock:
                    if len(self.batch_buffer) >= self.batch_size:
                        batch_to_process = self.batch_buffer[:self.batch_size]
                        self.batch_buffer = self.batch_buffer[self.batch_size:]
                    elif self.batch_buffer:
                        # 处理剩余的请求（如果等待时间过长）
                        oldest_request = self.batch_buffer[0]
                        wait_time = (datetime.now() - oldest_request['timestamp']).total_seconds()
                        
                        if wait_time > 1.0:  # 等待超过1秒
                            batch_to_process = self.batch_buffer.copy()
                            self.batch_buffer.clear()
                        else:
                            continue
                    else:
                        continue
                
                # 异步处理批次
                asyncio.create_task(self._process_batch(batch_to_process))
                
            except Exception as e:
                self.logger.error(f"批处理器错误: {e}")
    
    async def _process_batch(self, batch_requests):
        """处理批量请求"""
        try:
            start_time = time.time()
            
            # 并行处理批次中的所有请求
            tasks = []
            for request in batch_requests:
                task = asyncio.create_task(
                    self._process_batch_item(request)
                )
                tasks.append(task)
            
            # 等待所有任务完成
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 处理结果
            for i, (request, result) in enumerate(zip(batch_requests, results)):
                if isinstance(result, Exception):
                    request['future'].set_exception(result)
                else:
                    request['future'].set_result(result)
            
            batch_processing_time = time.time() - start_time
            self.throughput_counter += len(batch_requests)
            
            self.logger.debug(f"批处理完成: {len(batch_requests)} 个请求，耗时 {batch_processing_time:.3f}s")
            
        except Exception as e:
            self.logger.error(f"批处理失败: {e}")
            # 设置所有请求为失败
            for request in batch_requests:
                if not request['future'].done():
                    request['future'].set_exception(e)
    
    async def _process_batch_item(self, request):
        """处理批次中的单个项目"""
        loop = asyncio.get_event_loop()
        
        # 并行处理批次中的所有输入
        futures = []
        for input_data in request['input_batch']:
            future = loop.run_in_executor(
                self.executor,
                self._execute_fuzzy_inference,
                request['fuzzy_system'],
                input_data
            )
            futures.append(future)
        
        # 等待所有推理完成
        results = await asyncio.gather(*futures)
        
        return {
            'decisions': results,
            'request_id': request['id'],
            'batch_size': len(results)
        }
    
    def _execute_fuzzy_inference(self, fuzzy_system, input_data):
        """执行模糊推理（在线程池中运行）"""
        try:
            if hasattr(fuzzy_system, 'adaptive_inference'):
                # 使用自适应推理
                return fuzzy_system.adaptive_inference(*input_data)
            elif hasattr(fuzzy_system, 'infer'):
                # 使用标准推理
                return fuzzy_system.infer(*input_data)
            else:
                raise ValueError("模糊系统不支持推理方法")
                
        except Exception as e:
            self.logger.error(f"模糊推理执行失败: {e}")
            raise
    
    async def _performance_monitor(self):
        """性能监控器"""
        while self.is_running:
            try:
                await asyncio.sleep(10)  # 每10秒监控一次
                
                current_time = time.time()
                time_diff = current_time - self.last_throughput_time
                
                if time_diff > 0:
                    throughput = self.throughput_counter / time_diff
                    
                    # 计算平均处理时间
                    avg_processing_time = (
                        np.mean(self.processing_times) 
                        if self.processing_times else 0
                    )
                    
                    # 计算队列长度
                    queue_size = self.decision_queue.qsize()
                    batch_buffer_size = len(self.batch_buffer)
                    
                    self.logger.info(
                        f"性能监控 - 吞吐量: {throughput:.2f} req/s, "
                        f"平均处理时间: {avg_processing_time:.3f}s, "
                        f"队列长度: {queue_size}, "
                        f"批处理缓冲: {batch_buffer_size}"
                    )
                    
                    # 重置计数器
                    self.throughput_counter = 0
                    self.last_throughput_time = current_time
                
            except Exception as e:
                self.logger.error(f"性能监控错误: {e}")
    
    def get_performance_stats(self):
        """获取性能统计"""
        return {
            'avg_processing_time': np.mean(self.processing_times) if self.processing_times else 0,
            'max_processing_time': np.max(self.processing_times) if self.processing_times else 0,
            'min_processing_time': np.min(self.processing_times) if self.processing_times else 0,
            'queue_size': self.decision_queue.qsize(),
            'batch_buffer_size': len(self.batch_buffer),
            'total_processed': sum(self.processing_times),
            'is_running': self.is_running
        }

# 全局异步处理器实例
async_fuzzy_processor = AsyncFuzzyProcessor()
