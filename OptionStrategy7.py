from typing import Literal, Dict, List, Any, Union
import numpy as np
import time
from collections import deque
from dataclasses import dataclass
import pandas as pd
import pandas_ta as ta
import numexpr as ne
import onnxruntime as ort
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
from pydantic import ConfigDict
import logging
import rx
from rx import operators as ops
from rx.scheduler import ThreadPoolScheduler
import pickle
import os

pd.set_option('compute.use_numexpr', True)
ort_session = None

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

@dataclass
class IndicatorCache:
    ema_fast: float = 0
    ema_mid: float = 0
    ema_slow: float = 0
    rsi: float = 0
    atr: float = 0
    volume_ma: float = 0
    last_update: float = 0

@dataclass
class StrategyParams:
    ema: tuple = (5, 10, 20)
    stop_mult: float = 1.2
    profit_mult: float = 1.5
    trail_step: float = 0.3
    trend_weights: dict = None

    def __post_init__(self):
        self.trend_weights = self.trend_weights or {'short': 0.4, 'mid': 0.4, 'long': 0.2}

class Params(BaseParams):
    exchange: str = Field(default="SHFE", title="交易所代码")
    instrument_id: str = Field(default="cu2405", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    ema_fast_period: int = Field(default=5, title="快速EMA周期", ge=3, le=10)
    ema_mid_period: int = Field(default=10, title="中速EMA周期", ge=8, le=20)
    ema_slow_period: int = Field(default=20, title="慢速EMA周期", ge=15, le=30)
    rsi_period: int = Field(default=14, title="RSI周期", ge=7, le=21)
    rsi_upper: int = Field(default=65, title="RSI上限", ge=60, le=80)
    rsi_lower: int = Field(default=30, title="RSI下限", ge=20, le=40)
    volume_ma_period: int = Field(default=20, title="成交量MA周期", ge=10, le=30)
    volume_breakout_mult: float = Field(default=1.5, title="成交量突破倍数", ge=1.2, le=2.0)
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    atr_short_period: int = Field(default=5, title="短期ATR周期", ge=3, le=10)
    stop_mult: float = Field(default=1.2, title="止损倍数", ge=0.8, le=2.0)
    profit_mult: float = Field(default=1.5, title="止盈倍数", ge=1.2, le=2.5)
    trail_step: float = Field(default=0.3, title="追踪步长", ge=0.2, le=1.0)
    vol_threshold: float = Field(default=1.5, title="波动率阈值", ge=1.2, le=2.0)
    profit_take_ratio: float = Field(default=0.5, title="止盈回撤比例", ge=0.3, le=0.7)
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    trend_period_min: int = Field(default=8, title="最小趋势周期", ge=5, le=15)
    trend_period_max: int = Field(default=20, title="最大趋势周期", ge=15, le=30)
    trend_weight_recent: float = Field(default=0.6, title="近期价格权重", ge=0.5, le=0.8)
    volume_factor_weight: float = Field(default=0.3, title="成交量因子权重", ge=0.2, le=0.5)
    rsi_smooth_period: int = Field(default=3, title="RSI平滑周期", ge=2, le=5)
    rsi_trend_threshold: float = Field(default=0.3, title="RSI趋势阈值", ge=0.2, le=0.5)
    vol_filter_period: int = Field(default=5, title="波动率过滤周期", ge=3, le=10)
    vol_filter_threshold: float = Field(default=1.8, title="波动率过滤阈值", ge=1.5, le=2.5)
    trend_short_period: int = Field(default=3, title="短周期", ge=3, le=5)
    trend_mid_period: int = Field(default=5, title="中周期", ge=5, le=8)
    trend_long_period: int = Field(default=10, title="长周期", ge=8, le=15)
    trend_strength_threshold: float = Field(default=0.4, title="趋势强度阈值", ge=0.3, le=0.6)
    trend_duration_min: int = Field(default=2, title="最小趋势持续周期", ge=2, le=4)
    stoch_k_period: int = Field(default=9, title="随机指标K周期", ge=5, le=14)
    stoch_d_period: int = Field(default=3, title="随机指标D周期", ge=2, le=5)
    stoch_upper: int = Field(default=80, title="随机指标上限", ge=75, le=85)
    stoch_lower: int = Field(default=20, title="随机指标下限", ge=15, le=25)
    order_timeout: int = Field(default=10, title="订单超时时间(秒)", ge=5, le=30)
    std_filter_period: int = Field(default=20, title="标准差周期", ge=10, le=30)
    std_filter_mult: float = Field(default=1.5, title="标准差倍数", ge=1.0, le=2.0)
    momentum_smooth_period: int = Field(default=5, title="动量平滑周期", ge=3, le=8)
    acceleration_period: int = Field(default=3, title="加速度计算周期", ge=2, le=5)
    fib_period: int = Field(default=20, title="斐波那契周期", ge=10, le=30)
    fib_deviation: float = Field(default=0.02, title="斐波那契偏差", ge=0.01, le=0.05)
    fib_profit_ratio: float = Field(default=0.618, title="斐波那契止盈比率", ge=0.5, le=0.786)
    cache_ttl: int = Field(default=5, title="指标缓存时间(秒)", ge=1, le=10)
    max_history_size: int = Field(default=1000, title="历史数据最大长度", ge=500, le=2000)
    max_drawdown: float = Field(default=0.1, title="最大回撤限制", ge=0.05, le=0.2)
    position_size_mult: float = Field(default=0.02, title="仓位乘数", ge=0.01, le=0.05)
    min_volatility: float = Field(default=0.01, title="最小波动率", ge=0.005, le=0.02)
    min_trend_strength: float = Field(default=0.6, title="最小趋势强度", ge=0.5, le=0.8)
    signal_confirmation_periods: int = Field(default=3, title="信号确认周期数", ge=2, le=5)
    min_volume_ratio: float = Field(default=1.2, title="最小量比", ge=1.0, le=1.5)
    ml_enabled: bool = Field(default=True, title="启用机器学习")
    ml_model_type: Literal["rf", "lgb"] = Field(default="lgb", title="机器学习模型类型")
    ml_train_period: int = Field(default=1000, title="训练数据周期")
    ml_predict_period: int = Field(default=5, title="预测周期")
    ml_feature_window: int = Field(default=20, title="特征窗口大小")
    wavelet_type: str = Field(default="db4", title="小波类型")
    wavelet_level: int = Field(default=3, title="小波分解层数")
    rx_buffer_size: int = Field(default=100, title="响应式缓冲区大小")
    rx_sample_rate: int = Field(default=1, title="响应式采样率")
    ta_rsi_period: int = Field(default=14, title="RSI周期")
    ta_macd_fast: int = Field(default=12, title="MACD快线周期")
    ta_macd_slow: int = Field(default=26, title="MACD慢线周期")
    ta_macd_signal: int = Field(default=9, title="MACD信号线周期")
    batch_size: int = Field(default=100, title="批量处理大小")
    num_threads: int = Field(default=4, title="线程数")
    use_onnx: bool = Field(default=True, title="使用ONNX加速")
    feature_importance_threshold: float = Field(default=0.1, title="特征重要性阈值")
    prediction_confidence_threshold: float = Field(default=0.7, title="预测置信度阈值")
    validation_window: int = Field(default=10, title="验证窗口大小")

class State(BaseState):
    model_config = ConfigDict(arbitrary_types_allowed=True)
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    rsi: float = Field(default=0, title="RSI值")
    rsi_trend: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="RSI趋势")
    volume_ma: float = Field(default=0, title="成交量MA")
    volume_ratio: float = Field(default=0, title="量比")
    is_volume_breakout: bool = Field(default=False, title="成交量突破")
    atr: float = Field(default=0, title="ATR")
    atr_short: float = Field(default=0, title="短期ATR")
    trend_type: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    volatility_ratio: float = Field(default=1.0, title="波动率比值")
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    trend_period_current: int = Field(default=0, title="当前趋势周期")
    price_momentum: float = Field(default=0, title="价格动量")
    volume_factor: float = Field(default=0, title="成交量因子")
    rsi_smooth: float = Field(default=0, title="平滑RSI")
    rsi_momentum: float = Field(default=0, title="RSI动量")
    volatility_state: Literal["低波动", "中等波动", "高波动"] = Field(default="中等波动", title="波动率状态")
    signal_quality: float = Field(default=0, title="信号质量", ge=0, le=1)
    trend_short: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="短周期趋势")
    trend_mid: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="中周期趋势")
    trend_long: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="长周期趋势")
    trend_consensus: bool = Field(default=False, title="趋势共识")
    trend_reversal_confirmed: bool = Field(default=False, title="趋势反转确认")
    stoch_k: float = Field(default=50, title="随机指标K值")
    stoch_d: float = Field(default=50, title="随机指标D值")
    stoch_momentum: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="随机指标动量")
    order_time: float = Field(default=0, title="订单时间戳")
    price_std: float = Field(default=0, title="价格标准差")
    price_acceleration: float = Field(default=0, title="价格加速度")
    momentum_smoothed: float = Field(default=0, title="平滑动量")
    fib_levels: dict = Field(default_factory=dict, title="斐波那契水平")
    fib_support: float = Field(default=0, title="当前支撑位")
    fib_resistance: float = Field(default=0, title="当前阻力位")
    fib_trend_quality: float = Field(default=0, title="趋势质量", ge=0, le=1)
    win_rate: float = Field(default=0, title="胜率", ge=0, le=1)
    profit_factor: float = Field(default=0, title="盈亏比", ge=0)
    sharpe_ratio: float = Field(default=0, title="夏普比率")
    max_drawdown: float = Field(default=0, title="最大回撤", ge=0, le=1)
    trade_count: int = Field(default=0, title="交易次数", ge=0)
    avg_trade_duration: float = Field(default=0, title="平均持仓时间(秒)", ge=0)
    signal_accuracy: float = Field(default=0, title="信号准确率", ge=0, le=1)
    indicator_cache: IndicatorCache = Field(default_factory=IndicatorCache, title="指标缓存")
    calculation_time: float = Field(default=0, title="计算耗时(ms)")
    cache_hit_rate: float = Field(default=0, title="缓存命中率", ge=0, le=1)
    current_drawdown: float = Field(default=0, title="当前回撤", ge=0, le=1)
    position_size: float = Field(default=0, title="当前仓位大小")
    risk_adjusted_position: float = Field(default=0, title="风险调整后仓位")
    signal_quality_score: float = Field(default=0, title="信号质量评分", ge=0, le=1)
    trend_confirmation_count: int = Field(default=0, title="趋势确认计数")
    volume_confirmation: bool = Field(default=False, title="成交量确认")
    ml_model: Any = Field(default=None, title="机器学习模型")
    ml_features: np.ndarray = Field(default=None, title="特征矩阵")
    ml_predictions: np.ndarray = Field(default=None, title="预测结果")
    ml_accuracy: float = Field(default=0.0, title="模型准确率")
    wavelet_coeffs: dict = Field(default_factory=dict, title="小波系数")
    wavelet_energy: float = Field(default=0.0, title="小波能量")
    rx_stream: rx.Observable = Field(default=None, title="响应式数据流")
    rx_buffer: deque = Field(default_factory=deque, title="响应式缓冲区")
    ta_rsi: float = Field(default=0.0, title="RSI值")
    ta_macd: dict = Field(default_factory=dict, title="MACD指标")
    ta_bbands: dict = Field(default_factory=dict, title="布林带")
    feature_importance: Dict[str, float] = Field(default_factory=dict, title="特征重要性")
    prediction_validation: Dict[str, float] = Field(default_factory=dict, title="预测验证结果")
    model_performance: Dict[str, float] = Field(default_factory=dict, title="模型性能指标")

class OptionStrategy7(BaseStrategy):
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        self.kline_generator = KLineGenerator(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            callback=self.callback
        )
        
        # Initialize data structures
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        self.multi_timeframe_data = {"M1": deque(maxlen=100), "M5": deque(maxlen=100), "M15": deque(maxlen=100)}
        self.signal_confirmation_queue = deque(maxlen=self.params_map.signal_confirmation_periods)
        
        # Initialize ML model
        self._init_ml_model()
        self._init_rx_stream()
        
        # Initialize trading state
        self.buy_signal = False
        self.sell_signal = False
        self.position_size = 0
        self.entry_price = 0
        self.is_trailing = False
        self.tick = None
        self.order_id = None
        
        # Initialize performance tracking
        self.trade_returns = []
        self.trade_durations = []
        self.trade_timestamps = []
        self.prediction_errors = []
        self.cache_hits = 0
        self.cache_misses = 0
        self.error_count = 0
        self.last_ui_update = time.time()
        
        # Initialize risk management
        self.max_drawdown = 0
        self.current_drawdown = 0
        self.risk_score = 0
        self.position_size_mult = 0.02  # 2% risk per trade
        
        # Initialize ML model
        self._init_ml_model()
        self._init_rx_stream()

    def onInit(self) -> None:
        """Strategy initialization"""
        self.write_log("策略初始化")
        self.load_bar(10)
        
        # Initialize indicators
        self._init_indicators()
        
        # Load ML model if exists
        if self.params_map.ml_enabled:
            self._load_ml_model()
        
        # Initialize risk parameters
        self._init_risk_parameters()
        
        # Set strategy state
        self.inited = True
        self.trading = False

    def onStart(self) -> None:
        """Strategy startup"""
        self.write_log("策略启动")
        
        # Reset state
        self._reset_state()
        
        # Start ML model if enabled
        if self.params_map.ml_enabled:
            self._start_ml_model()
        
        # Set strategy state
        self.trading = True
        
        # Start data processing
        self._start_data_processing()

    def onStop(self) -> None:
        """Strategy shutdown"""
        self.write_log("策略停止")
        
        # Stop ML model if enabled
        if self.params_map.ml_enabled:
            self._stop_ml_model()
        
        # Save ML model if enabled
        if self.params_map.ml_enabled:
            self._save_ml_model()
        
        # Clean up resources
        self._cleanup_resources()
        
        # Set strategy state
        self.trading = False
        
        # Put event to notify UI
        self.put_event()

    def update_status_bar(self, status: dict = None):
        status = status or {
            'trend_type': self.state_map.trend_type,
            'trend_strength': self.state_map.trend_strength,
            'volatility': self.state_map.volatility,
            'position_size': self.position_size,
            'current_profit': self.state_map.current_profit,
            'win_rate': self.state_map.win_rate,
            'signal_accuracy': self.state_map.signal_accuracy
        }
        
        self.update_status('trend_type', status['trend_type'])
        self.update_status('trend_strength', f"{status['trend_strength']:.2f}")
        self.update_status('volatility', f"{status['volatility']:.2f}")
        self.update_status('position_size', str(status['position_size']))
        self.update_status('current_profit', f"{status['current_profit']:.2f}")
        self.update_status('win_rate', f"{status['win_rate']:.2%}")
        self.update_status('signal_accuracy', f"{status['signal_accuracy']:.2%}")
        super().update_status_bar()

    def update_indicator(self, name: str, value: float):
        super().update_indicator(name, value)

    def update_ui(self):
        main_indicators = self.main_indicator_data
        sub_indicators = self.sub_indicator_data
        
        self.update_main_chart(main_indicators)
        self.update_sub_chart(sub_indicators)
        self.update_status_bar()
        self.refresh_ui()

    def update_main_chart(self, indicators: dict):
        self.update_indicator('EMA_FAST', indicators['EMA_FAST'])
        self.update_indicator('EMA_MID', indicators['EMA_MID'])
        self.update_indicator('EMA_SLOW', indicators['EMA_SLOW'])
        self.update_indicator('ATR', indicators['ATR'])

    def update_sub_chart(self, indicators: dict):
        self.update_indicator('RSI', indicators['RSI'])
        self.update_indicator('VOLUME_RATIO', indicators['VOLUME_RATIO'])
        self.update_indicator('STOP_LOSS', indicators['STOP_LOSS'])
        self.update_indicator('TAKE_PROFIT', indicators['TAKE_PROFIT'])
        self.update_indicator('TRAILING_STOP', indicators['TRAILING_STOP'])

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        
        self.state_map.rx_buffer.append({
            'datetime': tick.datetime,
            'price': tick.last_price,
            'volume': tick.volume
        })
        
        if len(self.state_map.rx_buffer) > self.params_map.rx_buffer_size:
            self.state_map.rx_buffer.popleft()
            
        self.price_history.append(tick.last_price)
        self.volume_history.append(tick.volume)
        
        self.calc_indicator()
        self.calc_trend(tick)
        self.calc_signal(tick)
        self.exec_signal()
        self.update_ui()

    def on_order_cancel(self, order: OrderData) -> None:
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        
        is_long = trade.direction == "多"
        is_short = trade.direction == "空"
        
        self.state_map.trade_count += is_long
        self.state_map.current_profit = (trade.price - self.entry_price) * trade.volume * is_long
        
        if is_long:
            self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)
            self.state_map.current_drawdown = (self.state_map.max_profit - self.state_map.current_profit) / max(1, self.state_map.max_profit)
        
        if is_short:
            profit = (self.entry_price - trade.price) * trade.volume
            self.state_map.win_rate = ((self.state_map.win_rate * (self.state_map.trade_count - 1)) + (profit > 0)) / max(1, self.state_map.trade_count)
            self._update_sharpe_ratio(profit)
            self._update_signal_accuracy(profit > 0)

    def on_start(self):
        super().on_start()
        self._reset_state()

    def on_stop(self):
        super().on_stop()
        self._cleanup_resources()

    def callback(self, kline: KLineData) -> None:
        self.price_history.append(kline.close)
        self.volume_history.append(kline.volume)
        self.calc_indicator()
        self.calc_trend(kline)
        self.calc_signal(kline)
        self.exec_signal()

    def real_time_callback(self, kline: KLineData) -> None:
        self.multi_timeframe_data[kline.period].append(kline)
        self.calc_multi_period_trend(kline)
        self.update_dynamic_stops(kline.close) if self.position_size > 0 else None

    def calc_trend(self, data: Union[KLineData, TickData]) -> None:
        price = data.close if isinstance(data, KLineData) else data.last_price
        volume = data.volume
        
        self.price_history.append(price)
        self.volume_history.append(volume)
        
        if len(self.price_history) < 2:
            return

        vol_ratio = self.calc_volatility_ratio()
        self.trend_period = max(
            self.params_map.trend_period_min,
            min(self.params_map.trend_period_max, int(self.params_map.trend_period_max / vol_ratio))
        )
        
        while len(self.price_history) > self.trend_period:
            self.price_history.popleft()
            self.volume_history.popleft()
        
        if len(self.price_history) == self.trend_period:
            price_changes = np.diff(list(self.price_history))
            weights = np.exp(np.linspace(-1, 0, len(price_changes))) * self.params_map.trend_weight_recent
            weights /= np.sum(weights)
            
            weighted_changes = price_changes * weights
            direction_consistency = abs(np.sum(np.sign(weighted_changes))) / len(weighted_changes)
            
            volume_list = list(self.volume_history)
            volume_ma = np.mean(volume_list)
            volume_std = max(np.std(volume_list), 1)
            recent_volume = np.mean(volume_list[-3:])
            
            volume_factor = max(-1, min(1, (recent_volume - volume_ma) / volume_std))
            self.state_map.volume_factor = volume_factor
            
            ema_slopes = []
            for period in [self.params_map.ema_fast_period, self.params_map.ema_mid_period]:
                ema = self.kline_generator.producer.ema(period, array=True)
                ema_slopes.append((ema[-1] - ema[-2]) / ema[-2]) if len(ema) >= 2 else None
            
            if ema_slopes:
                base_trend_strength = abs(np.mean(ema_slopes))
                volume_component = abs(volume_factor) * self.params_map.volume_factor_weight
                trend_strength = min(1.0, base_trend_strength * (1 + volume_component))
                self.state_map.trend_strength = trend_strength
                self.state_map.price_momentum = np.sum(weighted_changes)
            
            new_trend = ("上升" if np.mean(weighted_changes) > 0 else "下降") if direction_consistency > self.trend_threshold else "震荡"
            
            if new_trend != self.state_map.trend_type:
                self.state_map.trend_type = new_trend
                self.state_map.is_trending = new_trend != "震荡"
                self.state_map.trend_duration = 1 if new_trend != "震荡" else 0
                self.current_params = self.param_sets[self.state_map.trend_type]
            elif self.state_map.is_trending:
                self.state_map.trend_duration += 1
                if self.state_map.trend_duration > self.max_trend_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
                    self.state_map.trend_type = "震荡"
                    self.current_params = self.param_sets["震荡"]
            
            vol_filter = self.kline_generator.producer.atr(self.params_map.vol_filter_period)[0]
            self.state_map.volatility_state = (
                "高波动" if vol_filter > self.params_map.vol_filter_threshold * self.state_map.atr else
                "低波动" if vol_filter < self.state_map.atr else "中等波动"
            )
            
            if self.state_map.trade_count > 0:
                self.state_map.win_rate = self.state_map.stop_profit_count / self.state_map.trade_count
                self.state_map.profit_factor = self.state_map.stop_profit_amount / max(1, self.state_map.stop_loss_amount)
                self.state_map.signal_accuracy = self.state_map.stop_profit_count / max(1, self.state_map.signal_confirm_count)

    def calc_indicator(self) -> None:
        self.current_params = self.current_params or self.param_sets["震荡"]
        
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        atr_short, _ = self.kline_generator.producer.atr(self.params_map.atr_short_period)
        
        self.state_map.atr = round(atr_long, 2)
        self.state_map.atr_short = round(atr_short, 2)
        self.state_map.volatility_ratio = round(atr_short / max(atr_long, 1), 2)
        
        if self.tick:
            current_price = self.tick.last_price
            
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                vol_adjustment = min(self.state_map.volatility_ratio / self.params_map.vol_threshold, 1.0)
                adjusted_stop_mult = self.current_params["stop_mult"] * vol_adjustment
                
                self.state_map.stop_loss = round(current_price - self.state_map.atr * adjusted_stop_mult, 2)
                self.state_map.take_profit = round(current_price + self.state_map.atr * self.current_params["profit_mult"], 2)
            
            self.state_map.trailing_stop = round(current_price - self.state_map.atr * self.current_params["trail_step"], 2)

    def update_dynamic_stops(self, current_price: float) -> None:
        if self.position_size <= 0:
            return
            
        self.state_map.highest_price = max(current_price, self.state_map.highest_price)
        self.state_map.lowest_price = min(current_price, self.state_map.lowest_price) if self.state_map.lowest_price != 0 else current_price
        
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)
        
        market_state = self._get_market_state()
        state_adjustment = self._get_state_adjustment(market_state)
        base_atr_multiple = self.state_map.atr * self.current_params["stop_mult"]
        risk_adjustment = state_adjustment["risk"]
        atr_multiple = base_atr_multiple * risk_adjustment
        
        if self.state_map.current_profit > 0:
            trigger_threshold = self.state_map.atr * (0.8 if self.state_map.volatility_ratio > self.params_map.vol_threshold else 1.0)
            self.is_trailing = self.is_trailing or self.state_map.current_profit > trigger_threshold
            
            if self.is_trailing:
                profit_ratio = self.state_map.current_profit / max(self.entry_price * self.position_size, 1)
                distance_mult = 0.75 if profit_ratio > 0.05 else 0.85 if profit_ratio > 0.02 else 0.95
                trend_factor = 1.1 if self.state_map.trend_strength > 0.6 else 0.9 if self.state_map.trend_strength < 0.3 else 1.0
                trail_distance = base_atr_multiple * distance_mult * trend_factor * risk_adjustment
                
                self.state_map.stop_loss = round(max(self.entry_price, self.state_map.highest_price - trail_distance), 2)
                profit_target = (
                    self.state_map.highest_price - trail_distance * 0.5 if profit_ratio > 0.05 else
                    self.state_map.highest_price + self.state_map.atr * self.current_params["profit_mult"] * risk_adjustment
                )
                self.state_map.take_profit = round(profit_target, 2)
            
            protection_stop = (
                self.entry_price - atr_multiple * 0.8 if self.state_map.trend_strength < 0.3 else
                self.entry_price - atr_multiple
            )
            max_loss_distance = atr_multiple * 1.5
            self.state_map.stop_loss = round(max(protection_stop, self.entry_price - max_loss_distance), 2)

    def calc_multi_period_trend(self, kline: KLineData) -> None:
        price_list = list(self.price_history)
        
        if len(price_list) >= self.params_map.std_filter_period:
            price_std = np.std(price_list[-self.params_map.std_filter_period:])
            self.state_map.price_std = price_std
            
            recent_std = np.std(price_list[-5:])
            std_ratio = recent_std / max(price_std, 1)
            vol_ratio = np.sqrt(0.7 * (self.state_map.volatility_ratio ** 2) + 0.3 * (std_ratio ** 2))
            
            self.trend_period = max(
                self.params_map.trend_period_min,
                min(self.params_map.trend_period_max, int(self.params_map.trend_period_max / (vol_ratio * self.params_map.std_filter_mult)))
            )
        
        if len(price_list) > self.trend_period:
            self.price_history.popleft()
            self.volume_history.popleft()
        
        if len(price_list) == self.trend_period:
            x = np.linspace(-2, 2, len(price_list))
            weights = 1 / (1 + np.exp(-x))
            weights /= np.sum(weights)
            
            price_changes = np.diff(price_list)
            weighted_changes = price_changes * weights[1:]
            
            if len(price_changes) >= self.params_map.acceleration_period:
                velocity = np.diff(price_changes[-self.params_map.acceleration_period:])
                acceleration = np.mean(velocity)
                self.state_map.price_acceleration = acceleration
                acc_factor = np.tanh(acceleration)
            else:
                acc_factor = 0
            
            if len(weighted_changes) >= self.params_map.momentum_smooth_period:
                alpha = 2 / (self.params_map.momentum_smooth_period + 1)
                smoothed_momentum = 0
                for change in weighted_changes[-self.params_map.momentum_smooth_period:]:
                    smoothed_momentum = alpha * change + (1 - alpha) * smoothed_momentum
                self.state_map.momentum_smoothed = smoothed_momentum
            
            if len(weighted_changes) > 0:
                base_strength = abs(np.sum(weighted_changes)) / np.sum(abs(weighted_changes))
                trend_strength = min(1.0, base_strength * (1 + 0.3 * abs(acc_factor)))
                trend_strength *= 1.2 if np.sign(smoothed_momentum) == np.sign(acc_factor) else 1.0
                self.state_map.trend_strength = trend_strength
            
            if len(price_list) >= self.params_map.fib_period:
                high = max(price_list[-self.params_map.fib_period:])
                low = min(price_list[-self.params_map.fib_period:])
                price_range = high - low
                
                self.state_map.fib_levels = {
                    "0.236": low + 0.236 * price_range,
                    "0.382": low + 0.382 * price_range,
                    "0.5": low + 0.5 * price_range,
                    "0.618": low + 0.618 * price_range,
                    "0.786": low + 0.786 * price_range
                }
                
                current_price = price_list[-1]
                for level, price in self.state_map.fib_levels.items():
                    if abs(current_price - price) / price < self.params_map.fib_deviation:
                        if float(level) < 0.5:
                            self.state_map.fib_support = price
                        else:
                            self.state_map.fib_resistance = price
                
                quality = min(1.0, (current_price - low) / price_range) if self.state_map.trend_type == "上升" else min(1.0, (high - current_price) / price_range)
                self.state_map.fib_trend_quality = quality

    def calc_stoch_momentum(self) -> None:
        high = self.kline_generator.producer.high
        low = self.kline_generator.producer.low
        close = self.kline_generator.producer.close
        
        if len(high) >= self.params_map.stoch_k_period:
            period_high = np.max(high[-self.params_map.stoch_k_period:])
            period_low = np.min(low[-self.params_map.stoch_k_period:])
            
            if period_high != period_low:
                k = 100 * (close[-1] - period_low) / (period_high - period_low)
                self.state_map.stoch_k = k
                
                if len(high) >= self.params_map.stoch_k_period + self.params_map.stoch_d_period:
                    d = np.mean([self.state_map.stoch_k for _ in range(self.params_map.stoch_d_period)])
                    self.state_map.stoch_d = d
                    
                    self.state_map.stoch_momentum = (
                        "上升" if k > d and k < self.params_map.stoch_upper else
                        "下降" if k < d and k > self.params_map.stoch_lower else "震荡"
                    )

    def _update_indicator_cache(self) -> None:
        current_time = time.time()
        cache_expired = current_time - self.state_map.indicator_cache.last_update > self.params_map.cache_ttl
        
        self.state_map.indicator_cache = IndicatorCache(
            ema_fast=self.state_map.ema_fast,
            ema_mid=self.state_map.ema_mid,
            ema_slow=self.state_map.ema_slow,
            rsi=self.state_map.rsi,
            atr=self.state_map.atr,
            volume_ma=self.state_map.volume_ma,
            last_update=current_time
        ) if cache_expired else self.state_map.indicator_cache
        
        self.cache_misses += cache_expired
        self.cache_hits += not cache_expired
        total = self.cache_hits + self.cache_misses
        self.state_map.cache_hit_rate = self.cache_hits / max(total, 1)

    def _calculate_position_size(self, current_price: float) -> float:
        volatility_factor = min(1.0, self.state_map.volatility / self.params_map.min_volatility)
        trend_factor = self.state_map.trend_strength
        signal_factor = self.state_map.signal_quality_score
        base_position = self.params_map.position_size_mult * self.params_map.order_volume
        adjusted_position = base_position * volatility_factor * trend_factor * signal_factor
        return min(adjusted_position, self.params_map.max_positions)

    def _validate_signal_quality(self, kline: KLineData) -> bool:
        return all([
            self.state_map.trend_strength >= self.params_map.min_trend_strength,
            self.state_map.volume_confirmation,
            self.state_map.trend_consensus,
            self.state_map.volatility >= self.params_map.min_volatility
        ])

    def calc_signal(self, data: Union[KLineData, TickData]):
        self.data_stream.on_next({
            'price': data.close if isinstance(data, KLineData) else data.last_price,
            'volume': data.volume,
            'timestamp': data.datetime
        })

        trend_cond = (self.state_map.ema_fast > self.state_map.ema_slow)
        momentum_cond = (self.state_map.stoch_k > self.state_map.stoch_d)
        volume_cond = (data.volume > self.state_map.volume_ma * 1.5)

        signal_matrix = np.array([trend_cond, momentum_cond, volume_cond])
        weights = np.array([0.4, 0.3, 0.3])
        signal_strength = np.dot(signal_matrix, weights)

        self.state_machine.transition({
            'signal_strength': signal_strength,
            'volatility': self.state_map.volatility,
            'position': self.position_size
        })

        self.buy_signal = self.state_machine.should_enter()
        self.sell_signal = self.state_machine.should_exit()

    def exec_signal(self):
        OrderExecutor.create_pipeline(
            self.data_stream,
            self.position_size,
            self.params_map.max_drawdown
        ).subscribe(
            on_next=lambda order: self._process_order(order),
            on_error=lambda e: self.logger.error(f"订单执行异常: {str(e)}")
        )

    def _process_order(self, order: dict):
        order_conditions = {
            'entry': (order['signal'] & ~order['position']),
            'exit': (order['profit'] >= order['target']) | (order['drawdown'] > order['threshold'])
        }

        order_matrix = pd.DataFrame.from_dict({
            'buy': [order_conditions['entry'], self.params_map.order_volume],
            'sell': [order_conditions['exit'], self.params_map.order_volume]
        }, orient='index', columns=['condition', 'volume'])

        valid_orders = order_matrix[order_matrix['condition']]
        self.order_id = self._execute_batch_orders(valid_orders) if not valid_orders.empty else None

    def _update_sharpe_ratio(self, profit: float) -> None:
        returns = profit / max(self.entry_price * self.position_size, 1)
        if self.state_map.trade_count > 1:
            avg_return = returns / self.state_map.trade_count
            std_return = np.std([returns])
            self.state_map.sharpe_ratio = avg_return / max(std_return, 1e-6)

    def _update_signal_accuracy(self, is_profitable: bool) -> None:
        if self.state_map.trade_count > 0:
            self.state_map.signal_accuracy = (
                self.state_map.signal_accuracy * (self.state_map.trade_count - 1) + 
                float(is_profitable)
            ) / self.state_map.trade_count

    def calculate_indicators(self, data: pd.DataFrame) -> None:
        self.state_map.atr = self._calculate_atr(data)
        self.state_map.rsi = self._calculate_rsi(data)
        self.state_map.macd = self._calculate_macd(data)
        self.state_map.ma20 = data['close'].rolling(window=20).mean().iloc[-1]
        self.state_map.ma50 = data['close'].rolling(window=50).mean().iloc[-1]
        self.state_map.ma200 = data['close'].rolling(window=200).mean().iloc[-1]
        self.state_map.volatility = data['close'].pct_change().std() * np.sqrt(252)
        self.state_map.volatility_ratio = self.state_map.volatility / data['close'].pct_change().std().rolling(window=20).mean().iloc[-1]
        self.state_map.trend_strength = self._calculate_trend_strength(data)
        self.state_map.market_sentiment = self._calculate_market_sentiment(data)
        self.state_map.volume_ma = data['volume'].rolling(window=20).mean().iloc[-1]
        self.state_map.volume_ratio = data['volume'].iloc[-1] / self.state_map.volume_ma
        self.state_map.momentum = self._calculate_momentum(data)
        self.state_map.support_level = self._calculate_support_level(data)
        self.state_map.resistance_level = self._calculate_resistance_level(data)
        self.state_map.market_state = self._get_market_state()
        self.state_map.risk_score = self._calculate_risk_score()

    def _calculate_atr(self, data: pd.DataFrame) -> float:
        high = data['high']
        low = data['low']
        close = data['close']
        
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = tr.ewm(span=self.params_map.atr_period, adjust=False).mean()
        return round(atr.iloc[-1], 2)

    def _calculate_rsi(self, data: pd.DataFrame) -> float:
        delta = data['close'].diff()
        gain = delta.where(delta > 0, 0)
        loss = -delta.where(delta < 0, 0)
        avg_gain = gain.ewm(span=self.params_map.rsi_period, adjust=False).mean()
        avg_loss = loss.ewm(span=self.params_map.rsi_period, adjust=False).mean()
        rs = avg_gain / avg_loss
        rsi = 100 - (100 / (1 + rs))
        return round(rsi.iloc[-1], 2)

    def _calculate_macd(self, data: pd.DataFrame) -> dict:
        ema_fast = data['close'].ewm(span=12, adjust=False).mean()
        ema_slow = data['close'].ewm(span=26, adjust=False).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=9, adjust=False).mean()
        histogram = macd_line - signal_line
        return {
            'macd': round(macd_line.iloc[-1], 2),
            'signal': round(signal_line.iloc[-1], 2),
            'histogram': round(histogram.iloc[-1], 2)
        }

    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        price_changes = data['close'].pct_change()
        direction_consistency = abs(price_changes.rolling(window=self.params_map.trend_period_min).mean()) / price_changes.rolling(window=self.params_map.trend_period_min).std()
        volatility = data['close'].pct_change().rolling(window=self.params_map.trend_period_min).std()
        trend_strength = direction_consistency * (1 - volatility)
        trend_strength = trend_strength.clip(0, 1)
        return round(trend_strength.iloc[-1], 2)

    def _calculate_market_sentiment(self, data: pd.DataFrame) -> float:
        price_momentum = data['close'].pct_change(periods=5)
        volume_change = data['volume'].pct_change(periods=5)
        rsi = self._calculate_rsi(data)
        rsi_position = (rsi - 50) / 50
        sentiment = (price_momentum * 0.4 + volume_change * 0.3 + rsi_position * 0.3)
        sentiment = sentiment.clip(-1, 1)
        return round(sentiment.iloc[-1], 2)

    def _calculate_momentum(self, data: pd.DataFrame) -> float:
        price_changes = data['close'].pct_change()
        acceleration = price_changes.diff()
        volume_weight = data['volume'] / data['volume'].rolling(window=20).mean()
        momentum = (price_changes * 0.5 + acceleration * 0.3 + volume_weight * 0.2)
        momentum = momentum.clip(-1, 1)
        return round(momentum.iloc[-1], 2)

    def _calculate_support_level(self, data: pd.DataFrame) -> float:
        recent_low = data['low'].rolling(window=20).min()
        ma_support = data['close'].rolling(window=20).mean() * 0.98
        high = data['high'].rolling(window=20).max()
        low = data['low'].rolling(window=20).min()
        fib_support = low + (high - low) * 0.382
        support_level = (recent_low * 0.4 + ma_support * 0.3 + fib_support * 0.3)
        return round(support_level.iloc[-1], 2)

    def _calculate_resistance_level(self, data: pd.DataFrame) -> float:
        recent_high = data['high'].rolling(window=20).max()
        ma_resistance = data['close'].rolling(window=20).mean() * 1.02
        high = data['high'].rolling(window=20).max()
        low = data['low'].rolling(window=20).min()
        fib_resistance = high - (high - low) * 0.618
        resistance_level = (recent_high * 0.4 + ma_resistance * 0.3 + fib_resistance * 0.3)
        return round(resistance_level.iloc[-1], 2)

    def _calculate_risk_score(self) -> float:
        volatility = self.state_map.volatility
        drawdown = self.state_map.current_drawdown
        duration = self.state_map.trend_duration
        risk_score = (volatility * 0.4 + drawdown * 0.3 + duration * 0.3)
        risk_score = risk_score.clip(0, 1)
        return round(risk_score, 2)

    def _init_ml_model(self) -> None:
        """Initialize ML model with optimized features"""
        if not self.params_map.ml_enabled:
            return
            
        # Initialize feature scaler
        self.feature_scaler = StandardScaler()
        
        # Initialize feature cache
        self.feature_cache = deque(maxlen=self.params_map.ml_feature_window)
        
        # Initialize model based on type
        if self.params_map.ml_model_type == "rf":
            self.state_map.ml_model = RandomForestClassifier(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=self.params_map.num_threads
            )
        else:  # lgb
            self.state_map.ml_model = lgb.LGBMClassifier(
                n_estimators=100,
                max_depth=10,
                learning_rate=0.1,
                random_state=42,
                n_jobs=self.params_map.num_threads
            )
        
        # Initialize ONNX runtime if enabled
        if self.params_map.use_onnx:
            self._init_onnx_runtime()
        
        # Initialize online learning buffer
        self.online_learning_buffer = deque(maxlen=self.params_map.ml_train_period)
        self.last_retrain_time = time.time()

    def _init_onnx_runtime(self):
        global ort_session
        
        onnx_model = (
            self._convert_rf_to_onnx() if self.params_map.ml_model_type == "rf" else
            self._convert_lgb_to_onnx()
        )
        
        if onnx_model:
            ort_session = ort.InferenceSession(onnx_model.SerializeToString())
            options = ort.SessionOptions()
            options.intra_op_num_threads = self.params_map.num_threads
            options.inter_op_num_threads = self.params_map.num_threads

    def _convert_rf_to_onnx(self):
        from skl2onnx import convert_sklearn
        from skl2onnx.common.data_types import FloatTensorType
        
        initial_type = [('float_input', FloatTensorType([None, self.state_map.ml_features.shape[1]]))]
        return convert_sklearn(self.state_map.ml_model, initial_types=initial_type)

    def _convert_lgb_to_onnx(self):
        from onnxmltools.convert import convert_lightgbm
        from onnxmltools.convert.common.data_types import FloatTensorType
        
        return convert_lightgbm(
            self.state_map.ml_model,
            initial_types=[('float_input', FloatTensorType([None, self.state_map.ml_features.shape[1]]))]
        )

    def _process_features(self, data: pd.DataFrame) -> np.ndarray:
        features = []
        
        price_expr = """
            (close - close_prev) / close_prev,
            mean(close, window=5),
            mean(close, window=20)
        """
        price_features = ne.evaluate(price_expr, {
            'close': data['close'].values,
            'close_prev': data['close'].shift(1).values
        })
        features.extend(price_features)
        
        for name, processor in self.feature_processors.items():
            if name != 'scaler':
                indicator = processor(data)
                features.extend([indicator[col].values for col in indicator.columns] if isinstance(indicator, pd.DataFrame) else [indicator.values])
        
        volume_expr = """
            (volume - volume_prev) / volume_prev,
            mean(volume, window=5),
            mean(volume, window=20)
        """
        volume_features = ne.evaluate(volume_expr, {
            'volume': data['volume'].values,
            'volume_prev': data['volume'].shift(1).values
        })
        features.extend(volume_features)
        
        feature_matrix = np.column_stack(features)
        feature_matrix = self.feature_processors['scaler'].fit_transform(feature_matrix)
        self.feature_cache.append(feature_matrix[-1])
        return feature_matrix

    def _init_rx_stream(self) -> None:
        scheduler = ThreadPoolScheduler(self.params_map.num_threads)
        
        self.state_map.rx_stream = rx.from_iterable(self.state_map.rx_buffer).pipe(
            ops.buffer_with_count(self.params_map.batch_size),
            ops.observe_on(scheduler),
            ops.map(lambda x: pd.DataFrame(x)),
            ops.map(lambda df: self._process_rx_data(df)),
            ops.catch(lambda e, source: self._handle_rx_error(e, source))
        ).subscribe(
            on_next=lambda x: self._handle_rx_data(x),
            on_error=lambda e: self.logger.error(f"响应式流错误: {str(e)}"),
            on_completed=lambda: self.logger.info("响应式流完成")
        )

    def _handle_rx_error(self, error: Exception, source: rx.Observable) -> rx.Observable:
        self.logger.error(f"响应式流处理错误: {str(error)}")
        return source

    def _validate_prediction(self, prediction: float, actual: float) -> bool:
        """Validate prediction with confidence filtering"""
        error = abs(prediction - actual)
        confidence = 1 - error
        
        self.state_map.prediction_validation = {
            'error': error,
            'confidence': confidence,
            'timestamp': time.time()
        }
        
        return confidence >= self.params_map.prediction_confidence_threshold

    def _update_feature_importance(self):
        if hasattr(self.state_map.ml_model, 'feature_importances_'):
            importance = self.state_map.ml_model.feature_importances_
            self.state_map.feature_importance = {
                f'feature_{i}': imp for i, imp in enumerate(importance)
                if imp >= self.params_map.feature_importance_threshold
            }
            self.update_indicator('FEATURE_IMPORTANCE', importance.mean())

    def _handle_rx_data(self, df: pd.DataFrame) -> None:
        self.state_map.ta_rsi = df['rsi'].iloc[-1]
        self.state_map.ta_macd = {
            'macd': df['MACD_12_26_9'].iloc[-1],
            'signal': df['MACDs_12_26_9'].iloc[-1],
            'hist': df['MACDh_12_26_9'].iloc[-1]
        }
        
        if self.params_map.ml_enabled and self.state_map.ml_model is not None:
            features = self._process_features(df)
            
            if len(features) > 0:
                self.state_map.ml_features = features
                predictions = (
                    ort_session.run(None, {'float_input': features.astype(np.float32)})[0] 
                    if self.params_map.use_onnx and ort_session is not None else
                    self.state_map.ml_model.predict(features)
                )
                self.state_map.ml_predictions = predictions
                self.state_map.signal_quality_score = float(predictions[-1]) if len(predictions) > 0 else 0
                
                if len(self.state_map.ml_predictions) >= self.params_map.validation_window:
                    self._validate_prediction(
                        self.state_map.ml_predictions[-1],
                        df['close'].iloc[-1] / df['close'].iloc[-2] - 1
                    )
                
                self._update_feature_importance()
                self._check_online_learning(df)
                self._update_ml_indicators()
        
        self.update_ui()

    def _update_ml_indicators(self):
        if self.state_map.ml_predictions is not None:
            self.update_indicator('ML_PREDICTION', float(self.state_map.ml_predictions[-1]))
            self.update_indicator('ML_CONFIDENCE', self.state_map.signal_quality_score)
            self.update_indicator('ML_ACCURACY', self.state_map.ml_accuracy)
            
            if self.state_map.feature_importance:
                self.update_indicator('FEATURE_IMPORTANCE', 
                    sum(self.state_map.feature_importance.values()) / len(self.state_map.feature_importance)
                )
            
            if self.state_map.prediction_validation:
                self.update_indicator('PREDICTION_VALIDATION', 
                    self.state_map.prediction_validation['confidence']
                )

    def update_ml_chart(self):
        if self.state_map.ml_predictions is not None:
            self.update_indicator('ML_PREDICTION', float(self.state_map.ml_predictions[-1]))
            self.update_indicator('ML_CONFIDENCE', self.state_map.signal_quality_score)
            self.update_indicator('ML_ACCURACY', self.state_map.ml_accuracy)
            
            if self.state_map.feature_importance:
                self.update_indicator('FEATURE_IMPORTANCE', 
                    sum(self.state_map.feature_importance.values()) / len(self.state_map.feature_importance)
                )
            
            if len(self.feature_cache) > 0:
                features = np.array(list(self.feature_cache))
                self.update_indicator('ML_FEATURE_DISTRIBUTION', features.mean(axis=0))

    def _load_training_data(self) -> None:
        history_data = self.kline_generator.producer.get_history(
            period=self.params_map.kline_style,
            count=self.params_map.ml_train_period
        )
        
        if history_data and len(history_data) >= self.params_map.ml_train_period:
            df = pd.DataFrame(history_data)
            features = self._extract_features(df)
            labels = self._generate_labels(df)
            self.state_map.ml_model.fit(features, labels)
            self.logger.info("机器学习模型训练完成")
        else:
            self.logger.warning("历史数据不足，无法训练模型")

    def _generate_labels(self, df: pd.DataFrame) -> np.ndarray:
        future_returns = df['close'].shift(-self.params_map.ml_predict_period) / df['close'] - 1
        labels = (future_returns > 0).astype(int)
        return labels[:-self.params_map.ml_predict_period]

    def _process_rx_data(self, df: pd.DataFrame) -> pd.DataFrame:
        df['rsi'] = ta.rsi(df['close'], length=self.params_map.ta_rsi_period)
        macd = ta.macd(
            df['close'],
            fast=self.params_map.ta_macd_fast,
            slow=self.params_map.ta_macd_slow,
            signal=self.params_map.ta_macd_signal
        )
        df = pd.concat([df, macd], axis=1)
        
        coeffs = pywt.wavedec(
            df['close'],
            self.params_map.wavelet_type,
            level=self.params_map.wavelet_level
        )
        self.state_map.wavelet_coeffs = {f'level_{i}': c for i, c in enumerate(coeffs)}
        self.state_map.wavelet_energy = np.sum([np.sum(np.square(c)) for c in coeffs])
        return df

    def _reset_state(self):
        self.calculation_start_time = 0
        self.cache_hits = 0
        self.cache_misses = 0
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        self.multi_timeframe_data = {"M1": deque(maxlen=100), "M5": deque(maxlen=100), "M15": deque(maxlen=100)}
        self.signal_confirmation_queue = deque(maxlen=self.params_map.signal_confirmation_periods)
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.order_id = None
        self.position_size = 0
        self.entry_price = 0
        self.is_trailing = False

    def _cleanup_resources(self) -> None:
        """Release all resources and clean up"""
        # Clear data buffers
        self.price_history.clear()
        self.volume_history.clear()
        self.rsi_history.clear()
        
        # Reset ML model
        if self.params_map.ml_enabled:
            self.state_map.ml_model = None
            self.state_map.ml_features = None
            self.state_map.ml_predictions = None
        
        # Reset trading state
        self.position_size = 0
        self.entry_price = 0
        self.is_trailing = False
        self.buy_signal = False
        self.sell_signal = False
        
        # Clear performance metrics
        self.trade_returns.clear()
        self.trade_durations.clear()
        self.trade_timestamps.clear()
        self.prediction_errors.clear()

    def run_cycle(self) -> None:
        """Single cycle of the strategy loop"""
        try:
            # Process incoming data
            self._process_incoming_data()
            
            # Update indicators
            self._update_indicators()
            
            # Generate signals
            self._generate_signals()
            
            # Execute trades
            self._execute_trades()
            
            # Update performance metrics
            self._update_metrics()
            
        except Exception as e:
            self.logger.error(f"Strategy cycle error: {str(e)}", exc_info=True)
            self._handle_error(e)

    def _process_incoming_data(self) -> None:
        """Process all incoming market data"""
        # Process ticks
        while not self.tick_queue.empty():
            try:
                tick = self.tick_queue.get_nowait()
                self._process_tick_data(tick)
            except queue.Empty:
                break
                
        # Process klines
        while not self.kline_queue.empty():
            try:
                kline = self.kline_queue.get_nowait()
                self.callback(kline)
            except queue.Empty:
                break
                
        # Process trades
        while not self.trade_queue.empty():
            try:
                trade = self.trade_queue.get_nowait()
                self.on_trade(trade, log=True)
            except queue.Empty:
                break

    def _update_indicators(self) -> None:
        """Update all technical indicators"""
        # Update slow indicators on kline close
        if self.new_kline_event.is_set():
            self.calc_indicator()
            self.calc_trend(self.last_kline)
            self._update_ml_predictions()
            self.new_kline_event.clear()
        
        # Update volume analysis
        self._update_volume_analysis()
        
        # Update RSI analysis
        self._update_rsi_analysis()
        
        # Update trend indicators
        self._update_trend_indicators()

    def _generate_signals(self) -> None:
        """Generate trading signals based on current state"""
        # Calculate composite signal score
        signal_score = (
            0.4 * self.state_map.trend_strength +
            0.3 * self.state_map.signal_quality_score +
            0.2 * (self.state_map.ml_predictions[-1] if self.params_map.ml_enabled else 0.5) +
            0.1 * (1 if self.state_map.volume_confirmation else 0)
        )
        
        # Generate signals
        self.buy_signal = (
            signal_score > 0.65 and
            self.position_size < self.params_map.max_positions and
            not self.state_map.volatility_state == "高波动"
        )
        
        self.sell_signal = (
            signal_score < 0.35 or
            self.state_map.current_drawdown > self.params_map.max_drawdown * 0.75
        )
        
        # Add to confirmation queue
        if self.buy_signal or self.sell_signal:
            self.signal_confirmation_queue.append((signal_score, time.time()))

    def _execute_trades(self) -> None:
        """Execute trades based on confirmed signals"""
        # Check for confirmed buy signal
        if (len(self.signal_confirmation_queue) >= self.params_map.signal_confirmation_periods and
            all(s[0] > 0.6 for s in self.signal_confirmation_queue) and
            self.signal_confirmation_queue[-1][1] - self.signal_confirmation_queue[0][1] < 60):
            
            self._execute_buy()
            self.signal_confirmation_queue.clear()
        
        # Check for confirmed sell signal
        elif (self.position_size > 0 and
              ((len(self.signal_confirmation_queue) >= self.params_map.signal_confirmation_periods and
               all(s[0] < 0.4 for s in self.signal_confirmation_queue)) or
              self.state_map.current_drawdown > self.params_map.max_drawdown)):
            
            self._execute_sell()
            self.signal_confirmation_queue.clear()

    def _update_metrics(self) -> None:
        """Update all performance metrics"""
        self._update_performance_metrics()
        self._update_risk_metrics()
        self._update_ml_model_performance()
        
        # Update UI every second
        current_time = time.time()
        if current_time - self.last_ui_update > 1.0:
            self.update_ui()
            self.last_ui_update = current_time

    def _handle_error(self, error: Exception) -> None:
        """Handle strategy errors gracefully"""
        self.logger.error(f"Strategy error: {str(error)}", exc_info=True)
        
        # Cancel all open orders
        self.cancel_all_orders()
        
        # Close positions if in emergency mode
        if isinstance(error, (MemoryError, RuntimeError)):
            if self.position_size > 0:
                self._emergency_close()
        
        # Reset error state
        self.error_count += 1
        if self.error_count > 10:
            self.logger.critical("Too many errors, stopping strategy")
            self.stop()

    def _emergency_close(self) -> None:
        """Emergency position close"""
        try:
            if self.position_size > 0:
                self.send_order({
                    'direction': 'SELL',
                    'price': self.tick.last_price if self.tick else 0,
                    'volume': self.position_size,
                    'exchange': self.params_map.exchange,
                    'instrument': self.params_map.instrument_id,
                    'order_type': 'MARKET',
                    'comment': 'Emergency close'
                })
                self.position_size = 0
                self.entry_price = 0
        except Exception as e:
            self.logger.critical(f"Emergency close failed: {str(e)}")

    def _extract_features(self, data: pd.DataFrame) -> np.ndarray:
        """Extract 5-dimensional features for ML model"""
        features = []
        
        # 1. Returns
        returns = data['close'].pct_change()
        features.append(returns.values[-self.params_map.ml_feature_window:])
        
        # 2. Volatility
        volatility = returns.rolling(window=20).std()
        features.append(volatility.values[-self.params_map.ml_feature_window:])
        
        # 3. MA difference
        ma_fast = data['close'].rolling(window=self.params_map.ema_fast_period).mean()
        ma_slow = data['close'].rolling(window=self.params_map.ema_slow_period).mean()
        ma_diff = (ma_fast - ma_slow) / ma_slow
        features.append(ma_diff.values[-self.params_map.ml_feature_window:])
        
        # 4. MACD
        macd = ta.macd(
            data['close'],
            fast=self.params_map.ta_macd_fast,
            slow=self.params_map.ta_macd_slow,
            signal=self.params_map.ta_macd_signal
        )
        features.append(macd['MACD_12_26_9'].values[-self.params_map.ml_feature_window:])
        
        # 5. RSI
        rsi = ta.rsi(data['close'], length=self.params_map.rsi_period)
        features.append(rsi.values[-self.params_map.ml_feature_window:])
        
        # Stack features and scale
        feature_matrix = np.column_stack(features)
        return self.feature_scaler.fit_transform(feature_matrix)

    def _check_online_learning(self, df: pd.DataFrame) -> None:
        """Check if online learning should be performed"""
        current_time = time.time()
        should_retrain = (
            len(self.online_learning_buffer) >= self.params_map.ml_train_period and
            current_time - self.last_retrain_time > 3600  # 1 hour
        )
        
        if should_retrain:
            self.online_learning_buffer.append(df.iloc[-1])
            features = self._extract_features(pd.DataFrame(self.online_learning_buffer))
            labels = self._generate_labels(pd.DataFrame(self.online_learning_buffer))
            
            # Incremental training
            self.state_map.ml_model.fit(features, labels)
            self.last_retrain_time = current_time
            self.logger.info("Online learning completed")
            
            # Update model accuracy
            predictions = self.state_map.ml_model.predict(features)
            self.state_map.ml_accuracy = np.mean(predictions == labels)
            
            # Save model after retraining
            self._save_ml_model()
        else:
            self.online_learning_buffer.append(df.iloc[-1])

    def _load_ml_model(self) -> None:
        """Load saved ML model"""
        try:
            model_path = f"models/{self.__class__.__name__}_model.pkl"
            if os.path.exists(model_path):
                with open(model_path, 'rb') as f:
                    self.state_map.ml_model = pickle.load(f)
                self.logger.info("ML model loaded successfully")
        except Exception as e:
            self.logger.error(f"Failed to load ML model: {str(e)}")

    def _save_ml_model(self) -> None:
        """Save ML model"""
        try:
            os.makedirs("models", exist_ok=True)
            model_path = f"models/{self.__class__.__name__}_model.pkl"
            with open(model_path, 'wb') as f:
                pickle.dump(self.state_map.ml_model, f)
            self.logger.info("ML model saved successfully")
        except Exception as e:
            self.logger.error(f"Failed to save ML model: {str(e)}")