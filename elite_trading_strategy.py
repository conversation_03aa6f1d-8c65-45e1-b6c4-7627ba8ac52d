"""
Elite Trading Strategy - 精英交易策略
基于最佳实践的全面优化策略实现
目标：达到主流社区高性能策略水平
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from strategy_backtester import BaseBacktestStrategy
from advanced_strategy_architecture import (
    MarketRegimeDetector, VolatilityRegimeDetector, 
    MultiIndicatorSignalGenerator, AdaptiveRiskManager,
    MarketRegime, VolatilityRegime, SignalScore, RiskParameters
)


class EliteTradingStrategy(BaseBacktestStrategy):
    """精英交易策略 - 集成所有优化措施的高性能策略"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 初始化核心组件
        self.market_detector = MarketRegimeDetector()
        self.volatility_detector = VolatilityRegimeDetector()
        self.signal_generator = MultiIndicatorSignalGenerator()
        self.risk_manager = AdaptiveRiskManager()
        
        # 策略参数 - 提高阈值减少交易频率
        self.min_signal_score = 0.75     # 最小信号评分阈值 (提高到0.75)
        self.min_confidence = 0.7        # 最小置信度阈值 (提高到0.7)
        self.max_positions = 1           # 最大持仓数
        self.commission = 0.0002         # 手续费0.02%
        self.slippage = 0.0001          # 滑点0.01%
        self.min_trade_interval = 4      # 最小交易间隔(小时)
        
        # 状态跟踪
        self.current_position = None
        self.entry_price = 0
        self.stop_loss = 0
        self.take_profit = 0
        self.trailing_stop = 0
        self.profit_targets = []
        self.position_size = 0
        self.last_trade_time = None      # 上次交易时间
        
        # 风险控制
        self.daily_loss_limit = 0.02     # 日损失限制2%
        self.max_consecutive_losses = 5   # 最大连续亏损次数
        self.consecutive_losses = 0
        
        # 性能跟踪
        self.trade_history = []
        self.daily_pnl = []
        
    def calculate_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """计算所有技术指标"""
        # EMA指标
        data['ema_8'] = data['close'].ewm(span=8, adjust=False).mean()
        data['ema_21'] = data['close'].ewm(span=21, adjust=False).mean()
        data['ema_50'] = data['close'].ewm(span=50, adjust=False).mean()
        
        # RSI指标
        data['rsi'] = self._calculate_rsi(data['close'], 14)
        
        # MACD指标
        macd_data = self._calculate_macd(data['close'], 12, 26, 9)
        data['macd'] = macd_data['macd']
        data['macd_signal'] = macd_data['signal']
        data['macd_histogram'] = macd_data['histogram']
        
        # 布林带指标
        bb_data = self._calculate_bollinger_bands(data['close'], 20, 2)
        data['bb_upper'] = bb_data['upper']
        data['bb_middle'] = bb_data['middle']
        data['bb_lower'] = bb_data['lower']
        
        # ATR指标
        data['atr'] = self._calculate_atr(data['high'], data['low'], data['close'], 14)
        
        # ADX指标（简化版）
        data['adx'] = self._calculate_adx_simple(data, 14)
        
        return data
    
    def _calculate_rsi(self, prices: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def _calculate_macd(self, prices: pd.Series, fast: int, slow: int, signal: int) -> Dict:
        """计算MACD"""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        histogram = macd - macd_signal
        
        return {
            'macd': macd,
            'signal': macd_signal,
            'histogram': histogram
        }
    
    def _calculate_bollinger_bands(self, prices: pd.Series, period: int, std_dev: float) -> Dict:
        """计算布林带"""
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        
        return {
            'upper': sma + (std * std_dev),
            'middle': sma,
            'lower': sma - (std * std_dev)
        }
    
    def _calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def _calculate_adx_simple(self, data: pd.DataFrame, period: int) -> pd.Series:
        """计算简化版ADX"""
        high = data['high']
        low = data['low']
        close = data['close']
        
        # 计算真实波幅
        tr = pd.concat([
            high - low,
            abs(high - close.shift(1)),
            abs(low - close.shift(1))
        ], axis=1).max(axis=1)
        
        # 使用ATR作为ADX的代理指标
        atr = tr.rolling(window=period).mean()
        adx = (atr / close * 100).rolling(window=period).mean()
        
        return adx.fillna(20)
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算技术指标
        data = self.calculate_indicators(data)
        
        # 初始化信号DataFrame
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        signals['confidence'] = 0.0
        signals['signal_score'] = 0.0
        signals['market_regime'] = ''
        signals['volatility_regime'] = ''
        
        # 状态跟踪
        in_position = False
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        trailing_stop = 0
        risk_params = None
        
        # 逐行生成信号
        for i in range(50, len(data)):  # 确保有足够的历史数据
            current_data = data.iloc[i]
            current_price = current_data['close']
            current_atr = current_data['atr']
            
            if pd.isna(current_atr) or current_atr <= 0:
                continue
            
            # 检测市场状态
            market_regime = self.market_detector.detect_regime(data, i)
            volatility_regime = self.volatility_detector.detect_volatility_regime(data, i)
            
            # 生成信号评分
            signal_score = self.signal_generator.generate_signal_score(data, i)
            
            # 记录状态信息
            signals.loc[data.index[i], 'market_regime'] = market_regime.value
            signals.loc[data.index[i], 'volatility_regime'] = volatility_regime.value
            signals.loc[data.index[i], 'signal_score'] = signal_score.overall_score
            signals.loc[data.index[i], 'confidence'] = signal_score.confidence
            
            # 如果有持仓，首先检查退出条件
            if in_position:
                should_exit, exit_reason = self._check_exit_conditions(
                    current_price, stop_loss, take_profit, trailing_stop,
                    signal_score, market_regime, i, data
                )
                
                if should_exit:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = exit_reason
                    signals.loc[data.index[i], 'confidence'] = 1.0
                    
                    # 重置状态
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    trailing_stop = 0
                    continue
                else:
                    # 更新追踪止损
                    if risk_params and current_price > entry_price * 1.01:  # 盈利1%后启动追踪
                        new_trailing = current_price - current_atr * risk_params.trailing_stop_atr_mult
                        trailing_stop = max(trailing_stop, new_trailing)
                        stop_loss = max(stop_loss, trailing_stop)
            
            # 如果没有持仓，检查入场条件
            if not in_position:
                current_time = data.index[i]
                should_enter, enter_reason = self._check_entry_conditions(
                    signal_score, market_regime, volatility_regime, current_time
                )

                if should_enter:
                    # 获取风险参数
                    risk_params = self.risk_manager.get_risk_parameters(
                        market_regime, volatility_regime, signal_score.confidence
                    )

                    signals.loc[data.index[i], 'action'] = 'buy'
                    signals.loc[data.index[i], 'reason'] = enter_reason
                    signals.loc[data.index[i], 'confidence'] = signal_score.confidence

                    # 设置止损止盈
                    in_position = True
                    entry_price = current_price
                    stop_loss = current_price - current_atr * risk_params.stop_loss_atr_mult
                    take_profit = current_price + current_atr * risk_params.take_profit_atr_mult
                    trailing_stop = stop_loss

                    # 更新交易时间
                    self.last_trade_time = current_time
        
        return signals
    
    def _check_exit_conditions(self, current_price: float, stop_loss: float, 
                             take_profit: float, trailing_stop: float,
                             signal_score: SignalScore, market_regime: MarketRegime,
                             current_idx: int, data: pd.DataFrame) -> Tuple[bool, str]:
        """检查退出条件"""
        
        # 止损
        if current_price <= stop_loss:
            return True, "stop_loss"
        
        # 止盈
        if current_price >= take_profit:
            return True, "take_profit"
        
        # 信号恶化
        if signal_score.overall_score < 0.3 and signal_score.confidence < 0.4:
            return True, "signal_deterioration"
        
        # 市场状态变化
        if market_regime == MarketRegime.HIGH_VOLATILITY and signal_score.confidence < 0.5:
            return True, "high_volatility_exit"
        
        # 时间止损（持仓超过48小时）
        if current_idx > 48:  # 假设每小时一个数据点
            return True, "time_stop"
        
        return False, ""
    
    def _check_entry_conditions(self, signal_score: SignalScore,
                               market_regime: MarketRegime,
                               volatility_regime: VolatilityRegime,
                               current_time=None) -> Tuple[bool, str]:
        """检查入场条件"""

        # 基本信号质量检查 - 更严格的阈值
        if signal_score.overall_score < self.min_signal_score:
            return False, ""

        if signal_score.confidence < self.min_confidence:
            return False, ""

        # 交易间隔控制
        if (self.last_trade_time is not None and current_time is not None and
            hasattr(current_time, 'hour') and hasattr(self.last_trade_time, 'hour')):
            time_diff = abs((current_time - self.last_trade_time).total_seconds() / 3600)
            if time_diff < self.min_trade_interval:
                return False, ""

        # 风险控制检查
        if self.consecutive_losses >= self.max_consecutive_losses:
            return False, ""

        # 市场状态过滤 - 更严格
        if market_regime == MarketRegime.HIGH_VOLATILITY and signal_score.confidence < 0.8:
            return False, ""

        # 多重确认 - 提高阈值
        confirmations = 0

        if signal_score.trend_score > 0.8:  # 提高到0.8
            confirmations += 1

        if signal_score.momentum_score > 0.7:  # 提高到0.7
            confirmations += 1

        if signal_score.volume_score > 0.7:  # 提高到0.7
            confirmations += 1

        if signal_score.volatility_score > 0.7:  # 提高到0.7
            confirmations += 1

        # 至少需要3个强确认，或者4个确认
        if confirmations >= 4:
            return True, f"strong_confirmation_{confirmations}"
        elif confirmations >= 3 and signal_score.overall_score > 0.85:
            return True, f"high_quality_confirmation_{confirmations}"

        return False, ""
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            return 1  # 简化为固定仓位
        return 0

    def get_strategy_info(self) -> Dict:
        """获取策略信息"""
        return {
            'name': 'Elite Trading Strategy',
            'version': '1.0',
            'description': '基于最佳实践的全面优化策略',
            'features': [
                '多指标融合信号生成',
                '市场状态自适应检测',
                '智能风险管理',
                '信号质量评分',
                '多重确认机制'
            ]
        }


def print_strategy_summary():
    """打印策略总结"""
    print("=" * 80)
    print("Elite Trading Strategy - 精英交易策略")
    print("=" * 80)
    
    features = [
        "核心特性:",
        "✓ 多指标融合信号生成 (EMA + RSI + MACD + 布林带)",
        "✓ 市场状态自适应检测 (趋势/震荡/高波动/突破)",
        "✓ 波动率制度识别 (低/正常/高波动)",
        "✓ 智能风险管理 (动态止损止盈)",
        "✓ 信号质量评分系统",
        "✓ 多重确认机制",
        "✓ 追踪止损保护利润",
        "✓ 严格风险控制",
        "",
        "优化措施:",
        "• 信号过滤: 最小评分0.75 + 最小置信度0.7",
        "• 多重确认: 至少3个强指标确认才入场",
        "• 交易间隔: 最小4小时间隔控制频率",
        "• 自适应参数: 根据市场状态动态调整",
        "• 风险控制: 连续亏损限制 + 时间止损",
        "• 成本控制: 手续费0.02% + 滑点0.01%",
        "",
        "预期性能:",
        "• 胜率目标: 50-60%",
        "• 夏普比率: > 1.5", 
        "• 最大回撤: < 15%",
        "• 盈亏比: > 1.3",
        "• 年化收益: > 20%",
    ]
    
    for feature in features:
        print(feature)


if __name__ == "__main__":
    print_strategy_summary()
