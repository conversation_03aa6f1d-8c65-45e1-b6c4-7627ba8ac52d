"""
优化版策略
基于TradingView最佳实践和问题分析的改进版本
主要优化：
1. 智能止盈止损机制
2. 多时间框架趋势确认
3. 动量过滤器
4. 自适应参数调整
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
from strategy_backtester import BaseBacktestStrategy
from datetime import datetime


class OptimizedStrategy(BaseBacktestStrategy):
    """优化版EMA+ATR策略"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 基础参数
        self.atr_period = 14
        self.max_positions = 3  # 降低最大持仓
        self.order_volume = 1
        
        # 优化的止盈止损参数
        self.base_stop_mult = 1.5  # 降低基础止损倍数
        self.base_profit_mult = 2.0  # 降低基础止盈倍数
        self.trail_activation = 1.0  # ATR倍数，启动追踪止损
        self.trail_step = 0.5  # 追踪步长
        
        # 多时间框架参数
        self.fast_ema_periods = [8, 21]  # 快速EMA组合
        self.slow_ema_periods = [50, 200]  # 慢速EMA组合
        
        # 动量过滤器参数
        self.rsi_period = 14
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        self.momentum_period = 10
        
        # 趋势强度参数
        self.trend_strength_period = 20
        self.min_trend_strength = 0.3
        self.strong_trend_threshold = 0.6
        
        # 波动率自适应参数
        self.volatility_lookback = 20
        self.low_vol_threshold = 0.5
        self.high_vol_threshold = 1.5
        
        # 状态变量
        self.current_trend = "neutral"  # up, down, neutral
        self.trend_strength = 0.0
        self.market_regime = "normal"  # normal, high_vol, low_vol
        self.entry_price = 0
        self.stop_loss = 0
        self.take_profit = 0
        self.trailing_stop = 0
        self.is_trailing = False
        self.profit_targets = []  # 分批止盈目标
        
    def calculate_rsi(self, data: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def calculate_momentum(self, data: pd.Series, period: int) -> pd.Series:
        """计算动量指标"""
        return (data / data.shift(period) - 1) * 100
    
    def calculate_trend_strength(self, ema_fast: pd.Series, ema_slow: pd.Series, period: int) -> pd.Series:
        """计算趋势强度"""
        # 使用EMA差值的标准化作为趋势强度
        ema_diff = (ema_fast - ema_slow) / ema_slow
        return ema_diff.rolling(window=period).std()
    
    def calculate_volatility_regime(self, atr: pd.Series, period: int) -> pd.Series:
        """计算波动率状态"""
        atr_ma = atr.rolling(window=period).mean()
        atr_ratio = atr / atr_ma
        
        regime = pd.Series(index=atr.index, dtype='object')
        regime[atr_ratio < self.low_vol_threshold] = 'low_vol'
        regime[atr_ratio > self.high_vol_threshold] = 'high_vol'
        regime = regime.fillna('normal')
        
        return regime
    
    def adaptive_parameters(self, trend_strength: float, volatility_regime: str) -> Dict:
        """自适应参数调整"""
        params = {
            'stop_mult': self.base_stop_mult,
            'profit_mult': self.base_profit_mult,
            'trail_step': self.trail_step
        }
        
        # 根据趋势强度调整
        if trend_strength > self.strong_trend_threshold:
            # 强趋势：放宽止盈，收紧止损
            params['profit_mult'] *= 1.5
            params['stop_mult'] *= 0.8
            params['trail_step'] *= 0.7
        elif trend_strength < self.min_trend_strength:
            # 弱趋势：收紧止盈，放宽止损
            params['profit_mult'] *= 0.7
            params['stop_mult'] *= 1.2
        
        # 根据波动率调整
        if volatility_regime == 'high_vol':
            # 高波动：放宽止损，收紧止盈
            params['stop_mult'] *= 1.3
            params['profit_mult'] *= 0.8
        elif volatility_regime == 'low_vol':
            # 低波动：收紧止损，放宽止盈
            params['stop_mult'] *= 0.8
            params['profit_mult'] *= 1.2
        
        return params
    
    def setup_profit_targets(self, entry_price: float, atr_value: float, params: Dict) -> List[Tuple[float, float]]:
        """设置分批止盈目标"""
        targets = []
        
        # 第一目标：1.5倍ATR，平仓50%
        target1 = entry_price + atr_value * 1.5
        targets.append((target1, 0.5))
        
        # 第二目标：基础止盈倍数，平仓30%
        target2 = entry_price + atr_value * params['profit_mult']
        targets.append((target2, 0.3))
        
        # 第三目标：扩展止盈，平仓剩余20%
        target3 = entry_price + atr_value * params['profit_mult'] * 1.5
        targets.append((target3, 0.2))
        
        return targets
    
    def update_stops(self, current_price: float, atr_value: float, params: Dict) -> None:
        """更新止损止盈"""
        if len([t for t in self.trades if t.is_open]) == 0:
            return
            
        open_trade = [t for t in self.trades if t.is_open][-1]
        current_profit = current_price - open_trade.entry_price
        
        # 更新追踪止损
        if current_profit > atr_value * self.trail_activation and not self.is_trailing:
            self.is_trailing = True
            self.trailing_stop = current_price - atr_value * params['trail_step']
        elif self.is_trailing:
            # 更新追踪止损（只能向上调整）
            new_trailing = current_price - atr_value * params['trail_step']
            self.trailing_stop = max(self.trailing_stop, new_trailing)
        
        # 更新止损价格
        if self.is_trailing:
            self.stop_loss = self.trailing_stop
        else:
            # 使用固定止损
            self.stop_loss = open_trade.entry_price - atr_value * params['stop_mult']
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算技术指标
        data['ema_8'] = data['close'].ewm(span=8, adjust=False).mean()
        data['ema_21'] = data['close'].ewm(span=21, adjust=False).mean()
        data['ema_50'] = data['close'].ewm(span=50, adjust=False).mean()
        data['ema_200'] = data['close'].ewm(span=200, adjust=False).mean()
        
        # 计算ATR
        tr1 = data['high'] - data['low']
        tr2 = abs(data['high'] - data['close'].shift(1))
        tr3 = abs(data['low'] - data['close'].shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        data['atr'] = tr.rolling(window=self.atr_period).mean()
        
        # 计算RSI和动量
        data['rsi'] = self.calculate_rsi(data['close'], self.rsi_period)
        data['momentum'] = self.calculate_momentum(data['close'], self.momentum_period)
        
        # 计算趋势强度
        data['trend_strength'] = self.calculate_trend_strength(
            data['ema_8'], data['ema_21'], self.trend_strength_period
        )
        
        # 计算波动率状态
        data['volatility_regime'] = self.calculate_volatility_regime(
            data['atr'], self.volatility_lookback
        )
        
        # 初始化信号
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        signals['confidence'] = 0.0
        
        # 逐行生成信号
        for i in range(max(200, self.trend_strength_period), len(data)):
            current_data = data.iloc[i]
            
            # 获取当前指标值
            ema_8 = current_data['ema_8']
            ema_21 = current_data['ema_21']
            ema_50 = current_data['ema_50']
            ema_200 = current_data['ema_200']
            rsi = current_data['rsi']
            momentum = current_data['momentum']
            trend_strength = current_data['trend_strength']
            volatility_regime = current_data['volatility_regime']
            atr_value = current_data['atr']
            current_price = current_data['close']
            
            # 自适应参数
            params = self.adaptive_parameters(trend_strength, volatility_regime)
            
            # 更新止损
            self.update_stops(current_price, atr_value, params)
            
            # 检查持仓
            open_positions = len([t for t in self.trades if t.is_open])
            
            # 多时间框架趋势确认
            short_term_bullish = ema_8 > ema_21
            long_term_bullish = ema_50 > ema_200
            
            # 动量过滤
            momentum_bullish = momentum > 0 and rsi > 40 and rsi < 80
            momentum_bearish = momentum < 0 or rsi < 20 or rsi > 80
            
            # 趋势强度过滤
            strong_trend = trend_strength > self.min_trend_strength
            
            # 买入信号
            buy_condition = (
                short_term_bullish and
                long_term_bullish and
                momentum_bullish and
                strong_trend and
                open_positions < self.max_positions and
                volatility_regime != 'high_vol'  # 避免高波动期开仓
            )
            
            # 卖出信号
            sell_condition = False
            sell_reason = ""
            
            if open_positions > 0:
                # 止损
                if current_price <= self.stop_loss:
                    sell_condition = True
                    sell_reason = "stop_loss"
                # 技术卖出
                elif not short_term_bullish or momentum_bearish:
                    sell_condition = True
                    sell_reason = "technical_exit"
                # 分批止盈检查
                elif len(self.profit_targets) > 0:
                    for target_price, target_ratio in self.profit_targets:
                        if current_price >= target_price:
                            sell_condition = True
                            sell_reason = f"profit_target_{target_price:.2f}"
                            break
            
            # 设置信号
            if buy_condition and open_positions == 0:
                signals.loc[data.index[i], 'action'] = 'buy'
                signals.loc[data.index[i], 'reason'] = 'optimized_entry'
                signals.loc[data.index[i], 'confidence'] = min(1.0, trend_strength * 2)
                
                # 初始化止盈止损
                self.entry_price = current_price
                self.stop_loss = current_price - atr_value * params['stop_mult']
                self.take_profit = current_price + atr_value * params['profit_mult']
                self.is_trailing = False
                self.profit_targets = self.setup_profit_targets(current_price, atr_value, params)
                
            elif sell_condition and open_positions > 0:
                signals.loc[data.index[i], 'action'] = 'sell'
                signals.loc[data.index[i], 'reason'] = sell_reason
                signals.loc[data.index[i], 'confidence'] = 1.0
                
                # 重置状态
                self.entry_price = 0
                self.stop_loss = 0
                self.take_profit = 0
                self.is_trailing = False
                self.profit_targets = []
        
        return signals
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            # 基于信心度调整仓位
            confidence = signal.get('confidence', 1.0)
            base_size = self.order_volume
            
            # 高信心度时增加仓位
            if confidence > 0.8:
                return int(base_size * 1.5)
            elif confidence > 0.6:
                return base_size
            else:
                return max(1, int(base_size * 0.5))
        
        return 0


if __name__ == "__main__":
    # 测试代码
    from strategy_backtester import StrategyBacktester
    from original_strategy_adapter import generate_sample_data
    
    # 生成测试数据
    data = generate_sample_data()
    print(f"生成测试数据: {len(data)} 条记录")
    
    # 创建策略实例
    optimized_strategy = OptimizedStrategy()
    
    # 创建回测器
    backtester = StrategyBacktester()
    
    # 运行回测
    print("开始回测优化策略...")
    result = backtester.run_backtest(optimized_strategy, data)
    
    # 输出结果
    print(f"总交易次数: {len([t for t in result.trades if not t.is_open])}")
    print(f"胜率: {result.win_rate:.2%}")
    print(f"总收益率: {result.total_return:.2%}")
    print(f"最大回撤: {result.max_drawdown:.2%}")
    print(f"夏普比率: {result.sharpe_ratio:.3f}")
    print(f"盈亏比: {result.profit_factor:.2f}")
