#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化后Strategy3测试脚本
"""

import sys
import os
import time
import numpy as np
from datetime import datetime

def test_strategy_basic():
    """基础策略测试"""
    print("🧪 Strategy3 优化后基础测试")
    print("=" * 50)
    
    try:
        from Strategy3 import Strategy3
        print("✅ Strategy3 导入成功")
        
        # 创建策略实例
        strategy = Strategy3()
        print("✅ Strategy3 初始化成功")
        
        # 模拟市场数据
        test_data = {
            'price': 100.0,
            'volume': 1000,
            'timestamp': time.time(),
            'price_history': [98, 99, 100, 101, 102],
            'volatility': 0.02,
            'stability': 0.7,
            'profit': 0.01
        }
        
        print("📊 测试数据准备完成")
        
        # 执行决策测试
        decision = strategy.make_trading_decision(test_data)
        print("✅ 决策生成成功")
        
        # 分析决策结果
        action = decision.get('action', 'unknown')
        confidence = decision.get('confidence', 0)
        position_size = decision.get('position_size', 0)
        
        print(f"\n📈 决策结果分析:")
        print(f"  行动: {action}")
        print(f"  置信度: {confidence:.3f}")
        print(f"  仓位大小: {position_size:.3f}")
        
        # 评估优化效果
        if confidence >= 0.6:
            print("✅ 置信度良好 (≥0.6)")
        elif confidence >= 0.5:
            print("⚠️ 置信度中等 (0.5-0.6)")
        else:
            print("❌ 置信度偏低 (<0.5)")
        
        if action != 'hold' and position_size > 0:
            print("✅ 策略产生了交易信号")
        else:
            print("ℹ️ 策略选择保守策略")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_strategy_multiple_scenarios():
    """多场景测试"""
    print("\n🔄 多场景测试")
    print("=" * 50)
    
    try:
        from Strategy3 import Strategy3
        strategy = Strategy3()
        
        scenarios = [
            {
                'name': '高稳定低波动',
                'data': {'price': 100.0, 'volatility': 0.01, 'stability': 0.8, 'profit': 0.02}
            },
            {
                'name': '低稳定高波动',
                'data': {'price': 100.0, 'volatility': 0.05, 'stability': 0.3, 'profit': -0.01}
            },
            {
                'name': '中等市场状态',
                'data': {'price': 100.0, 'volatility': 0.025, 'stability': 0.6, 'profit': 0.005}
            },
            {
                'name': '极端负收益',
                'data': {'price': 100.0, 'volatility': 0.08, 'stability': 0.2, 'profit': -0.05}
            }
        ]
        
        results = []
        
        for scenario in scenarios:
            print(f"\n📊 测试场景: {scenario['name']}")
            
            test_data = {
                'price': scenario['data']['price'],
                'volume': 1000,
                'timestamp': time.time(),
                'price_history': [98, 99, 100, 101, 102],
                'volatility': scenario['data']['volatility'],
                'stability': scenario['data']['stability'],
                'profit': scenario['data']['profit']
            }
            
            decision = strategy.make_trading_decision(test_data)
            
            action = decision.get('action', 'unknown')
            confidence = decision.get('confidence', 0)
            position_size = decision.get('position_size', 0)
            
            print(f"  结果: {action}, 置信度: {confidence:.3f}, 仓位: {position_size:.3f}")
            
            results.append({
                'scenario': scenario['name'],
                'action': action,
                'confidence': confidence,
                'position_size': position_size
            })
        
        # 分析结果
        print(f"\n📈 多场景测试总结:")
        avg_confidence = np.mean([r['confidence'] for r in results])
        print(f"  平均置信度: {avg_confidence:.3f}")
        
        action_counts = {}
        for r in results:
            action_counts[r['action']] = action_counts.get(r['action'], 0) + 1
        
        print(f"  行动分布: {action_counts}")
        
        # 检查风险管理
        high_risk_scenarios = [r for r in results if r['scenario'] in ['低稳定高波动', '极端负收益']]
        conservative_actions = sum(1 for r in high_risk_scenarios if r['action'] == 'hold' or r['position_size'] < 0.05)
        
        if conservative_actions >= len(high_risk_scenarios) * 0.8:
            print("✅ 风险管理表现良好")
        else:
            print("⚠️ 风险管理需要改进")
        
        return True
        
    except Exception as e:
        print(f"❌ 多场景测试失败: {e}")
        return False

def performance_comparison():
    """性能对比分析"""
    print("\n📊 性能对比分析")
    print("=" * 50)
    
    print("优化前问题:")
    print("  - 收益率: -69.52%")
    print("  - 夏普率: -69168.40")
    print("  - 胜率: 0%")
    print("  - 主要问题: 决策融合逻辑缺陷、风险管理不足、信号质量差")
    
    print("\n优化措施:")
    print("  ✅ 决策融合机制优化 - 自适应权重、置信度阈值提升")
    print("  ✅ 风险管理系统强化 - 动态止损、仓位控制、风险评估")
    print("  ✅ 仓位管理策略改进 - 动态仓位计算、资金利用优化")
    print("  ✅ 信号过滤机制增强 - 严格过滤条件、信号质量验证")
    print("  ✅ 模糊推理系统优化 - 改进规则和隶属函数")
    
    print("\n预期改进:")
    print("  🎯 置信度阈值从0.5提升到0.6+")
    print("  🎯 仓位管理更加动态和保守")
    print("  🎯 风险控制更加严格")
    print("  🎯 信号质量显著提升")

def main():
    """主测试函数"""
    print("🚀 Strategy3 优化后综合测试")
    print("=" * 70)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # 基础测试
    basic_success = test_strategy_basic()
    
    if basic_success:
        # 多场景测试
        multi_success = test_strategy_multiple_scenarios()
        
        # 性能对比
        performance_comparison()
        
        print("\n" + "=" * 70)
        if basic_success and multi_success:
            print("🎉 所有测试通过！Strategy3优化成功")
        else:
            print("⚠️ 部分测试失败，需要进一步优化")
    else:
        print("\n❌ 基础测试失败，无法继续")
    
    print("=" * 70)

if __name__ == "__main__":
    main()
