from typing import Literal
import numpy as np
from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    
    # 策略选择参数
    strategy_mode: Literal["KDJ_BOLL", "EMA_ATR", "COMBINE"] = Field(default="COMBINE", title="策略模式")
    trade_direction: Literal["buy"] = Field(default="buy", title="交易方向")
    
    # 通用参数
    order_volume: int = Field(default=1, title="报单数量")
    max_position: int = Field(default=5, title="最大持仓单数", ge=1, le=10)
    delay_trade: int = Field(default=5, title="开盘延迟交易时间（分钟）")
    
    # KDJ+BOLL参数
    tech_type: Literal["KDJ", "KDJ+BOLL", "MACD", "MA"] = Field(default="KDJ+BOLL", title="技术指标")
    N: int = Field(default=9, title="KDJ 参数1")
    M1: int = Field(default=3, title="KDJ 参数2")
    M2: int = Field(default=3, title="KDJ 参数3")
    kdj_overbought: int = Field(default=80, title="KDJ 超买阈值")
    kdj_oversold: int = Field(default=20, title="KDJ 超卖阈值")
    boll_period: int = Field(default=20, title="布林带周期")
    boll_dev: float = Field(default=2.0, title="布林带标准差倍数")
    
    # EMA+ATR参数
    ema_mode: Literal["manual", "auto"] = Field(default="auto", title="EMA工作模式")
    ema_fast_period: int = Field(default=5, title="快EMA周期")
    ema_mid_period: int = Field(default=15, title="中EMA周期")
    ema_slow_period: int = Field(default=30, title="慢EMA周期")
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    stop_mult: float = Field(default=2.2, title="止损倍数", ge=1.5, le=2.5)
    profit_mult: float = Field(default=3.0, title="止盈倍数", ge=2.5, le=3.5)
    trail_step: float = Field(default=1.0, title="追踪步长", ge=0.5, le=1.5)
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    
    # 止损参数
    stop_loss_mode: Literal["fixed", "atr", "boll", "dynamic"] = Field(default="dynamic", title="止损模式")
    stop_profit_mode: Literal["fixed", "atr", "boll", "dynamic"] = Field(default="dynamic", title="止盈模式")
    trailing_stop: bool = Field(default=True, title="是否启用追踪止盈止损")
    stop_loss_ratio: float = Field(default=0.02, title="止损比例")
    stop_profit_ratio: float = Field(default=0.03, title="止盈比例")
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")


class State(BaseState):
    """状态映射模型"""
    # KDJ指标
    k: float = Field(default=0, title="K")
    d: float = Field(default=0, title="D")
    j: float = Field(default=0, title="J")
    
    # BOLL指标
    boll_upper: float = Field(default=0, title="布林带上轨")
    boll_lower: float = Field(default=0, title="布林带下轨")
    
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # ATR指标
    atr: float = Field(default=0, title="真实波幅")
    
    # 趋势状态
    trend_type: Literal["A", "B"] = Field(default="A", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    
    # 信号过滤
    volume_filter: bool = Field(default=False, title="成交量过滤信号")
    
    # 止损止盈
    stop_loss_price: float = Field(default=0, title="止损点位")
    stop_profit_price: float = Field(default=0, title="止盈点位")
    trailing_stop_active: bool = Field(default=False, title="追踪止盈止损是否激活")
    trailing_stop_price: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 统计信息
    stop_loss_count: int = Field(default=0, title="当日止损次数")
    stop_loss_amount: float = Field(default=0.0, title="当日止损金额")
    stop_profit_count: int = Field(default=0, title="当日止盈次数")
    stop_profit_amount: float = Field(default=0.0, title="当日止盈金额")
    last_trade_date: str = Field(default="", title="上次交易日期")


class CombinedStrategy(BaseStrategy):
    """KDJ+BOLL与三周期EMA+ATR综合策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        self.cover_signal: bool = False
        self.short_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 15  # 趋势判断周期
        self.trend_count = 0
        self.price_history = []
        self.trend_history = []  # 记录历史趋势状态
        self.volatility_history = []  # 记录历史波动率
        
        # 趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.5  # 波动率阈值
        self.min_trend_duration = 5  # 最小趋势持续周期
        self.max_trend_duration = 30  # 最大趋势持续周期
        
        # 参数组合
        self.param_sets = {
            "A": {  # 趋势型参数
                "ema": [5, 15, 30],
                "atr_period": 14,
                "stop_mult": 2.2,
                "profit_mult": 3.0,
                "trail_step": 1.0
            },
            "B": {  # 震荡型参数
                "ema": [3, 10, 20],
                "atr_period": 21,
                "stop_mult": 1.8,
                "profit_mult": 2.5,
                "trail_step": 0.5
            }
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        
        # KDJ计算中间变量
        self.kk = 0
        self.dd = 0
        self.jj = 0
        
        # MACD计算中间变量
        self.macd1 = 0
        self.signall1 = 0

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        indicators = {
            "Order Volume": self.params_map.order_volume,
            "Max Position": self.params_map.max_position,
            "Stop Loss Count": self.state_map.stop_loss_count,
            "Stop Loss Amount": self.state_map.stop_loss_amount,
            "Stop Profit Count": self.state_map.stop_profit_count,
            "Stop Profit Amount": self.state_map.stop_profit_amount,
        }
        
        if self.params_map.strategy_mode in ["KDJ_BOLL", "COMBINE"]:
            indicators.update({
                "BOLL Upper": self.state_map.boll_upper,
                "BOLL Lower": self.state_map.boll_lower,
                "Stop Loss": self.state_map.stop_loss_price,
                "Stop Profit": self.state_map.stop_profit_price,
            })
        
        if self.params_map.strategy_mode in ["EMA_ATR", "COMBINE"]:
            indicators.update({
                "EMA_FAST": self.state_map.ema_fast,
                "EMA_MID": self.state_map.ema_mid,
                "EMA_SLOW": self.state_map.ema_slow,
                "ATR": self.state_map.atr,
                "Trailing Stop": self.state_map.trailing_stop_price,
            })
        
        return indicators

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        indicators = {}
        
        if self.params_map.strategy_mode in ["KDJ_BOLL", "COMBINE"]:
            indicators.update({
                "K": self.state_map.k,
                "D": self.state_map.d,
                "J": self.state_map.j,
            })
        
        if self.params_map.strategy_mode in ["EMA_ATR", "COMBINE"]:
            indicators.update({
                "Trend Strength": self.state_map.trend_strength,
                "Volatility": self.state_map.volatility,
                "Trend Duration": self.state_map.trend_duration,
            })
        
        indicators.update({
            "Trailing Active": int(self.state_map.trailing_stop_active),
        })
        
        return indicators

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick

        # 检查是否为新交易日，重置日内统计数据
        current_date = tick.datetime.strftime("%Y-%m-%d")
        if self.state_map.last_trade_date != current_date:
            self.state_map.last_trade_date = current_date
            # 每个交易日开始时重置统计数据
            self.state_map.stop_loss_count = 0  
            self.state_map.stop_loss_amount = 0.0
            self.state_map.stop_profit_count = 0
            self.state_map.stop_profit_amount = 0.0
            self.buy_signal = True  # 允许生成买入信号

        # 检查是否接近收盘时间
        if self.is_closing_time(tick.datetime):
            self.close_all_positions()
            self.buy_signal = False  # 禁止生成新的买入信号
            return

        self.kline_generator.tick_to_kline(tick)

    def is_closing_time(self, current_time):
        """判断是否接近收盘时间"""
        return current_time.strftime("%H:%M:%S") in ["14:54:00", "22:54:00"]

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        super().on_trade(trade, log)
        self.order_id = None
        
        # 更新入场价格和持仓量
        if trade.direction == "buy":
            self.entry_price = trade.price
            self.position_size += trade.volume
            self.state_map.highest_price = trade.price
            self.state_map.lowest_price = trade.price
        elif trade.direction == "sell":
            self.position_size -= trade.volume
            if self.position_size <= 0:
                self.entry_price = 0
                self.position_size = 0
                self.is_trailing = False
                self.state_map.highest_price = 0
                self.state_map.lowest_price = 0
                self.state_map.current_profit = 0
                self.state_map.max_profit = 0

    def on_start(self):
        """策略启动逻辑"""
        self.kline_generator = KLineGenerator(
            callback=self.callback,
            real_time_callback=self.real_time_callback,
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            style=self.params_map.kline_style
        )
        self.kline_generator.push_history_data()

        super().on_start()

        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.cover_signal = False
        self.short_signal = False
        self.tick = None
        
        # 初始化动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        
        # 初始化参数
        if self.params_map.ema_mode == "manual":
            self.current_params = {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "atr_period": self.params_map.atr_period,
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = None  # 自动模式等待趋势判断

        self.update_status_bar()

    def on_stop(self):
        """策略停止逻辑"""
        super().on_stop()
        self.close_all_positions()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 计算指标
        self.calc_indicator()
        
        # 计算信号
        self.calc_signal(kline)
        
        # 信号执行
        self.exec_signal()
        
        # 线图更新
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        self.calc_indicator()

        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def bollinger(self, n: int = 20, dev: float = 2.0):
        """计算布林带
        Args:
            n: 移动平均周期
            dev: 标准差倍数
        Returns:
            upper, lower: 上轨和下轨
        """
        close_prices = self.kline_generator.producer.close
        if len(close_prices) < n:
            return 0, 0
            
        ma = np.mean(close_prices[-n:])
        std = np.std(close_prices[-n:])
        upper = ma + dev * std
        lower = ma - dev * std
        return upper, lower

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        if self.params_map.strategy_mode == "KDJ_BOLL":
            return
            
        # 更新价格历史
        self.price_history.append(kline.close)
        if len(self.price_history) > self.trend_period:
            self.price_history.pop(0)
            
        if len(self.price_history) == self.trend_period:
            # 1. 计算价格变化率
            price_changes = np.diff(self.price_history)
            
            # 2. 计算方向一致性
            direction_consistency = abs(np.sum(np.sign(price_changes))) / len(price_changes)
            
            # 3. 计算波动率
            volatility = np.std(price_changes)
            self.volatility_history.append(volatility)
            if len(self.volatility_history) > 10:
                self.volatility_history.pop(0)
            
            # 4. 计算趋势强度
            # 使用EMA斜率作为趋势强度指标
            ema_slopes = []
            for period in [5, 10, 20]:
                ema = self.kline_generator.producer.ema(period, array=True)
                if len(ema) >= 2:
                    slope = (ema[-1] - ema[-2]) / ema[-2]
                    ema_slopes.append(slope)
            
            # 计算趋势强度（EMA斜率的一致性）
            if ema_slopes:
                trend_strength = abs(np.mean(ema_slopes))
                self.state_map.trend_strength = min(1.0, trend_strength)
            
            # 5. 计算趋势持续性
            if self.state_map.is_trending:
                self.state_map.trend_duration += 1
                if self.state_map.trend_duration > self.max_trend_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
            else:
                self.state_map.trend_duration = 0
            
            # 6. 综合判断趋势
            # 使用多个指标综合判断趋势
            is_trending = (
                direction_consistency > self.trend_threshold and  # 方向一致性
                volatility > self.volatility_threshold and  # 波动率
                self.state_map.trend_strength > 0.3 and  # 趋势强度
                (self.state_map.trend_duration >= self.min_trend_duration or  # 趋势持续性
                 direction_consistency > 0.8)  # 强趋势可以降低持续性要求
            )
            
            # 7. 更新趋势状态
            if is_trending != self.state_map.is_trending:
                self.state_map.is_trending = is_trending
                self.state_map.trend_type = "A" if is_trending else "B"
                self.trend_history.append(is_trending)
                if len(self.trend_history) > 20:
                    self.trend_history.pop(0)
                
                # 更新参数
                self.current_params = self.param_sets[self.state_map.trend_type]
            
            # 8. 更新状态映射
            self.state_map.volatility = volatility
            
            # 9. 趋势反转预警
            if len(self.trend_history) >= 3:
                recent_trends = self.trend_history[-3:]
                if recent_trends.count(True) == 2 and recent_trends[-1] == False:
                    # 趋势可能反转，调整参数
                    if self.state_map.trend_type == "A":
                        self.current_params["stop_mult"] *= 0.9  # 收紧止损
                        self.current_params["profit_mult"] *= 0.9  # 收紧止盈
                    else:
                        self.current_params["stop_mult"] *= 1.1  # 放宽止损
                        self.current_params["profit_mult"] *= 1.1  # 放宽止盈

    def calc_indicator(self) -> None:
        """计算指标"""
        # 计算KDJ指标
        if self.params_map.strategy_mode in ["KDJ_BOLL", "COMBINE"]:
            k, d, j = self.kline_generator.producer.kdj(
                fastk_period=self.params_map.N,
                slowk_period=self.params_map.M1,
                slowd_period=self.params_map.M2,
                array=True
            )

            (
                (self.kk, self.state_map.k),
                (self.dd, self.state_map.d),
                (self.jj, self.state_map.j)
            ) = np.round((k[-2:], d[-2:], j[-2:]), 2)

            # 使用新的布林带计算方法
            self.state_map.boll_upper, self.state_map.boll_lower = self.bollinger(
                self.params_map.boll_period, 
                self.params_map.boll_dev
            )
            self.state_map.boll_upper = round(self.state_map.boll_upper, 2)
            self.state_map.boll_lower = round(self.state_map.boll_lower, 2)

            # 成交量过滤
            self.state_map.volume_filter = self.kline_generator.producer.volume[-1] > np.mean(
                self.kline_generator.producer.volume[-10:]
            )

        # 计算EMA和ATR指标
        if self.params_map.strategy_mode in ["EMA_ATR", "COMBINE"]:
            if self.current_params is None:
                self.current_params = self.param_sets["A"]  # 默认使用趋势型参数
                
            # 计算EMA
            ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
            ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
            ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
            
            self.state_map.ema_fast = round(ema_fast[-1], 2) if len(ema_fast) > 0 else 0
            self.state_map.ema_mid = round(ema_mid[-1], 2) if len(ema_mid) > 0 else 0
            self.state_map.ema_slow = round(ema_slow[-1], 2) if len(ema_slow) > 0 else 0
            
            # 计算ATR
            atr, _ = self.kline_generator.producer.atr(self.current_params["atr_period"])
            self.state_map.atr = round(atr, 2) if atr is not None else 0

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损"""
        if self.position_size <= 0:
            return
            
        # 如果使用固定止盈止损，则不更新动态止损
        if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
            return
            
        # 更新最高价和最低价
        if current_price > self.state_map.highest_price:
            self.state_map.highest_price = current_price
        if current_price < self.state_map.lowest_price:
            self.state_map.lowest_price = current_price
        
        # 计算当前盈亏
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        
        # 更新最大盈亏
        if self.state_map.current_profit > self.state_map.max_profit:
            self.state_map.max_profit = self.state_map.current_profit
        
        # 动态调整止损
        if self.state_map.current_profit > 0:
            # 当盈利超过ATR的1倍时，启动追踪止损
            if not self.is_trailing and self.state_map.current_profit > self.state_map.atr:
                self.is_trailing = True
                self.state_map.trailing_stop_active = True
            
            if self.is_trailing:
                # 使用最高价回撤ATR的倍数作为止损
                self.state_map.stop_loss_price = round(
                    self.state_map.highest_price - self.state_map.atr * self.current_params["stop_mult"], 2
                )
                self.state_map.trailing_stop_price = self.state_map.stop_loss_price
        else:
            # 未盈利时使用初始止损
            self.state_map.stop_loss_price = round(
                self.entry_price - self.state_map.atr * self.current_params["stop_mult"], 2
            )
            self.state_map.trailing_stop_price = self.state_map.stop_loss_price
        
        # 动态调整止盈
        self.state_map.stop_profit_price = round(
            self.entry_price + self.state_map.atr * self.current_params["profit_mult"], 2
        )

    def calc_stop_loss_profit(self):
        """计算止盈止损价格"""
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position <= 0:
            return
            
        # 获取当前价格，如果tick为空则使用K线收盘价
        current_price = self.tick.last_price if self.tick else self.kline_generator.producer.close[-1]
        
        # 止损计算
        if self.params_map.stop_loss_mode == "fixed":
            self.state_map.stop_loss_price = current_price * (1 - self.params_map.stop_loss_ratio)
        elif self.params_map.stop_loss_mode == "atr":
            self.state_map.stop_loss_price = current_price - self.state_map.atr * self.params_map.stop_mult
        elif self.params_map.stop_loss_mode == "boll":
            self.state_map.stop_loss_price = self.state_map.boll_lower
        elif self.params_map.stop_loss_mode == "dynamic":
            self.update_dynamic_stops(current_price)
        
        # 止盈计算
        if self.params_map.stop_profit_mode == "fixed":
            self.state_map.stop_profit_price = current_price * (1 + self.params_map.stop_profit_ratio)
        elif self.params_map.stop_profit_mode == "atr":
            self.state_map.stop_profit_price = current_price + self.state_map.atr * self.params_map.profit_mult
        elif self.params_map.stop_profit_mode == "boll":
            self.state_map.stop_profit_price = self.state_map.boll_upper
        elif self.params_map.stop_profit_mode == "dynamic":
            self.update_dynamic_stops(current_price)
        
        # 启用追踪止盈止损
        if self.params_map.trailing_stop and self.params_map.stop_loss_mode != "dynamic":
            self.state_map.trailing_stop_active = True
            self.state_map.stop_loss_price = max(self.state_map.stop_loss_price, current_price * 0.98)
            self.state_map.stop_profit_price = max(self.state_map.stop_profit_price, current_price * 1.02)

    def calc_signal(self, kline: KLineData):
        """计算交易信号"""
        # 计算趋势
        self.calc_trend(kline)
        
        # 计算指标
        self.calc_indicator()
        
        # 更新动态止盈止损
        if self.tick:
            self.calc_stop_loss_profit()
        
        # 生成交易信号
        kdj_boll_signal = False
        ema_atr_signal = False
        
        # KDJ+BOLL信号
        if self.params_map.strategy_mode in ["KDJ_BOLL", "COMBINE"]:
            current_price = self.tick.last_price if self.tick else kline.close
            kdj_boll_signal = (
                self.state_map.j < self.params_map.kdj_oversold
                and current_price > self.state_map.boll_lower
                and self.state_map.volume_filter
            )
        
        # EMA+ATR信号
        if self.params_map.strategy_mode in ["EMA_ATR", "COMBINE"]:
            if self.state_map.is_trending:
                # 趋势型交易信号
                ema_atr_signal = (
                    self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
                    self.state_map.trend_strength > 0.4  # 增加趋势强度要求
                )
            else:
                # 震荡型交易信号
                ema_atr_signal = (
                    self.state_map.ema_fast < self.state_map.ema_mid and
                    self.state_map.ema_mid > self.state_map.ema_slow and
                    self.state_map.volatility < self.volatility_threshold * 1.5  # 控制波动率
                )
        
        # 综合信号判断
        if self.params_map.strategy_mode == "KDJ_BOLL":
            self.buy_signal = kdj_boll_signal
        elif self.params_map.strategy_mode == "EMA_ATR":
            self.buy_signal = ema_atr_signal
        else:  # COMBINE模式
            self.buy_signal = kdj_boll_signal and ema_atr_signal
        
        # 更新价格
        self.long_price = kline.close
        if self.tick:
            self.long_price = self.tick.ask_price1
            if self.params_map.price_type == "D2":
                self.long_price = self.tick.ask_price2

    def exec_signal(self):
        """执行交易信号并处理止盈止损"""
        self.signal_price = 0
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        if self.order_id is not None:
            self.cancel_order(self.order_id)

        # 检查是否达到最大持仓数
        if position.net_position >= self.params_map.max_position:
            self.buy_signal = False

        # 止盈止损逻辑
        if position.net_position > 0 and self.tick:
            current_price = self.tick.last_price
            bid_price = self.tick.bid_price1
            
            # 止损检查
            if current_price <= self.state_map.stop_loss_price:
                self.sell_signal = True
                self.signal_price = -bid_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=bid_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
                self.state_map.stop_loss_count += 1
                self.state_map.stop_loss_amount += abs(position.net_position * (current_price - self.entry_price))
                if self.state_map.stop_loss_count >= 3:
                    self.buy_signal = False
            
            # 止盈检查
            elif current_price >= self.state_map.stop_profit_price:
                self.sell_signal = True
                self.signal_price = -bid_price
                if self.trading:
                    self.order_id = self.auto_close_position(
                        exchange=self.params_map.exchange,
                        instrument_id=self.params_map.instrument_id,
                        price=bid_price,
                        volume=position.net_position,
                        order_direction="sell"
                    )
                self.state_map.stop_profit_count += 1
                self.state_map.stop_profit_amount += abs(position.net_position * (current_price - self.entry_price))

        # 开仓逻辑
        if self.buy_signal and position.net_position < self.params_map.max_position:
            self.signal_price = self.long_price
            if self.trading:
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=self.long_price,
                    order_direction="buy"
                )

    def close_all_positions(self):
        """收盘前自动平仓"""
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position > 0:
            # 使用当前价格作为平仓价格
            current_price = self.tick.last_price if self.tick else self.kline_generator.producer.close[-1]
            bid_price = self.tick.bid_price1 if self.tick else current_price
            
            self.auto_close_position(
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                price=bid_price,
                volume=position.net_position,
                order_direction="sell"
            )
            
            # 更新统计信息
            profit = (bid_price - self.entry_price) * position.net_position
            if profit > 0:
                self.state_map.stop_profit_count += 1
                self.state_map.stop_profit_amount += profit
            else:
                self.state_map.stop_loss_count += 1
                self.state_map.stop_loss_amount += abs(profit)
            
            # 重置动态止盈止损相关变量
            self.entry_price = 0
            self.position_size = 0
            self.is_trailing = False
            self.state_map.highest_price = 0
            self.state_map.lowest_price = 0
            self.state_map.current_profit = 0
            self.state_map.max_profit = 0
            self.state_map.trailing_stop_active = False