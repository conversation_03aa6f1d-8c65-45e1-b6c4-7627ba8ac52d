#!/usr/bin/env python3
"""
Volume SuperTrend AI - 独立实现版本
基于TradingView Volume SuperTrend AI指标的Python实现
集成k-NN机器学习算法进行智能趋势预测

核心特性:
1. Volume-Weighted Moving Average (VWMA) 计算
2. k-Nearest Neighbors (k-NN) 机器学习预测
3. 动态SuperTrend计算
4. 自适应参数优化
5. 实时信号生成

作者: AI Assistant
日期: 2025-06-27
"""

import numpy as np
import time
from collections import deque
from typing import Dict, List, Optional, Tuple
import threading
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关导入
try:
    from sklearn.neighbors import KNeighborsRegressor
    from sklearn.preprocessing import StandardScaler
    ML_AVAILABLE = True
    print("✓ scikit-learn 可用，启用完整AI功能")
except ImportError:
    ML_AVAILABLE = False
    print("⚠ scikit-learn 不可用，使用简化AI功能")


class VolumeSuperTrendAI:
    """Volume SuperTrend AI 核心算法实现"""
    
    def __init__(self, k_neighbors=5, data_points=20, price_trend_len=30, prediction_len=100):
        self.k = k_neighbors
        self.data_points = data_points
        self.price_trend_len = price_trend_len
        self.prediction_len = prediction_len
        
        # 数据存储
        self.price_history = deque(maxlen=500)
        self.volume_history = deque(maxlen=500)
        self.supertrend_history = deque(maxlen=500)
        self.labels_history = deque(maxlen=500)
        
        # ML模型
        if ML_AVAILABLE:
            try:
                self.knn_model = KNeighborsRegressor(n_neighbors=k_neighbors, weights='distance')
                self.scaler = StandardScaler()
            except Exception as e:
                print(f"ML模型初始化失败: {e}")
                self.knn_model = None
                self.scaler = None
        else:
            self.knn_model = None
            self.scaler = None
            
        self.is_trained = False
        
        # SuperTrend状态
        self.prev_supertrend = None
        self.prev_direction = None
        self.prev_upper_band = None
        self.prev_lower_band = None
        
    def calculate_vwma(self, prices: List[float], volumes: List[float], period: int) -> float:
        """计算成交量加权移动平均"""
        if len(prices) < period or len(volumes) < period:
            return prices[-1] if prices else 0.0
            
        price_volume = sum(p * v for p, v in zip(prices[-period:], volumes[-period:]))
        total_volume = sum(volumes[-period:])
        
        return price_volume / total_volume if total_volume > 0 else prices[-1]
    
    def calculate_atr(self, highs: List[float], lows: List[float], closes: List[float], period: int = 14) -> float:
        """计算平均真实波幅 (ATR)"""
        if len(highs) < period or len(lows) < period or len(closes) < period:
            return 0.01
        
        true_ranges = []
        for i in range(1, len(closes)):
            high_low = highs[i] - lows[i]
            high_close = abs(highs[i] - closes[i-1])
            low_close = abs(lows[i] - closes[i-1])
            true_range = max(high_low, high_close, low_close)
            true_ranges.append(true_range)
        
        return sum(true_ranges[-period:]) / period if true_ranges else 0.01
    
    def calculate_supertrend(self, prices: List[float], volumes: List[float], 
                           atr_values: List[float], factor: float = 3.0, period: int = 10) -> Tuple[float, int]:
        """计算Volume SuperTrend"""
        if len(prices) < period:
            return prices[-1] if prices else 0.0, 1
            
        # 计算VWMA
        vwma = self.calculate_vwma(prices, volumes, period)
        
        # 获取ATR
        atr = atr_values[-1] if atr_values else 0.01
        
        # 计算上下轨
        upper_band = vwma + factor * atr
        lower_band = vwma - factor * atr
        
        # 确定趋势方向
        current_price = prices[-1]
        
        if self.prev_supertrend is not None and self.prev_direction is not None:
            # 重新定义上下轨
            if upper_band < self.prev_upper_band or prices[-2] > self.prev_upper_band:
                upper_band = upper_band
            else:
                upper_band = self.prev_upper_band
                
            if lower_band > self.prev_lower_band or prices[-2] < self.prev_lower_band:
                lower_band = lower_band
            else:
                lower_band = self.prev_lower_band
            
            # 确定方向
            if self.prev_supertrend == self.prev_upper_band:
                if current_price > upper_band:
                    direction = -1  # 上升趋势
                else:
                    direction = 1   # 下降趋势
            else:
                if current_price < lower_band:
                    direction = 1   # 下降趋势
                else:
                    direction = -1  # 上升趋势
        else:
            direction = 1 if current_price < vwma else -1
            
        # 确定SuperTrend值
        if direction == -1:
            supertrend = lower_band
        else:
            supertrend = upper_band
            
        # 保存当前值供下次使用
        self.prev_supertrend = supertrend
        self.prev_direction = direction
        self.prev_upper_band = upper_band
        self.prev_lower_band = lower_band
        
        return supertrend, direction
    
    def update_data(self, price: float, volume: float, high: float = None, low: float = None):
        """更新数据"""
        self.price_history.append(price)
        self.volume_history.append(volume)
        
        # 如果没有提供高低价，使用价格的小幅波动
        if high is None:
            high = price * 1.005
        if low is None:
            low = price * 0.995
        
        # 计算ATR
        if len(self.price_history) >= 15:
            highs = [p * 1.005 for p in list(self.price_history)[-15:]]  # 模拟高价
            lows = [p * 0.995 for p in list(self.price_history)[-15:]]   # 模拟低价
            closes = list(self.price_history)[-15:]
            atr = self.calculate_atr(highs, lows, closes)
        else:
            atr = price * 0.01  # 默认ATR为价格的1%
        
        # 计算SuperTrend
        supertrend, direction = self.calculate_supertrend(
            list(self.price_history), 
            list(self.volume_history), 
            [atr]
        )
        
        self.supertrend_history.append(supertrend)
        
        # 计算标签（用于训练）
        if len(self.price_history) >= self.price_trend_len:
            price_trend = self.calculate_vwma(
                list(self.price_history), 
                list(self.volume_history), 
                self.price_trend_len
            )
            
            if len(self.supertrend_history) >= self.prediction_len:
                st_trend = sum(list(self.supertrend_history)[-self.prediction_len:]) / self.prediction_len
                label = 1 if price_trend > st_trend else 0
            else:
                label = 1 if price > supertrend else 0
                
            self.labels_history.append(label)
        
        return supertrend, direction
    
    def train_model(self):
        """训练k-NN模型"""
        if not ML_AVAILABLE or len(self.supertrend_history) < self.data_points or not self.knn_model or not self.scaler:
            return False
            
        try:
            # 准备训练数据
            X = []
            y = []
            
            for i in range(self.data_points, len(self.supertrend_history)):
                features = list(self.supertrend_history)[i-self.data_points:i]
                if i < len(self.labels_history):
                    label = self.labels_history[i]
                    X.append(features)
                    y.append(label)
            
            if len(X) < self.k:
                return False
                
            X = np.array(X)
            y = np.array(y)
            
            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)
            
            # 训练模型
            self.knn_model.fit(X_scaled, y)
            self.is_trained = True
            
            return True
            
        except Exception as e:
            print(f"训练模型失败: {e}")
            return False
    
    def predict_signal(self, current_supertrend: float) -> Tuple[float, str]:
        """预测交易信号"""
        if not self.is_trained or not ML_AVAILABLE or not self.knn_model or not self.scaler:
            # 使用简单规则作为后备
            if len(self.price_history) > 0 and len(self.supertrend_history) > 0:
                current_price = self.price_history[-1]
                if current_price > current_supertrend:
                    return 1.0, "bullish"
                else:
                    return 0.0, "bearish"
            return 0.5, "neutral"
        
        try:
            # 准备预测数据
            if len(self.supertrend_history) < self.data_points:
                return 0.5, "neutral"
                
            features = list(self.supertrend_history)[-self.data_points:]
            features = np.array(features).reshape(1, -1)
            features_scaled = self.scaler.transform(features)
            
            # 预测
            prediction = self.knn_model.predict(features_scaled)[0]
            
            # 转换为信号
            if prediction > 0.7:
                return prediction, "bullish"
            elif prediction < 0.3:
                return prediction, "bearish"
            else:
                return prediction, "neutral"
                
        except Exception as e:
            print(f"预测失败: {e}")
            return 0.5, "neutral"
    
    def get_signal_strength(self, current_price: float) -> Dict:
        """获取综合信号强度"""
        if len(self.supertrend_history) == 0:
            return {"strength": 0.5, "direction": "neutral", "confidence": 0.0}
        
        current_supertrend = self.supertrend_history[-1]
        
        # 获取AI预测
        ai_strength, ai_signal = self.predict_signal(current_supertrend)
        
        # 计算价格相对位置
        price_ratio = current_price / current_supertrend if current_supertrend > 0 else 1.0
        
        # 计算成交量确认
        volume_confirmation = 1.0
        if len(self.volume_history) >= 10:
            recent_volume = sum(list(self.volume_history)[-5:]) / 5
            avg_volume = sum(list(self.volume_history)[-10:]) / 10
            volume_confirmation = min(recent_volume / avg_volume, 2.0) / 2.0
        
        # 综合信号强度
        if ai_signal == "bullish" and price_ratio > 1.01:
            strength = min(ai_strength * volume_confirmation * 1.2, 1.0)
            direction = "bullish"
        elif ai_signal == "bearish" and price_ratio < 0.99:
            strength = max((1 - ai_strength) * volume_confirmation * 1.2, 0.0)
            direction = "bearish"
        else:
            strength = 0.5
            direction = "neutral"
        
        confidence = abs(strength - 0.5) * 2  # 0-1之间的置信度
        
        return {
            "strength": strength,
            "direction": direction,
            "confidence": confidence,
            "ai_signal": ai_signal,
            "ai_strength": ai_strength,
            "price_ratio": price_ratio,
            "volume_confirmation": volume_confirmation,
            "supertrend": current_supertrend
        }


class TradingSignalGenerator:
    """交易信号生成器"""
    
    def __init__(self):
        self.vst_ai = VolumeSuperTrendAI()
        self.signal_history = deque(maxlen=100)
        
    def update_market_data(self, price: float, volume: float, high: float = None, low: float = None):
        """更新市场数据"""
        return self.vst_ai.update_data(price, volume, high, low)
    
    def generate_trading_signal(self, current_price: float, risk_tolerance: float = 0.7) -> Dict:
        """生成交易信号"""
        signal_data = self.vst_ai.get_signal_strength(current_price)
        
        # 根据风险承受度调整信号
        if signal_data["confidence"] < risk_tolerance:
            action = "hold"
        elif signal_data["direction"] == "bullish" and signal_data["strength"] > 0.7:
            action = "buy"
        elif signal_data["direction"] == "bearish" and signal_data["strength"] < 0.3:
            action = "sell"
        else:
            action = "hold"
        
        trading_signal = {
            "action": action,
            "timestamp": time.time(),
            "price": current_price,
            "signal_data": signal_data
        }
        
        self.signal_history.append(trading_signal)
        return trading_signal
    
    def train_ai_model(self):
        """训练AI模型"""
        return self.vst_ai.train_model()
    
    def get_performance_summary(self) -> Dict:
        """获取性能摘要"""
        if len(self.signal_history) == 0:
            return {"total_signals": 0}
        
        buy_signals = sum(1 for s in self.signal_history if s["action"] == "buy")
        sell_signals = sum(1 for s in self.signal_history if s["action"] == "sell")
        hold_signals = sum(1 for s in self.signal_history if s["action"] == "hold")
        
        avg_confidence = np.mean([s["signal_data"]["confidence"] for s in self.signal_history])
        
        return {
            "total_signals": len(self.signal_history),
            "buy_signals": buy_signals,
            "sell_signals": sell_signals,
            "hold_signals": hold_signals,
            "avg_confidence": avg_confidence,
            "ai_trained": self.vst_ai.is_trained
        }


if __name__ == "__main__":
    print("Volume SuperTrend AI - 独立实现版本")
    print("=" * 50)
    
    # 创建信号生成器
    signal_generator = TradingSignalGenerator()
    
    # 模拟市场数据
    print("模拟市场数据测试...")
    np.random.seed(42)
    
    base_price = 100.0
    for i in range(100):
        # 模拟价格和成交量
        price = base_price + 0.01 * i + np.random.normal(0, 0.5)
        volume = 1000 + np.random.normal(0, 200)
        
        # 更新数据
        signal_generator.update_market_data(price, volume)
        
        # 每10个数据点生成一次信号
        if i % 10 == 0 and i > 20:
            signal = signal_generator.generate_trading_signal(price)
            print(f"第{i}个数据点 - 价格: {price:.2f}, 信号: {signal['action']}, "
                  f"置信度: {signal['signal_data']['confidence']:.3f}")
    
    # 训练AI模型
    print("\n训练AI模型...")
    if signal_generator.train_ai_model():
        print("✓ AI模型训练成功")
    else:
        print("⚠ AI模型训练失败，使用简单规则")
    
    # 显示性能摘要
    print("\n性能摘要:")
    summary = signal_generator.get_performance_summary()
    for key, value in summary.items():
        print(f"  {key}: {value}")
    
    print("\n✓ Volume SuperTrend AI 独立实现测试完成")
