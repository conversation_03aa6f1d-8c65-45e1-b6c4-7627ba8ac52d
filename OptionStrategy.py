from typing import Literal, Dict, List, Any, Union
import numpy as np
import time
from collections import deque
from dataclasses import dataclass
import pandas as pd
import pandas_ta as ta
import numexpr as ne
import onnxruntime as ort
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestClassifier
import lightgbm as lgb
from pydantic import ConfigDict
import logging
import rx
from rx import operators as ops
from rx.scheduler import ThreadPoolScheduler

# 启用numexpr加速pandas计算
pd.set_option('compute.use_numexpr', True)

# 初始化ONNX运行时
ort_session = None

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator

@dataclass
class IndicatorCache:
    """缓存常用指标计算结果"""
    ema_fast: float = 0
    ema_mid: float = 0
    ema_slow: float = 0
    rsi: float = 0
    atr: float = 0
    volume_ma: float = 0
    last_update: float = 0

@dataclass
class StrategyParams:
    """策略参数配置"""
    ema: tuple = (5, 10, 20)  # (fast, mid, slow) EMA periods
    stop_mult: float = 1.2    # 止损倍数
    profit_mult: float = 1.5  # 止盈倍数
    trail_step: float = 0.3   # 追踪步长
    trend_weights: dict = None  # 趋势权重配置

    def __post_init__(self):
        if self.trend_weights is None:
            self.trend_weights = {'short': 0.4, 'mid': 0.4, 'long': 0.2}

class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="SHFE", title="交易所代码")  # 设置默认交易所代码
    instrument_id: str = Field(default="cu2405", title="合约代码")  # 设置默认合约代码
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # EMA参数设置
    ema_fast_period: int = Field(default=5, title="快速EMA周期", ge=3, le=10)
    ema_mid_period: int = Field(default=10, title="中速EMA周期", ge=8, le=20)
    ema_slow_period: int = Field(default=20, title="慢速EMA周期", ge=15, le=30)
    
    # RSI参数设置
    rsi_period: int = Field(default=14, title="RSI周期", ge=7, le=21)
    rsi_upper: int = Field(default=65, title="RSI上限", ge=60, le=80)  # 降低RSI上限阈值
    rsi_lower: int = Field(default=30, title="RSI下限", ge=20, le=40)
    
    # 成交量参数设置
    volume_ma_period: int = Field(default=20, title="成交量MA周期", ge=10, le=30)
    volume_breakout_mult: float = Field(default=1.5, title="成交量突破倍数", ge=1.2, le=2.0)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    atr_short_period: int = Field(default=5, title="短期ATR周期", ge=3, le=10)  # 新增短期ATR参数
    stop_mult: float = Field(default=1.2, title="止损倍数", ge=0.8, le=2.0)
    profit_mult: float = Field(default=1.5, title="止盈倍数", ge=1.2, le=2.5)
    trail_step: float = Field(default=0.3, title="追踪步长", ge=0.2, le=1.0)
    vol_threshold: float = Field(default=1.5, title="波动率阈值", ge=1.2, le=2.0)  # 新增波动率阈值参数
    
    # 动态止盈参数
    profit_take_ratio: float = Field(default=0.5, title="止盈回撤比例", ge=0.3, le=0.7)  # 新增：盈利回撤止盈比例
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 趋势判断参数
    trend_period_min: int = Field(default=8, title="最小趋势周期", ge=5, le=15)
    trend_period_max: int = Field(default=20, title="最大趋势周期", ge=15, le=30)
    trend_weight_recent: float = Field(default=0.6, title="近期价格权重", ge=0.5, le=0.8)
    volume_factor_weight: float = Field(default=0.3, title="成交量因子权重", ge=0.2, le=0.5)
    
    # RSI平滑参数
    rsi_smooth_period: int = Field(default=3, title="RSI平滑周期", ge=2, le=5)
    rsi_trend_threshold: float = Field(default=0.3, title="RSI趋势阈值", ge=0.2, le=0.5)
    
    # 波动率过滤参数
    vol_filter_period: int = Field(default=5, title="波动率过滤周期", ge=3, le=10)
    vol_filter_threshold: float = Field(default=1.8, title="波动率过滤阈值", ge=1.5, le=2.5)
    
    # 多周期趋势参数
    trend_short_period: int = Field(default=3, title="短周期", ge=3, le=5)
    trend_mid_period: int = Field(default=5, title="中周期", ge=5, le=8)
    trend_long_period: int = Field(default=10, title="长周期", ge=8, le=15)
    trend_strength_threshold: float = Field(default=0.4, title="趋势强度阈值", ge=0.3, le=0.6)
    trend_duration_min: int = Field(default=2, title="最小趋势持续周期", ge=2, le=4)
    
    # 随机动量指标参数
    stoch_k_period: int = Field(default=9, title="随机指标K周期", ge=5, le=14)
    stoch_d_period: int = Field(default=3, title="随机指标D周期", ge=2, le=5)
    stoch_upper: int = Field(default=80, title="随机指标上限", ge=75, le=85)
    stoch_lower: int = Field(default=20, title="随机指标下限", ge=15, le=25)
    
    # 订单执行参数
    order_timeout: int = Field(default=10, title="订单超时时间(秒)", ge=5, le=30)
    
    # 趋势计算高级参数
    std_filter_period: int = Field(default=20, title="标准差周期", ge=10, le=30)
    std_filter_mult: float = Field(default=1.5, title="标准差倍数", ge=1.0, le=2.0)
    momentum_smooth_period: int = Field(default=5, title="动量平滑周期", ge=3, le=8)
    acceleration_period: int = Field(default=3, title="加速度计算周期", ge=2, le=5)
    
    # 斐波那契参数
    fib_period: int = Field(default=20, title="斐波那契周期", ge=10, le=30)
    fib_deviation: float = Field(default=0.02, title="斐波那契偏差", ge=0.01, le=0.05)
    fib_profit_ratio: float = Field(default=0.618, title="斐波那契止盈比率", ge=0.5, le=0.786)
    
    # 新增性能优化参数
    cache_ttl: int = Field(default=5, title="指标缓存时间(秒)", ge=1, le=10)
    max_history_size: int = Field(default=1000, title="历史数据最大长度", ge=500, le=2000)
    
    # 新增风险管理参数
    max_drawdown: float = Field(default=0.1, title="最大回撤限制", ge=0.05, le=0.2)
    position_size_mult: float = Field(default=0.02, title="仓位乘数", ge=0.01, le=0.05)
    min_volatility: float = Field(default=0.01, title="最小波动率", ge=0.005, le=0.02)
    
    # 新增信号质量参数
    min_trend_strength: float = Field(default=0.6, title="最小趋势强度", ge=0.5, le=0.8)
    signal_confirmation_periods: int = Field(default=3, title="信号确认周期数", ge=2, le=5)
    min_volume_ratio: float = Field(default=1.2, title="最小量比", ge=1.0, le=1.5)
    
    # 新增机器学习参数
    ml_enabled: bool = Field(default=True, title="启用机器学习")
    ml_model_type: Literal["rf", "lgb"] = Field(default="lgb", title="机器学习模型类型")
    ml_train_period: int = Field(default=1000, title="训练数据周期")
    ml_predict_period: int = Field(default=5, title="预测周期")
    ml_feature_window: int = Field(default=20, title="特征窗口大小")
    
    # 新增小波变换参数
    wavelet_type: str = Field(default="db4", title="小波类型")
    wavelet_level: int = Field(default=3, title="小波分解层数")
    
    # 新增响应式参数
    rx_buffer_size: int = Field(default=100, title="响应式缓冲区大小")
    rx_sample_rate: int = Field(default=1, title="响应式采样率")
    
    # 新增技术指标参数
    ta_rsi_period: int = Field(default=14, title="RSI周期")
    ta_macd_fast: int = Field(default=12, title="MACD快线周期")
    ta_macd_slow: int = Field(default=26, title="MACD慢线周期")
    ta_macd_signal: int = Field(default=9, title="MACD信号线周期")
    
    # 新增性能优化参数
    batch_size: int = Field(default=100, title="批量处理大小")
    num_threads: int = Field(default=4, title="线程数")
    use_onnx: bool = Field(default=True, title="使用ONNX加速")
    
    # 新增机器学习参数
    feature_importance_threshold: float = Field(default=0.1, title="特征重要性阈值")
    prediction_confidence_threshold: float = Field(default=0.7, title="预测置信度阈值")
    validation_window: int = Field(default=10, title="验证窗口大小")


class State(BaseState):
    """状态映射模型"""
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # RSI指标
    rsi: float = Field(default=0, title="RSI值")
    rsi_trend: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="RSI趋势")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量MA")
    volume_ratio: float = Field(default=0, title="量比")
    is_volume_breakout: bool = Field(default=False, title="成交量突破")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    atr_short: float = Field(default=0, title="短期ATR")
    
    # 趋势状态
    trend_type: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    volatility_ratio: float = Field(default=1.0, title="波动率比值")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 趋势相关新增状态
    trend_period_current: int = Field(default=0, title="当前趋势周期")
    price_momentum: float = Field(default=0, title="价格动量")
    volume_factor: float = Field(default=0, title="成交量因子")
    
    # RSI相关新增状态
    rsi_smooth: float = Field(default=0, title="平滑RSI")
    rsi_momentum: float = Field(default=0, title="RSI动量")
    
    # 波动率相关新增状态
    volatility_state: Literal["低波动", "中等波动", "高波动"] = Field(default="中等波动", title="波动率状态")
    signal_quality: float = Field(default=0, title="信号质量", ge=0, le=1)
    
    # 多周期趋势状态
    trend_short: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="短周期趋势")
    trend_mid: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="中周期趋势")
    trend_long: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="长周期趋势")
    trend_consensus: bool = Field(default=False, title="趋势共识")
    trend_reversal_confirmed: bool = Field(default=False, title="趋势反转确认")
    
    # 随机动量指标状态
    stoch_k: float = Field(default=50, title="随机指标K值")
    stoch_d: float = Field(default=50, title="随机指标D值")
    stoch_momentum: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="随机指标动量")
    
    # 订单执行状态
    order_time: float = Field(default=0, title="订单时间戳")
    
    # 趋势计算高级状态
    price_std: float = Field(default=0, title="价格标准差")
    price_acceleration: float = Field(default=0, title="价格加速度")
    momentum_smoothed: float = Field(default=0, title="平滑动量")
    
    # 斐波那契状态
    fib_levels: dict = Field(default_factory=dict, title="斐波那契水平")
    fib_support: float = Field(default=0, title="当前支撑位")
    fib_resistance: float = Field(default=0, title="当前阻力位")
    fib_trend_quality: float = Field(default=0, title="趋势质量", ge=0, le=1)
    
    # 性能监控指标
    win_rate: float = Field(default=0, title="胜率", ge=0, le=1)
    profit_factor: float = Field(default=0, title="盈亏比", ge=0)
    sharpe_ratio: float = Field(default=0, title="夏普比率")
    max_drawdown: float = Field(default=0, title="最大回撤", ge=0, le=1)
    trade_count: int = Field(default=0, title="交易次数", ge=0)
    avg_trade_duration: float = Field(default=0, title="平均持仓时间(秒)", ge=0)
    signal_accuracy: float = Field(default=0, title="信号准确率", ge=0, le=1)
    
    # 新增性能监控状态
    indicator_cache: IndicatorCache = Field(default_factory=IndicatorCache, title="指标缓存")
    calculation_time: float = Field(default=0, title="计算耗时(ms)")
    cache_hit_rate: float = Field(default=0, title="缓存命中率", ge=0, le=1)
    
    # 新增风险管理状态
    current_drawdown: float = Field(default=0, title="当前回撤", ge=0, le=1)
    position_size: float = Field(default=0, title="当前仓位大小")
    risk_adjusted_position: float = Field(default=0, title="风险调整后仓位")
    
    # 新增信号质量状态
    signal_quality_score: float = Field(default=0, title="信号质量评分", ge=0, le=1)
    trend_confirmation_count: int = Field(default=0, title="趋势确认计数")
    volume_confirmation: bool = Field(default=False, title="成交量确认")
    
    # 新增机器学习状态
    ml_model: Any = Field(default=None, title="机器学习模型")
    ml_features: np.ndarray = Field(default=None, title="特征矩阵")
    ml_predictions: np.ndarray = Field(default=None, title="预测结果")
    ml_accuracy: float = Field(default=0.0, title="模型准确率")
    
    # 新增小波变换状态
    wavelet_coeffs: dict = Field(default_factory=dict, title="小波系数")
    wavelet_energy: float = Field(default=0.0, title="小波能量")
    
    # 新增响应式状态
    rx_stream: rx.Observable = Field(default=None, title="响应式数据流")
    rx_buffer: deque = Field(default_factory=deque, title="响应式缓冲区")
    
    # 新增技术指标状态
    ta_rsi: float = Field(default=0.0, title="RSI值")
    ta_macd: dict = Field(default_factory=dict, title="MACD指标")
    ta_bbands: dict = Field(default_factory=dict, title="布林带")
    
    # 新增机器学习状态
    feature_importance: Dict[str, float] = Field(default_factory=dict, title="特征重要性")
    prediction_validation: Dict[str, float] = Field(default_factory=dict, title="预测验证结果")
    model_performance: Dict[str, float] = Field(default_factory=dict, title="模型性能指标")


class OptionStrategy(BaseStrategy):
    """机器学习策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 初始化 K 线生成器
        self.kline_generator = KLineGenerator(
            exchange=self.params_map.exchange,
            instrument_id=self.params_map.instrument_id,
            callback=self.callback
        )
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 行情判断相关
        self.trend_period = 10  # 趋势判断周期
        self.trend_count = 0
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        
        # 趋势判断参数
        self.trend_threshold = 0.6  # 趋势判断阈值
        self.volatility_threshold = 0.4  # 波动率阈值
        self.min_trend_duration = 3  # 最小趋势持续周期
        self.max_trend_duration = 20  # 最大趋势持续周期
        
        # 策略模式参数配置
        self.param_sets = {
            "上升": StrategyParams(
                ema=(self.params_map.ema_fast_period, self.params_map.ema_mid_period, self.params_map.ema_slow_period),
                stop_mult=1.5,
                profit_mult=2.0,
                trail_step=0.5,
                trend_weights={'short': 0.6, 'mid': 0.3, 'long': 0.1}
            ),
            "下降": StrategyParams(
                ema=(self.params_map.ema_fast_period, self.params_map.ema_mid_period, self.params_map.ema_slow_period),
                stop_mult=1.2,
                profit_mult=1.5,
                trail_step=0.3,
                trend_weights={'short': 0.3, 'mid': 0.5, 'long': 0.2}
            ),
            "震荡": StrategyParams(
                ema=(self.params_map.ema_fast_period, self.params_map.ema_mid_period, self.params_map.ema_slow_period),
                stop_mult=1.0,
                profit_mult=1.5,
                trail_step=0.3,
                trend_weights={'short': 0.4, 'mid': 0.4, 'long': 0.2}
            )
        }
        
        self.current_strategy: StrategyParams = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0
        
        # 动态止盈止损相关
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        
        # 订单管理相关
        self.order_time = 0
        
        # 趋势检测历史数据
        self.trend_history = {
            "short": [],
            "mid": [],
            "long": []
        }
        self.trend_reversal_count = 0

        # 使用deque优化历史数据存储
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        
        # 多周期数据存储
        self.multi_timeframe_data = {
            "M1": deque(maxlen=100),
            "M5": deque(maxlen=100),
            "M15": deque(maxlen=100)
        }
        
        # 信号确认队列
        self.signal_confirmation_queue = deque(maxlen=self.params_map.signal_confirmation_periods)
        
        # 性能监控
        self.calculation_start_time = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 初始化日志
        self.logger = logging.getLogger(self.__class__.__name__)

    def update_status_bar(self, status: dict = None):
        """更新状态栏显示"""
        try:
            if status is None:
                status = {
                    'trend_type': self.state_map.trend_type,
                    'trend_strength': self.state_map.trend_strength,
                    'volatility': self.state_map.volatility,
                    'position_size': self.position_size,
                    'current_profit': self.state_map.current_profit,
                    'win_rate': self.state_map.win_rate,
                    'signal_accuracy': self.state_map.signal_accuracy
                }
            
            # 更新趋势状态
            self.update_status('trend_type', status['trend_type'])
            self.update_status('trend_strength', f"{status['trend_strength']:.2f}")
            
            # 更新波动率
            self.update_status('volatility', f"{status['volatility']:.2f}")
            
            # 更新持仓信息
            self.update_status('position_size', str(status['position_size']))
            self.update_status('current_profit', f"{status['current_profit']:.2f}")
            
            # 更新性能指标
            self.update_status('win_rate', f"{status['win_rate']:.2%}")
            self.update_status('signal_accuracy', f"{status['signal_accuracy']:.2%}")
            
        except Exception as e:
            self.logger.error(f"更新状态栏时出错: {str(e)}")
            
        # 调用父类的update_status_bar
        super().update_status_bar()

    def update_indicator(self, name: str, value: float):
        """更新指标显示"""
        try:
            # 调用父类的更新指标方法
            super().update_indicator(name, value)
        except Exception as e:
            self.logger.error(f"更新指标 {name} 时出错: {str(e)}")

    def update_ui(self):
        """更新UI显示"""
        try:
            # 更新主图指标
            main_indicators = self.main_indicator_data
            self.update_main_chart(main_indicators)
            
            # 更新副图指标
            sub_indicators = self.sub_indicator_data
            self.update_sub_chart(sub_indicators)
            
            # 更新状态栏
            self.update_status_bar()
            
            # 强制刷新UI
            self.refresh_ui()
            
        except Exception as e:
            self.logger.error(f"更新UI时出错: {str(e)}")

    def update_main_chart(self, indicators: dict):
        """更新主图指标显示"""
        try:
            # 更新EMA指标
            self.update_indicator('EMA_FAST', indicators['EMA_FAST'])
            self.update_indicator('EMA_MID', indicators['EMA_MID'])
            self.update_indicator('EMA_SLOW', indicators['EMA_SLOW'])
            
            # 更新ATR指标
            self.update_indicator('ATR', indicators['ATR'])
            
        except Exception as e:
            self.logger.error(f"更新主图指标时出错: {str(e)}")

    def update_sub_chart(self, indicators: dict):
        """更新副图指标显示"""
        try:
            # 更新RSI指标
            self.update_indicator('RSI', indicators['RSI'])
            
            # 更新成交量比率
            self.update_indicator('VOLUME_RATIO', indicators['VOLUME_RATIO'])
            
            # 更新止损止盈线
            self.update_indicator('STOP_LOSS', indicators['STOP_LOSS'])
            self.update_indicator('TAKE_PROFIT', indicators['TAKE_PROFIT'])
            self.update_indicator('TRAILING_STOP', indicators['TRAILING_STOP'])
            
        except Exception as e:
            self.logger.error(f"更新副图指标时出错: {str(e)}")

    def on_tick(self, tick: TickData):
        """处理行情数据"""
        try:
            super().on_tick(tick)
            
            # 更新响应式缓冲区
            self.state_map.rx_buffer.append({
                'datetime': tick.datetime,
                'price': tick.last_price,
                'volume': tick.volume
            })
            
            # 保持缓冲区大小
            if len(self.state_map.rx_buffer) > self.params_map.rx_buffer_size:
                self.state_map.rx_buffer.popleft()
                
            # 更新历史数据
            self.price_history.append(tick.last_price)
            self.volume_history.append(tick.volume)
            
            # 计算指标
            self.calc_indicator()
            
            # 计算趋势
            self.calc_trend(tick)
            
            # 计算信号
            self.calc_signal(tick)
            
            # 执行信号
            self.exec_signal()
            
            # 更新UI
            self.update_ui()
            
        except Exception as e:
            self.logger.error(f"处理行情数据时出错: {str(e)}")

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """处理成交回报"""
        super().on_trade(trade, log)
        
        # 更新交易统计
        if trade.direction == "多":
            self.state_map.trade_count += 1
            self.state_map.current_profit = (trade.price - self.entry_price) * trade.volume
            self.state_map.max_profit = max(self.state_map.max_profit, self.state_map.current_profit)
            
            # 更新回撤
            if self.state_map.max_profit > 0:
                self.state_map.current_drawdown = (self.state_map.max_profit - self.state_map.current_profit) / self.state_map.max_profit
                
        elif trade.direction == "空":
            # 计算交易结果
            profit = (self.entry_price - trade.price) * trade.volume
            self.state_map.current_profit = 0
            self.state_map.max_profit = 0
            
            # 更新性能指标
            if profit > 0:
                self.state_map.win_rate = (self.state_map.win_rate * (self.state_map.trade_count - 1) + 1) / self.state_map.trade_count
            else:
                self.state_map.win_rate = (self.state_map.win_rate * (self.state_map.trade_count - 1)) / self.state_map.trade_count
                
            # 更新夏普比率
            self._update_sharpe_ratio(profit)
            
            # 更新信号准确率
            self._update_signal_accuracy(profit > 0)

    def on_start(self):
        """策略启动"""
        super().on_start()

        # 初始化性能监控
        self.calculation_start_time = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 初始化历史数据
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        
        # 初始化多周期数据
        self.multi_timeframe_data = {
            "M1": deque(maxlen=100),
            "M5": deque(maxlen=100),
            "M15": deque(maxlen=100)
        }
        
        # 初始化信号确认队列
        self.signal_confirmation_queue = deque(maxlen=self.params_map.signal_confirmation_periods)
        
        # 重置状态
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.order_id = None
        self.position_size = 0
        self.entry_price = 0
        self.is_trailing = False
        
        # 初始化机器学习模型
        if self.params_map.ml_enabled:
            self._init_ml_model()
            
        # 初始化响应式数据流
        self._init_rx_stream()

    def on_stop(self):
        """策略停止"""
        super().on_stop()
        
        # 清理资源
        self.price_history.clear()
        self.volume_history.clear()
        self.rsi_history.clear()
        
        for timeframe in self.multi_timeframe_data.values():
            timeframe.clear()
            
        self.signal_confirmation_queue.clear()

    def callback(self, kline: KLineData) -> None:
        """K线回调"""
        # 更新历史数据
        self.price_history.append(kline.close)
        self.volume_history.append(kline.volume)
        
        # 计算指标
        self.calc_indicator()
        
        # 计算趋势
        self.calc_trend(kline)
        
        # 计算信号
        self.calc_signal(kline)
        
        # 执行信号
        self.exec_signal()

    def real_time_callback(self, kline: KLineData) -> None:
        """实时K线回调"""
        # 更新多周期数据
        self.multi_timeframe_data[kline.period].append(kline)
        
        # 计算多周期趋势
        self.calc_multi_period_trend(kline)
        
        # 更新动态止盈止损
        if self.position_size > 0:
            self.update_dynamic_stops(kline.close)

    def calc_trend(self, data: Union[KLineData, TickData]) -> None:
        """计算趋势状态"""
        # 更新价格历史
        if isinstance(data, KLineData):
            self.price_history.append(data.close)
            self.volume_history.append(data.volume)
        else:  # TickData
            self.price_history.append(data.last_price)
            self.volume_history.append(data.volume)
        
        # 1. 动态调整趋势判断周期
        volatility = self.state_map.volatility if self.state_map.volatility > 0 else 1.0
        vol_ratio = self.state_map.volatility_ratio if self.state_map.volatility_ratio > 0 else 1.0
        
        # 在高波动时使用较短周期，低波动时使用较长周期
        self.trend_period = max(
            self.params_map.trend_period_min,
            min(
                self.params_map.trend_period_max,
                int(self.params_map.trend_period_max / vol_ratio)
            )
        )
        self.state_map.trend_period_current = self.trend_period
        
        # 保持历史数据长度
        while len(self.price_history) > self.trend_period:
            self.price_history.popleft()
            self.volume_history.popleft()
        
        if len(self.price_history) == self.trend_period:
            # 2. 改进方向一致性计算（加入权重机制）
            price_changes = np.diff(list(self.price_history))
            weights = np.exp(np.linspace(-1, 0, len(price_changes))) * self.params_map.trend_weight_recent
            weights = weights / np.sum(weights)  # 归一化权重
            
            weighted_changes = price_changes * weights
            direction_consistency = abs(np.sum(np.sign(weighted_changes))) / len(weighted_changes)
            
            # 3. 计算成交量因子
            volume_list = list(self.volume_history)
            volume_ma = np.mean(volume_list)
            volume_std = np.std(volume_list)
            recent_volume = np.mean(volume_list[-3:])  # 最近3根K线的平均成交量
            
            volume_factor = (recent_volume - volume_ma) / (volume_std if volume_std > 0 else 1)
            volume_factor = max(-1, min(1, volume_factor))  # 归一化到[-1, 1]范围
            self.state_map.volume_factor = volume_factor
            
            # 4. 计算趋势强度（结合成交量因子）
            ema_slopes = []
            for period in [self.params_map.ema_fast_period, self.params_map.ema_mid_period]:
                ema = self.kline_generator.producer.ema(period, array=True)
                if len(ema) >= 2:
                    slope = (ema[-1] - ema[-2]) / ema[-2]
                    ema_slopes.append(slope)
            
            if ema_slopes:
                base_trend_strength = abs(np.mean(ema_slopes))
                volume_component = abs(volume_factor) * self.params_map.volume_factor_weight
                
                # 综合趋势强度
                trend_strength = base_trend_strength * (1 + volume_component)
                self.state_map.trend_strength = min(1.0, trend_strength)
                
                # 计算价格动量
                price_momentum = np.sum(weighted_changes)
                self.state_map.price_momentum = price_momentum
            
            # 5. 判断趋势方向
            if direction_consistency > self.trend_threshold:
                if np.mean(weighted_changes) > 0:
                    new_trend = "上升"
                else:
                    new_trend = "下降"
            else:
                new_trend = "震荡"
            
            # 6. 更新趋势状态
            if new_trend != self.state_map.trend_type:
                self.state_map.trend_type = new_trend
                if new_trend != "震荡":
                    self.state_map.is_trending = True
                    self.state_map.trend_duration = 1
                else:
                    self.state_map.is_trending = False
                    self.state_map.trend_duration = 0
                
                # 更新参数
                self.current_params = self.param_sets[self.state_map.trend_type]
            elif self.state_map.is_trending:
                self.state_map.trend_duration += 1
                if self.state_map.trend_duration > self.max_trend_duration:
                    self.state_map.trend_duration = 0
                    self.state_map.is_trending = False
                    self.state_map.trend_type = "震荡"
                    self.current_params = self.param_sets["震荡"]
            
            # 7. 更新波动率状态
            vol_filter = self.kline_generator.producer.atr(self.params_map.vol_filter_period)[0]
            if vol_filter > self.params_map.vol_filter_threshold * self.state_map.atr:
                self.state_map.volatility_state = "高波动"
            elif vol_filter < self.state_map.atr:
                self.state_map.volatility_state = "低波动"
            else:
                self.state_map.volatility_state = "中等波动"
                
            # 8. 更新性能监控指标
            if self.state_map.trade_count > 0:
                self.state_map.win_rate = self.state_map.stop_profit_count / self.state_map.trade_count
                self.state_map.profit_factor = self.state_map.stop_profit_amount / max(1, self.state_map.stop_loss_amount)
                self.state_map.signal_accuracy = self.state_map.stop_profit_count / max(1, self.state_map.signal_confirm_count)

    def calc_indicator(self) -> None:
        """计算技术指标"""
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]  # 默认使用震荡参数
            
        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0], array=True)
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1], array=True)
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2], array=True)
        
        self.state_map.ema_fast = round(ema_fast[-1], 2)
        self.state_map.ema_mid = round(ema_mid[-1], 2)
        self.state_map.ema_slow = round(ema_slow[-1], 2)
        
        # 计算长短期ATR
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        atr_short, _ = self.kline_generator.producer.atr(self.params_map.atr_short_period)
        
        self.state_map.atr = round(atr_long, 2)
        self.state_map.atr_short = round(atr_short, 2)
        
        # 计算波动率比值
        self.state_map.volatility_ratio = round(atr_short / atr_long, 2) if atr_long > 0 else 1.0
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            # 根据模式选择止损止盈计算方式
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                # 使用固定止盈止损
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                # 根据波动率调整止损倍数
                vol_adjustment = min(self.state_map.volatility_ratio / self.params_map.vol_threshold, 1.0)
                adjusted_stop_mult = self.current_params["stop_mult"] * vol_adjustment
                
                # 使用ATR动态止盈止损
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * adjusted_stop_mult, 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * self.current_params["trail_step"], 2
            )

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损（优化版）"""
        if self.position_size <= 0:
            return
            
        # 更新最高价和最低价
        if current_price > self.state_map.highest_price:
            self.state_map.highest_price = current_price
        if current_price < self.state_map.lowest_price or self.state_map.lowest_price == 0:
            self.state_map.lowest_price = current_price
        
        # 计算当前盈亏
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        if self.state_map.current_profit > self.state_map.max_profit:
            self.state_map.max_profit = self.state_map.current_profit
            
        # 获取市场状态和调整因子
        market_state = self._get_market_state()
        state_adjustment = self._get_state_adjustment(market_state)
        
        # 基础ATR倍数
        base_atr_multiple = self.state_map.atr * self.current_params["stop_mult"]
        
        # 根据市场状态和风险调整止损倍数
        risk_adjustment = state_adjustment["risk"]
        atr_multiple = base_atr_multiple * risk_adjustment
        
        if self.state_map.current_profit > 0:
                # 根据波动率调整追踪止损触发条件
                trigger_threshold = self.state_map.atr * (0.8 if self.state_map.volatility_ratio > self.params_map.vol_threshold else 1.0)
                
                # 当盈利超过调整后的ATR倍数时，启动追踪止损
                if not self.is_trailing and self.state_map.current_profit > trigger_threshold:
                    self.is_trailing = True
                
                if self.is_trailing:
                    # 根据盈利水平动态调整追踪距离
                    profit_ratio = self.state_map.current_profit / (self.entry_price * self.position_size)
                
                # 更平滑的追踪距离调整
                if profit_ratio > 0.05:  # 大盈利
                    distance_mult = 0.75  # 适度追踪
                elif profit_ratio > 0.02:  # 中等盈利
                    distance_mult = 0.85
                else:  # 小盈利
                    distance_mult = 0.95
                    
                # 考虑趋势强度
                trend_factor = 1.0
                if self.state_map.trend_strength > 0.6:
                    trend_factor = 1.1  # 强趋势时放宽追踪
                elif self.state_map.trend_strength < 0.3:
                    trend_factor = 0.9  # 弱趋势时收紧追踪
                    
                    # 最终追踪距离
                trail_distance = base_atr_multiple * distance_mult * trend_factor * risk_adjustment
                    
                # 设置止损价格
                self.state_map.stop_loss = round(
                    max(
                        self.entry_price,  # 不低于入场价
                        self.state_map.highest_price - trail_distance
                    ),
                    2
                )
                
                # 动态调整止盈目标
                if profit_ratio > 0.05:  # 大盈利时
                    profit_target = self.state_map.highest_price - trail_distance * 0.5  # 适度止盈
                else:  # 正常止盈
                    profit_target = self.state_map.highest_price + self.state_map.atr * self.current_params["profit_mult"] * risk_adjustment
                    self.state_map.take_profit = round(profit_target, 2)
                
                # 未盈利时的保护性止损
                if self.state_map.trend_strength < 0.3:  # 趋势减弱时
                    protection_stop = self.entry_price - atr_multiple * 0.8  # 适度止损
                else:  # 正常止损
                    protection_stop = self.entry_price - atr_multiple
                
                # 确保止损不会太远
                max_loss_distance = atr_multiple * 1.5
                protection_stop = max(
                    protection_stop,
                    self.entry_price - max_loss_distance
                )
        self.state_map.stop_loss = round(protection_stop, 2)

    def calc_multi_period_trend(self, kline: KLineData) -> None:
        """计算多周期趋势（增强版）"""
        # 1. 计算价格标准差
        price_list = list(self.price_history)
        if len(price_list) >= self.params_map.std_filter_period:
            price_std = np.std(price_list[-self.params_map.std_filter_period:])
            self.state_map.price_std = price_std
            
            # 计算波动率比率（考虑标准差）
            recent_std = np.std(price_list[-5:])  # 最近5根K线的标准差
            std_ratio = recent_std / price_std if price_std > 0 else 1.0
            
            # 结合ATR和标准差的波动率
            vol_ratio = np.sqrt(
                0.7 * (self.state_map.volatility_ratio ** 2) +  # ATR权重
                0.3 * (std_ratio ** 2)  # 标准差权重
            )
            
            # 动态调整趋势周期
            self.trend_period = max(
                self.params_map.trend_period_min,
                min(
                    self.params_map.trend_period_max,
                    int(self.params_map.trend_period_max / (vol_ratio * self.params_map.std_filter_mult))
                )
            )
        
        # 2. 非线性权重分配
        if len(price_list) > self.trend_period:
            self.price_history.popleft()
            self.volume_history.popleft()
        
        if len(price_list) == self.trend_period:
            # 使用sigmoid函数进行非线性权重分配
            x = np.linspace(-2, 2, len(price_list))
            weights = 1 / (1 + np.exp(-x))  # sigmoid函数
            weights = weights / np.sum(weights)  # 归一化
            
            # 计算加权价格变化
            price_changes = np.diff(price_list)
            weighted_changes = price_changes * weights[1:]  # 去掉第一个权重（对应差分）
            
            # 3. 计算价格加速度
            if len(price_changes) >= self.params_map.acceleration_period:
                velocity = np.diff(price_changes[-self.params_map.acceleration_period:])
                acceleration = np.mean(velocity)
                self.state_map.price_acceleration = acceleration
                
                # 加速度因子（归一化到[-1, 1]范围）
                acc_factor = np.tanh(acceleration)  # 使用tanh进行非线性变换
            else:
                acc_factor = 0
            
            # 4. 计算平滑动量
            if len(weighted_changes) >= self.params_map.momentum_smooth_period:
                # 使用指数平滑
                alpha = 2 / (self.params_map.momentum_smooth_period + 1)
                smoothed_momentum = 0
                for i, change in enumerate(weighted_changes[-self.params_map.momentum_smooth_period:]):
                    smoothed_momentum = alpha * change + (1 - alpha) * smoothed_momentum
                self.state_map.momentum_smoothed = smoothed_momentum
            
            # 5. 增强型趋势强度计算
            if len(weighted_changes) > 0:
                # 基础趋势强度
                base_strength = abs(np.sum(weighted_changes)) / np.sum(abs(weighted_changes))
                
                # 加入加速度因子
                trend_strength = base_strength * (1 + 0.3 * abs(acc_factor))  # 加速度提升趋势强度
                
                # 考虑动量的一致性
                if np.sign(smoothed_momentum) == np.sign(acc_factor):
                    trend_strength *= 1.2  # 动量和加速度一致时增强趋势
                
                self.state_map.trend_strength = min(1.0, trend_strength)
            
            # 6. 计算斐波那契水平
            if len(price_list) >= self.params_map.fib_period:
                high = max(price_list[-self.params_map.fib_period:])
                low = min(price_list[-self.params_map.fib_period:])
                price_range = high - low
                
                # 计算主要斐波那契水平
                self.state_map.fib_levels = {
                    "0.236": low + 0.236 * price_range,
                    "0.382": low + 0.382 * price_range,
                    "0.5": low + 0.5 * price_range,
                    "0.618": low + 0.618 * price_range,
                    "0.786": low + 0.786 * price_range
                }
                
                # 确定当前价格位置
                current_price = price_list[-1]
                for level, price in self.state_map.fib_levels.items():
                    if abs(current_price - price) / price < self.params_map.fib_deviation:
                        if float(level) < 0.5:
                            self.state_map.fib_support = price
                        else:
                            self.state_map.fib_resistance = price
                
                # 计算趋势质量（基于斐波那契位置）
                if self.state_map.trend_type == "上升":
                    quality = min(1.0, (current_price - low) / price_range)
                else:
                    quality = min(1.0, (high - current_price) / price_range)
                self.state_map.fib_trend_quality = quality

    def calc_stoch_momentum(self) -> None:
        """计算随机动量指标"""
        high = self.kline_generator.producer.high
        low = self.kline_generator.producer.low
        close = self.kline_generator.producer.close
        
        if len(high) >= self.params_map.stoch_k_period:
            # 计算随机指标K值
            period_high = np.max(high[-self.params_map.stoch_k_period:])
            period_low = np.min(low[-self.params_map.stoch_k_period:])
            
            if period_high != period_low:
                k = 100 * (close[-1] - period_low) / (period_high - period_low)
                self.state_map.stoch_k = k
                
                # 计算D值（K值的移动平均）
                if len(high) >= self.params_map.stoch_k_period + self.params_map.stoch_d_period:
                    d = np.mean([self.state_map.stoch_k for _ in range(self.params_map.stoch_d_period)])
                    self.state_map.stoch_d = d
                    
                    # 判断动量方向
                    if k > d and k < self.params_map.stoch_upper:
                        self.state_map.stoch_momentum = "上升"
                    elif k < d and k > self.params_map.stoch_lower:
                        self.state_map.stoch_momentum = "下降"
                    else:
                        self.state_map.stoch_momentum = "震荡"
    
    def _update_indicator_cache(self) -> None:
        """更新指标缓存"""
        current_time = time.time()
        if current_time - self.state_map.indicator_cache.last_update > self.params_map.cache_ttl:
            self.state_map.indicator_cache = IndicatorCache(
                ema_fast=self.state_map.ema_fast,
                ema_mid=self.state_map.ema_mid,
                ema_slow=self.state_map.ema_slow,
                rsi=self.state_map.rsi,
                atr=self.state_map.atr,
                volume_ma=self.state_map.volume_ma,
                last_update=current_time
            )
            self.cache_misses += 1
        else:
            self.cache_hits += 1
            
        # 更新缓存命中率
        total = self.cache_hits + self.cache_misses
        if total > 0:
            self.state_map.cache_hit_rate = self.cache_hits / total

    def _calculate_position_size(self, current_price: float) -> float:
        """计算风险调整后的仓位大小"""
        # 基于波动率调整仓位
        volatility_factor = min(1.0, self.state_map.volatility / self.params_map.min_volatility)
        
        # 基于趋势强度调整仓位
        trend_factor = self.state_map.trend_strength
        
        # 基于信号质量调整仓位
        signal_factor = self.state_map.signal_quality_score
        
        # 计算基础仓位
        base_position = self.params_map.position_size_mult * self.params_map.order_volume
        
        # 应用调整因子
        adjusted_position = base_position * volatility_factor * trend_factor * signal_factor
        
        # 确保不超过最大持仓限制
        return min(adjusted_position, self.params_map.max_positions)

    def _validate_signal_quality(self, kline: KLineData) -> bool:
        """验证信号质量"""
        # 检查趋势强度
        if self.state_map.trend_strength < self.params_map.min_trend_strength:
            return False
            
        # 检查成交量确认
        if not self.state_map.volume_confirmation:
            return False
            
        # 检查多周期趋势一致性
        if not self.state_map.trend_consensus:
            return False
            
        # 检查波动率过滤
        if self.state_map.volatility < self.params_map.min_volatility:
            return False
            
        return True

    def calc_signal(self, data: Union[KLineData, TickData]):
        """基于状态机的信号计算逻辑"""
        # 响应式数据流处理
        self.data_stream.on_next({
            'price': data.close if isinstance(data, KLineData) else data.last_price,
            'volume': data.volume,
            'timestamp': data.datetime
        })

        # 布尔代数组合交易条件
        trend_cond = (self.state_map.ema_fast > self.state_map.ema_slow)
        momentum_cond = (self.state_map.stoch_k > self.state_map.stoch_d)
        volume_cond = (data.volume > self.state_map.volume_ma * 1.5)

        # 向量化信号计算
        signal_matrix = np.array([trend_cond, momentum_cond, volume_cond])
        weights = np.array([0.4, 0.3, 0.3])
        signal_strength = np.dot(signal_matrix, weights)

        # 状态转换逻辑
        self.state_machine.transition({
            'signal_strength': signal_strength,
            'volatility': self.state_map.volatility,
            'position': self.position_size
        })

        # 生成状态驱动信号
        self.buy_signal = self.state_machine.should_enter()
        self.sell_signal = self.state_machine.should_exit()

    def exec_signal(self):
        """响应式订单执行"""
        OrderExecutor.create_pipeline(
            self.data_stream,
            self.position_size,
            self.params_map.max_drawdown
        ).subscribe(
            on_next=lambda order: self._process_order(order),
            on_error=lambda e: self.logger.error(f"订单执行异常: {str(e)}")
        )

    def _process_order(self, order: dict):
        """处理订单状态转换"""
        order_conditions = {
            'entry': (order['signal'] & ~order['position']),
            'exit': (order['profit'] >= order['target']) | (order['drawdown'] > order['threshold'])
        }

        # 使用布尔矩阵执行订单
        order_matrix = pd.DataFrame.from_dict({
            'buy': [order_conditions['entry'], self.params_map.order_volume],
            'sell': [order_conditions['exit'], self.params_map.order_volume]
        }, orient='index', columns=['condition', 'volume'])

        # 生成最终订单指令
        valid_orders = order_matrix[order_matrix['condition']]
        if not valid_orders.empty:
            self.order_id = self._execute_batch_orders(valid_orders)

    def _update_sharpe_ratio(self, profit: float) -> None:
        """更新夏普比率"""
        # 计算收益率
        returns = profit / (self.entry_price * self.position_size) if self.entry_price > 0 else 0
        
        # 更新夏普比率
        if self.state_map.trade_count > 1:
            avg_return = returns / self.state_map.trade_count
            std_return = np.std([returns])
            self.state_map.sharpe_ratio = avg_return / std_return if std_return > 0 else 0

    def _update_signal_accuracy(self, is_profitable: bool) -> None:
        """更新信号准确率"""
        if self.state_map.trade_count > 0:
            self.state_map.signal_accuracy = (
                self.state_map.signal_accuracy * (self.state_map.trade_count - 1) + 
                float(is_profitable)
            ) / self.state_map.trade_count

    def calculate_indicators(self, data: pd.DataFrame) -> None:
        """计算技术指标（优化版）"""
        try:
            # 1. 基础指标
            self.state_map.atr = self._calculate_atr(data)
            self.state_map.rsi = self._calculate_rsi(data)
            self.state_map.macd = self._calculate_macd(data)
            
            # 2. 趋势指标
            self.state_map.ma20 = data['close'].rolling(window=20).mean().iloc[-1]
            self.state_map.ma50 = data['close'].rolling(window=50).mean().iloc[-1]
            self.state_map.ma200 = data['close'].rolling(window=200).mean().iloc[-1]
            
            # 3. 波动率指标
            self.state_map.volatility = data['close'].pct_change().std() * np.sqrt(252)
            self.state_map.volatility_ratio = self.state_map.volatility / self.state_map.volatility.rolling(window=20).mean().iloc[-1]
            
            # 4. 趋势强度
            self.state_map.trend_strength = self._calculate_trend_strength(data)
            
            # 5. 市场情绪
            self.state_map.market_sentiment = self._calculate_market_sentiment(data)
            
            # 6. 成交量分析
            self.state_map.volume_ma = data['volume'].rolling(window=20).mean().iloc[-1]
            self.state_map.volume_ratio = data['volume'].iloc[-1] / self.state_map.volume_ma
            
            # 7. 价格动量
            self.state_map.momentum = self._calculate_momentum(data)
            
            # 8. 支撑阻力
            self.state_map.support_level = self._calculate_support_level(data)
            self.state_map.resistance_level = self._calculate_resistance_level(data)
            
            # 9. 市场状态
            self.state_map.market_state = self._get_market_state()
            
            # 10. 风险指标
            self.state_map.risk_score = self._calculate_risk_score()
            
        except Exception as e:
            self.logger.error(f"计算指标时出错: {str(e)}")
            raise

    def _calculate_atr(self, data: pd.DataFrame) -> float:
        """计算平均真实波幅(ATR)
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 当前ATR值
            
        Note:
            ATR计算公式:
            1. TR = max(high-low, abs(high-close_prev), abs(low-close_prev))
            2. ATR = EMA(TR, period)
        """
        try:
            # 1. 计算真实波幅(TR)
            high = data['high']
            low = data['low']
            close = data['close']
            
            # 使用向量化操作计算TR
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            
            # 2. 计算ATR(使用EMA)
            atr = tr.ewm(span=self.params_map.atr_period, adjust=False).mean()
            
            # 3. 返回最新ATR值
            return round(atr.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算ATR时出错: {str(e)}")
            raise

    def _calculate_rsi(self, data: pd.DataFrame) -> float:
        """计算相对强弱指标(RSI)
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 当前RSI值
            
        Note:
            RSI计算公式:
            1. 计算价格变化: delta = close - close_prev
            2. 分离上涨和下跌: gain = max(delta, 0), loss = abs(min(delta, 0))
            3. 计算平均上涨和下跌: avg_gain = EMA(gain, period), avg_loss = EMA(loss, period)
            4. RS = avg_gain / avg_loss
            5. RSI = 100 - (100 / (1 + RS))
        """
        try:
            # 1. 计算价格变化
            delta = data['close'].diff()
            
            # 2. 分离上涨和下跌
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            
            # 3. 计算平均上涨和下跌(使用EMA)
            avg_gain = gain.ewm(span=self.params_map.rsi_period, adjust=False).mean()
            avg_loss = loss.ewm(span=self.params_map.rsi_period, adjust=False).mean()
            
            # 4. 计算RS
            rs = avg_gain / avg_loss
            
            # 5. 计算RSI
            rsi = 100 - (100 / (1 + rs))
            
            # 6. 返回最新RSI值
            return round(rsi.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算RSI时出错: {str(e)}")
            raise

    def _calculate_macd(self, data: pd.DataFrame) -> dict:
        """计算MACD指标
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            dict: 包含MACD、信号线和柱状图的值
            
        Note:
            MACD计算公式:
            1. EMA12 = EMA(close, 12)
            2. EMA26 = EMA(close, 26)
            3. MACD = EMA12 - EMA26
            4. Signal = EMA(MACD, 9)
            5. Histogram = MACD - Signal
        """
        try:
            # 1. 计算快线和慢线EMA
            ema_fast = data['close'].ewm(span=12, adjust=False).mean()
            ema_slow = data['close'].ewm(span=26, adjust=False).mean()
            
            # 2. 计算MACD线
            macd_line = ema_fast - ema_slow
            
            # 3. 计算信号线
            signal_line = macd_line.ewm(span=9, adjust=False).mean()
            
            # 4. 计算柱状图
            histogram = macd_line - signal_line
            
            # 5. 返回最新值
            return {
                'macd': round(macd_line.iloc[-1], 2),
                'signal': round(signal_line.iloc[-1], 2),
                'histogram': round(histogram.iloc[-1], 2)
            }
            
        except Exception as e:
            self.logger.error(f"计算MACD时出错: {str(e)}")
            raise

    def _calculate_trend_strength(self, data: pd.DataFrame) -> float:
        """计算趋势强度
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 趋势强度值(0-1之间)
            
        Note:
            趋势强度计算公式:
            1. 计算价格变化率
            2. 计算方向一致性
            3. 计算波动率
            4. 综合计算趋势强度
        """
        try:
            # 1. 计算价格变化率
            price_changes = data['close'].pct_change()
            
            # 2. 计算方向一致性
            direction_consistency = abs(price_changes.rolling(
                window=self.params_map.trend_period_min
            ).mean()) / price_changes.rolling(
                window=self.params_map.trend_period_min
            ).std()
            
            # 3. 计算波动率
            volatility = data['close'].pct_change().rolling(
                window=self.params_map.trend_period_min
            ).std()
            
            # 4. 计算趋势强度
            trend_strength = direction_consistency * (1 - volatility)
            
            # 5. 归一化到0-1范围
            trend_strength = trend_strength.clip(0, 1)
            
            # 6. 返回最新值
            return round(trend_strength.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算趋势强度时出错: {str(e)}")
            raise

    def _calculate_market_sentiment(self, data: pd.DataFrame) -> float:
        """计算市场情绪
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 市场情绪值(-1到1之间)
            
        Note:
            市场情绪计算公式:
            1. 计算价格动量
            2. 计算成交量变化
            3. 计算RSI位置
            4. 综合计算市场情绪
        """
        try:
            # 1. 计算价格动量
            price_momentum = data['close'].pct_change(periods=5)
            
            # 2. 计算成交量变化
            volume_change = data['volume'].pct_change(periods=5)
            
            # 3. 计算RSI位置
            rsi = self._calculate_rsi(data)
            rsi_position = (rsi - 50) / 50  # 归一化到-1到1
            
            # 4. 计算市场情绪
            sentiment = (
                price_momentum * 0.4 +  # 价格动量权重
                volume_change * 0.3 +   # 成交量权重
                rsi_position * 0.3      # RSI权重
            )
            
            # 5. 归一化到-1到1范围
            sentiment = sentiment.clip(-1, 1)
            
            # 6. 返回最新值
            return round(sentiment.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算市场情绪时出错: {str(e)}")
            raise

    def _calculate_momentum(self, data: pd.DataFrame) -> float:
        """计算价格动量
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 动量值(-1到1之间)
            
        Note:
            动量计算公式:
            1. 计算价格变化率
            2. 计算加速度
            3. 计算成交量加权
            4. 综合计算动量
        """
        try:
            # 1. 计算价格变化率
            price_changes = data['close'].pct_change()
            
            # 2. 计算加速度
            acceleration = price_changes.diff()
            
            # 3. 计算成交量加权
            volume_weight = data['volume'] / data['volume'].rolling(window=20).mean()
            
            # 4. 计算动量
            momentum = (
                price_changes * 0.5 +  # 价格变化权重
                acceleration * 0.3 +   # 加速度权重
                volume_weight * 0.2    # 成交量权重
            )
            
            # 5. 归一化到-1到1范围
            momentum = momentum.clip(-1, 1)
            
            # 6. 返回最新值
            return round(momentum.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算动量时出错: {str(e)}")
            raise

    def _calculate_support_level(self, data: pd.DataFrame) -> float:
        """计算支撑位
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 支撑位价格
            
        Note:
            支撑位计算公式:
            1. 计算最近低点
            2. 计算移动平均支撑
            3. 计算斐波那契支撑
            4. 综合计算支撑位
        """
        try:
            # 1. 计算最近低点
            recent_low = data['low'].rolling(window=20).min()
            
            # 2. 计算移动平均支撑
            ma_support = data['close'].rolling(window=20).mean() * 0.98
            
            # 3. 计算斐波那契支撑
            high = data['high'].rolling(window=20).max()
            low = data['low'].rolling(window=20).min()
            fib_support = low + (high - low) * 0.382
            
            # 4. 综合计算支撑位
            support_level = (
                recent_low * 0.4 +     # 最近低点权重
                ma_support * 0.3 +     # 移动平均权重
                fib_support * 0.3      # 斐波那契权重
            )
            
            # 5. 返回最新值
            return round(support_level.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算支撑位时出错: {str(e)}")
            raise

    def _calculate_resistance_level(self, data: pd.DataFrame) -> float:
        """计算阻力位
        
        Args:
            data: 包含OHLCV数据的DataFrame
            
        Returns:
            float: 阻力位价格
            
        Note:
            阻力位计算公式:
            1. 计算最近高点
            2. 计算移动平均阻力
            3. 计算斐波那契阻力
            4. 综合计算阻力位
        """
        try:
            # 1. 计算最近高点
            recent_high = data['high'].rolling(window=20).max()
            
            # 2. 计算移动平均阻力
            ma_resistance = data['close'].rolling(window=20).mean() * 1.02
            
            # 3. 计算斐波那契阻力
            high = data['high'].rolling(window=20).max()
            low = data['low'].rolling(window=20).min()
            fib_resistance = high - (high - low) * 0.618
            
            # 4. 综合计算阻力位
            resistance_level = (
                recent_high * 0.4 +     # 最近高点权重
                ma_resistance * 0.3 +   # 移动平均权重
                fib_resistance * 0.3    # 斐波那契权重
            )
            
            # 5. 返回最新值
            return round(resistance_level.iloc[-1], 2)
            
        except Exception as e:
            self.logger.error(f"计算阻力位时出错: {str(e)}")
            raise
    def _calculate_risk_score(self) -> float:
        """计算风险评分
        
        Returns:
            float: 风险评分值(0-1之间)
            
        Note:
            风险评分计算公式:
            1. 计算波动率
            2. 计算回撤
            3. 计算持仓时间
            4. 综合计算风险评分
        """
        try:
            # 1. 计算波动率
            volatility = self.state_map.volatility
            
            # 2. 计算回撤
            drawdown = self.state_map.current_drawdown
            
            # 3. 计算持仓时间
            duration = self.state_map.trend_duration
            
            # 4. 综合计算风险评分
            risk_score = (
                volatility * 0.4 +  # 波动率权重
                drawdown * 0.3 +    # 回撤权重
                duration * 0.3      # 持仓时间权重
            )
            
            # 5. 归一化到0-1范围
            risk_score = risk_score.clip(0, 1)
            
            # 6. 返回最新值
            return round(risk_score, 2)
            
        except Exception as e:
            self.logger.error(f"计算风险评分时出错: {str(e)}")
            raise

    def _init_ml_model(self) -> None:
        """初始化机器学习模型"""
        try:
            if self.params_map.ml_model_type == "rf":
                self.state_map.ml_model = RandomForestClassifier(
                    n_estimators=100,
                    max_depth=10,
                    random_state=42,
                    n_jobs=self.params_map.num_threads
                )
            else:
                self.state_map.ml_model = lgb.LGBMClassifier(
                    n_estimators=100,
                    max_depth=10,
                    learning_rate=0.1,
                    random_state=42,
                    n_jobs=self.params_map.num_threads
                )
            
            # 初始化ONNX运行时
            if self.params_map.use_onnx:
                self._init_onnx_runtime()
            
            # 初始化特征工程流水线
            self._init_feature_pipeline()
            
            # 加载历史数据进行训练
            self._load_training_data()
            
            # 初始化在线学习参数
            self.online_learning_buffer = deque(maxlen=self.params_map.ml_train_period)
            self.last_retrain_time = time.time()
            
        except Exception as e:
            self.logger.error(f"初始化机器学习模型时出错: {str(e)}")

    def _init_onnx_runtime(self):
        """初始化ONNX运行时"""
        try:
            global ort_session
            
            # 转换模型为ONNX格式
            if self.params_map.ml_model_type == "rf":
                onnx_model = self._convert_rf_to_onnx()
            else:
                onnx_model = self._convert_lgb_to_onnx()
            
            # 创建ONNX会话
            ort_session = ort.InferenceSession(onnx_model.SerializeToString())
            
            # 配置会话选项
            options = ort.SessionOptions()
            options.intra_op_num_threads = self.params_map.num_threads
            options.inter_op_num_threads = self.params_map.num_threads
            
        except Exception as e:
            self.logger.error(f"初始化ONNX运行时出错: {str(e)}")

    def _convert_rf_to_onnx(self):
        """转换随机森林模型为ONNX格式"""
        try:
            from skl2onnx import convert_sklearn
            from skl2onnx.common.data_types import FloatTensorType
            
            # 定义输入类型
            initial_type = [('float_input', FloatTensorType([None, self.state_map.ml_features.shape[1]]))]
            
            # 转换模型
            onnx_model = convert_sklearn(
                self.state_map.ml_model,
                initial_types=initial_type
            )
            
            return onnx_model
            
        except Exception as e:
            self.logger.error(f"转换随机森林模型为ONNX格式时出错: {str(e)}")
            return None

    def _convert_lgb_to_onnx(self):
        """转换LightGBM模型为ONNX格式"""
        try:
            from onnxmltools.convert import convert_lightgbm
            
            # 转换模型
            onnx_model = convert_lightgbm(
                self.state_map.ml_model,
                initial_types=[('float_input', FloatTensorType([None, self.state_map.ml_features.shape[1]]))]
            )
            
            return onnx_model
            
        except Exception as e:
            self.logger.error(f"转换LightGBM模型为ONNX格式时出错: {str(e)}")
            return None

    def _process_features(self, data: pd.DataFrame) -> np.ndarray:
        """处理特征数据（优化版）"""
        try:
            # 使用numexpr加速计算
            features = []
            
            # 1. 基础价格特征（使用numexpr）
            price_expr = """
                (close - close_prev) / close_prev,
                mean(close, window=5),
                mean(close, window=20)
            """
            price_features = ne.evaluate(price_expr, {
                'close': data['close'].values,
                'close_prev': data['close'].shift(1).values
            })
            features.extend(price_features)
            
            # 2. 技术指标特征
            for name, processor in self.feature_processors.items():
                if name == 'scaler':
                    continue
                indicator = processor(data)
                if isinstance(indicator, pd.DataFrame):
                    features.extend([indicator[col].values for col in indicator.columns])
                else:
                    features.append(indicator.values)
            
            # 3. 成交量特征（使用numexpr）
            volume_expr = """
                (volume - volume_prev) / volume_prev,
                mean(volume, window=5),
                mean(volume, window=20)
            """
            volume_features = ne.evaluate(volume_expr, {
                'volume': data['volume'].values,
                'volume_prev': data['volume'].shift(1).values
            })
            features.extend(volume_features)
            
            # 4. 特征标准化
            feature_matrix = np.column_stack(features)
            feature_matrix = self.feature_processors['scaler'].fit_transform(feature_matrix)
            
            # 5. 更新特征缓存
            self.feature_cache.append(feature_matrix[-1])
            
            return feature_matrix
            
        except Exception as e:
            self.logger.error(f"处理特征数据时出错: {str(e)}")
            return np.array([])

    def _init_rx_stream(self) -> None:
        """初始化响应式数据流（优化版）"""
        try:
            # 创建线程池调度器
            scheduler = ThreadPoolScheduler(self.params_map.num_threads)
            
            self.state_map.rx_stream = rx.from_iterable(
                self.state_map.rx_buffer
            ).pipe(
                # 批量处理
                ops.buffer_with_count(self.params_map.batch_size),
                # 并行处理
                ops.observe_on(scheduler),
                # 数据处理
                ops.map(lambda x: pd.DataFrame(x)),
                ops.map(lambda df: self._process_rx_data(df)),
                # 错误处理
                ops.catch(lambda e, source: self._handle_rx_error(e, source))
            )
            
            # 订阅数据流
            self.state_map.rx_stream.subscribe(
                on_next=lambda x: self._handle_rx_data(x),
                on_error=lambda e: self.logger.error(f"响应式流错误: {str(e)}"),
                on_completed=lambda: self.logger.info("响应式流完成")
            )
            
        except Exception as e:
            self.logger.error(f"初始化响应式数据流时出错: {str(e)}")

    def _handle_rx_error(self, error: Exception, source: rx.Observable) -> rx.Observable:
        """处理响应式流错误"""
        try:
            self.logger.error(f"响应式流处理错误: {str(error)}")
            return source
        except Exception as e:
            self.logger.error(f"处理响应式流错误时出错: {str(e)}")
            return rx.empty()

    def _validate_prediction(self, prediction: float, actual: float) -> bool:
        """验证预测结果"""
        try:
            # 计算预测误差
            error = abs(prediction - actual)
            
            # 更新验证结果
            self.state_map.prediction_validation = {
                'error': error,
                'confidence': 1 - error,
                'timestamp': time.time()
            }
            
            # 检查置信度
            return self.state_map.prediction_validation['confidence'] >= self.params_map.prediction_confidence_threshold
            
        except Exception as e:
            self.logger.error(f"验证预测结果时出错: {str(e)}")
            return False

    def _update_feature_importance(self):
        """更新特征重要性"""
        try:
            if hasattr(self.state_map.ml_model, 'feature_importances_'):
                # 获取特征重要性
                importance = self.state_map.ml_model.feature_importances_
                
                # 更新状态
                self.state_map.feature_importance = {
                    f'feature_{i}': imp for i, imp in enumerate(importance)
                    if imp >= self.params_map.feature_importance_threshold
                }
                
                # 更新UI
                self.update_indicator('FEATURE_IMPORTANCE', importance.mean())
                
        except Exception as e:
            self.logger.error(f"更新特征重要性时出错: {str(e)}")

    def _handle_rx_data(self, df: pd.DataFrame) -> None:
        """处理响应式数据结果（优化版）"""
        try:
            # 1. 更新技术指标状态
            self.state_map.ta_rsi = df['rsi'].iloc[-1]
            self.state_map.ta_macd = {
                'macd': df['MACD_12_26_9'].iloc[-1],
                'signal': df['MACDs_12_26_9'].iloc[-1],
                'hist': df['MACDh_12_26_9'].iloc[-1]
            }
            
            # 2. 更新机器学习预测
            if self.params_map.ml_enabled and self.state_map.ml_model is not None:
                # 处理特征
                features = self._process_features(df)
                
                if len(features) > 0:
                    # 更新特征状态
                    self.state_map.ml_features = features
                    
                    # 进行预测（使用ONNX加速）
                    if self.params_map.use_onnx and ort_session is not None:
                        predictions = ort_session.run(
                            None,
                            {'float_input': features.astype(np.float32)}
                        )[0]
                    else:
                        predictions = self.state_map.ml_model.predict(features)
                    
                    self.state_map.ml_predictions = predictions
                    
                    # 更新信号质量
                    if len(predictions) > 0:
                        self.state_map.signal_quality_score = float(predictions[-1])
                    
                    # 验证预测结果
                    if len(self.state_map.ml_predictions) >= self.params_map.validation_window:
                        self._validate_prediction(
                            self.state_map.ml_predictions[-1],
                            df['close'].iloc[-1] / df['close'].iloc[-2] - 1
                        )
                    
                    # 更新特征重要性
                    self._update_feature_importance()
                    
                    # 检查是否需要在线学习
                    self._check_online_learning(df)
                    
                    # 更新UI中的机器学习指标
                    self._update_ml_indicators()
            
            # 3. 触发UI更新
            self.update_ui()
            
        except Exception as e:
            self.logger.error(f"处理响应式数据时出错: {str(e)}")

    def _update_ml_indicators(self):
        """更新机器学习相关指标（增强版）"""
        try:
            if self.state_map.ml_predictions is not None:
                # 更新预测结果
                self.update_indicator('ML_PREDICTION', float(self.state_map.ml_predictions[-1]))
                
                # 更新信号质量
                self.update_indicator('ML_CONFIDENCE', self.state_map.signal_quality_score)
                
                # 更新模型准确率
                self.update_indicator('ML_ACCURACY', self.state_map.ml_accuracy)
                
                # 更新特征重要性
                if self.state_map.feature_importance:
                    self.update_indicator('FEATURE_IMPORTANCE', 
                        sum(self.state_map.feature_importance.values()) / len(self.state_map.feature_importance)
                    )
                
                # 更新预测验证结果
                if self.state_map.prediction_validation:
                    self.update_indicator('PREDICTION_VALIDATION', 
                        self.state_map.prediction_validation['confidence']
                    )
                
        except Exception as e:
            self.logger.error(f"更新机器学习指标时出错: {str(e)}")

    def update_ml_chart(self):
        """更新机器学习预测图表"""
        try:
            if self.state_map.ml_predictions is not None:
                # 更新预测结果
                self.update_indicator('ML_PREDICTION', float(self.state_map.ml_predictions[-1]))
                
                # 更新信号质量
                self.update_indicator('ML_CONFIDENCE', self.state_map.signal_quality_score)
                
                # 更新模型性能
                self.update_indicator('ML_ACCURACY', self.state_map.ml_accuracy)
                
                # 更新特征重要性
                if self.state_map.feature_importance:
                    self.update_indicator('FEATURE_IMPORTANCE', 
                        sum(self.state_map.feature_importance.values()) / len(self.state_map.feature_importance)
                    )
                
                # 更新特征分布
                if len(self.feature_cache) > 0:
                    features = np.array(list(self.feature_cache))
                    self.update_indicator('ML_FEATURE_DISTRIBUTION', features.mean(axis=0))
                
        except Exception as e:
            self.logger.error(f"更新机器学习图表时出错: {str(e)}")

    def _load_training_data(self) -> None:
        """加载训练数据"""
        try:
            # 获取历史K线数据
            history_data = self.kline_generator.producer.get_history(
                period=self.params_map.kline_style,
                count=self.params_map.ml_train_period
            )
            
            if history_data and len(history_data) >= self.params_map.ml_train_period:
                # 转换为DataFrame
                df = pd.DataFrame(history_data)
                
                # 提取特征
                features = self._extract_features(df)
                
                # 生成标签（使用未来N根K线的涨跌作为标签）
                labels = self._generate_labels(df)
                
                # 训练模型
                self.state_map.ml_model.fit(features, labels)
                self.logger.info("机器学习模型训练完成")
                
            else:
                self.logger.warning("历史数据不足，无法训练模型")
                
        except Exception as e:
            self.logger.error(f"加载训练数据时出错: {str(e)}")

    def _generate_labels(self, df: pd.DataFrame) -> np.ndarray:
        """生成训练标签"""
        try:
            # 计算未来N根K线的涨跌
            future_returns = df['close'].shift(-self.params_map.ml_predict_period) / df['close'] - 1
            
            # 将涨跌转换为二分类标签
            labels = (future_returns > 0).astype(int)
            
            # 移除最后N个无效标签
            labels = labels[:-self.params_map.ml_predict_period]
            
            return labels
            
        except Exception as e:
            self.logger.error(f"生成标签时出错: {str(e)}")
            return np.array([])

    def _process_rx_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理响应式数据"""
        # 1. 计算技术指标
        df['rsi'] = ta.rsi(df['close'], length=self.params_map.ta_rsi_period)
        macd = ta.macd(
            df['close'],
            fast=self.params_map.ta_macd_fast,
            slow=self.params_map.ta_macd_slow,
            signal=self.params_map.ta_macd_signal
        )
        df = pd.concat([df, macd], axis=1)
        
        # 2. 小波变换
        coeffs = pywt.wavedec(
            df['close'],
            self.params_map.wavelet_type,
            level=self.params_map.wavelet_level
        )
        self.state_map.wavelet_coeffs = {
            f'level_{i}': c for i, c in enumerate(coeffs)
        }
        self.state_map.wavelet_energy = np.sum([
            np.sum(np.square(c)) for c in coeffs
        ])
        
        return df

    def on_start(self):
        """策略启动"""
        super().on_start()
        
        # 初始化机器学习模型
        if self.params_map.ml_enabled:
            self._init_ml_model()
            
        # 初始化响应式数据流
        self._init_rx_stream()
        
        # 初始化性能监控
        self.calculation_start_time = 0
        self.cache_hits = 0
        self.cache_misses = 0
        
        # 初始化历史数据
        self.price_history = deque(maxlen=self.params_map.max_history_size)
        self.volume_history = deque(maxlen=self.params_map.max_history_size)
        self.rsi_history = deque(maxlen=self.params_map.max_history_size)
        
        # 初始化多周期数据
        self.multi_timeframe_data = {
            "M1": deque(maxlen=100),
            "M5": deque(maxlen=100),
            "M15": deque(maxlen=100)
        }
        
        # 初始化信号确认队列
        self.signal_confirmation_queue = deque(maxlen=self.params_map.signal_confirmation_periods)
        
        # 重置状态
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self.order_id = None
        self.position_size = 0
        self.entry_price = 0
        self.is_trailing = False

    def refresh_ui(self):
        """强制刷新UI"""
        try:
            # 调用父类的刷新方法
            super().refresh_ui()
            
            # 更新图表
            self.update_charts()
            
        except Exception as e:
            self.logger.error(f"刷新UI时出错: {str(e)}")

    def update_charts(self):
        """更新所有图表"""
        try:
            # 更新主图
            self.update_main_chart(self.main_indicator_data)
            
            # 更新副图
            self.update_sub_chart(self.sub_indicator_data)
            
            # 更新其他图表
            self.update_other_charts()
            
        except Exception as e:
            self.logger.error(f"更新图表时出错: {str(e)}")

    def update_other_charts(self):
        """更新其他图表"""
        try:
            # 更新机器学习预测图表
            if self.params_map.ml_enabled and self.state_map.ml_predictions is not None:
                self.update_ml_chart()
            
            # 更新趋势图表
            self.update_trend_chart()
            
            # 更新波动率图表
            self.update_volatility_chart()
            
        except Exception as e:
            self.logger.error(f"更新其他图表时出错: {str(e)}")

    def update_trend_chart(self):
        """更新趋势图表"""
        try:
            self.update_indicator('TREND_STRENGTH', self.state_map.trend_strength)
            self.update_indicator('TREND_DIRECTION', 1.0 if self.state_map.trend_type == "上升" else -1.0)
            
        except Exception as e:
            self.logger.error(f"更新趋势图表时出错: {str(e)}")

    def update_volatility_chart(self):
        """更新波动率图表"""
        try:
            self.update_indicator('VOLATILITY', self.state_map.volatility)
            self.update_indicator('VOLATILITY_RATIO', self.state_map.volatility_ratio)
            
        except Exception as e:
            self.logger.error(f"更新波动率图表时出错: {str(e)}")

