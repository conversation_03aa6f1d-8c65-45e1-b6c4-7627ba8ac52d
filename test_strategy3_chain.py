#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy3责任链架构测试脚本
验证新架构的功能和性能
"""

import sys
import time
import numpy as np
from datetime import datetime
from Strategy3 import Strategy3, create_strategy3, Tick<PERSON><PERSON>

def generate_test_data(num_points: int = 100) -> list:
    """生成测试数据"""
    np.random.seed(42)
    
    # 生成带趋势的价格数据
    base_price = 100.0
    trend = 0.001  # 轻微上升趋势
    noise_level = 0.02
    
    prices = []
    volumes = []
    
    for i in range(num_points):
        # 价格：趋势 + 随机波动
        price = base_price * (1 + trend * i + np.random.normal(0, noise_level))
        prices.append(price)
        
        # 成交量：随机变化
        volume = 1000 + np.random.normal(0, 200)
        volumes.append(max(100, volume))
    
    return [(prices[i], volumes[i]) for i in range(num_points)]

def test_strategy_performance():
    """测试策略性能"""
    print("=" * 60)
    print("Strategy3 责任链架构性能测试")
    print("=" * 60)
    
    # 创建策略实例
    strategy = create_strategy3()
    
    # 显示初始配置
    print(f"\n初始配置:")
    print(f"- 模糊推理校正: {strategy.params.enable_fuzzy_correction}")
    print(f"- 控制论辅助: {strategy.params.enable_control_theory}")
    print(f"- 机器学习辅助: {strategy.params.enable_ml_prediction}")
    print(f"- Hull MA周期: {strategy.params.hull_period}")
    print(f"- STC参数: {strategy.params.stc_fast_period}/{strategy.params.stc_slow_period}/{strategy.params.stc_cycle_period}")
    
    # 生成测试数据
    test_data = generate_test_data(200)
    print(f"\n生成测试数据: {len(test_data)} 个数据点")
    
    # 性能测试
    start_time = time.time()
    processing_times = []
    
    print("\n开始处理数据...")
    for i, (price, volume) in enumerate(test_data):
        tick_start = time.time()
        
        # 创建模拟tick数据
        tick = TickData(price, volume)
        
        # 处理tick
        strategy.on_tick(tick)
        
        tick_time = time.time() - tick_start
        processing_times.append(tick_time)
        
        # 每50个数据点显示一次进度
        if (i + 1) % 50 == 0:
            avg_time = np.mean(processing_times[-50:])
            print(f"处理进度: {i+1}/{len(test_data)}, 平均处理时间: {avg_time*1000:.2f}ms")
    
    total_time = time.time() - start_time
    
    # 性能统计
    print(f"\n性能统计:")
    print(f"- 总处理时间: {total_time:.3f}秒")
    print(f"- 平均每tick处理时间: {np.mean(processing_times)*1000:.2f}ms")
    print(f"- 最大处理时间: {np.max(processing_times)*1000:.2f}ms")
    print(f"- 最小处理时间: {np.min(processing_times)*1000:.2f}ms")
    print(f"- 处理速度: {len(test_data)/total_time:.1f} ticks/秒")
    
    # 策略性能摘要
    performance = strategy.get_performance_summary()
    print(f"\n策略性能摘要:")
    for key, value in performance.items():
        if isinstance(value, float):
            print(f"- {key}: {value:.4f}")
        else:
            print(f"- {key}: {value}")
    
    # 信号统计
    print(f"\n信号统计:")
    print(f"- 主信号数量: {len(strategy.state.primary_signals)}")
    print(f"- 校正信号数量: {len(strategy.state.corrected_signals)}")
    print(f"- 最终决策数量: {len(strategy.state.final_decisions)}")
    
    if strategy.state.primary_signals:
        primary_strengths = [s['signal_strength'] for s in strategy.state.primary_signals]
        print(f"- 主信号强度范围: [{np.min(primary_strengths):.3f}, {np.max(primary_strengths):.3f}]")
    
    if strategy.state.corrected_signals:
        final_signals = [s['final_signal'] for s in strategy.state.corrected_signals]
        print(f"- 最终信号强度范围: [{np.min(final_signals):.3f}, {np.max(final_signals):.3f}]")

def test_module_switches():
    """测试模块开关功能"""
    print("\n" + "=" * 60)
    print("模块开关测试")
    print("=" * 60)
    
    # 测试不同模块组合
    test_configs = [
        {"fuzzy": True, "control": True, "ml": True, "name": "全模块"},
        {"fuzzy": True, "control": False, "ml": False, "name": "仅模糊推理"},
        {"fuzzy": False, "control": True, "ml": False, "name": "仅控制论"},
        {"fuzzy": False, "control": False, "ml": True, "name": "仅机器学习"},
        {"fuzzy": False, "control": False, "ml": False, "name": "仅主信号"},
    ]
    
    test_data = generate_test_data(50)  # 较少数据用于快速测试
    
    for config in test_configs:
        print(f"\n测试配置: {config['name']}")
        
        # 创建策略并设置参数
        strategy = create_strategy3()
        strategy.params.enable_fuzzy_correction = config["fuzzy"]
        strategy.params.enable_control_theory = config["control"]
        strategy.params.enable_ml_prediction = config["ml"]
        
        # 重新初始化责任链
        from Strategy3 import SignalProcessingChain
        strategy.signal_chain = SignalProcessingChain(strategy.params)
        
        # 处理数据
        start_time = time.time()
        for price, volume in test_data:
            tick = TickData(price, volume)
            strategy.on_tick(tick)
        
        processing_time = time.time() - start_time
        performance = strategy.get_performance_summary()
        
        print(f"  - 处理时间: {processing_time:.3f}秒")
        print(f"  - 平均延迟: {performance['processing_latency']*1000:.2f}ms")
        print(f"  - 信号数量: {len(strategy.state.primary_signals)}")

def test_signal_chain():
    """测试责任链信号处理"""
    print("\n" + "=" * 60)
    print("责任链信号处理测试")
    print("=" * 60)
    
    from Strategy3 import SignalProcessingChain, Strategy3Params
    
    # 创建测试参数
    params = Strategy3Params()
    
    # 创建责任链
    chain = SignalProcessingChain(params)
    
    # 测试信号数据
    test_signal = {
        'primary_signal': 0.6,
        'current_price': 100.0,
        'target_price': 102.0,
        'trend_strength': 0.5,
        'volatility': 0.02,
        'volume': 1000.0,
        'volume_ratio': 1.2,
        'hull_value': 99.5,
        'stc_value': 75.0,
        'timestamp': time.time()
    }
    
    print(f"输入信号: {test_signal['primary_signal']:.3f}")
    
    # 处理信号
    result = chain.process_signal(test_signal)
    
    print(f"\n处理结果:")
    print(f"- 模糊校正后: {result.get('fuzzy_correction', 'N/A')}")
    print(f"- 控制论校正后: {result.get('control_correction', 'N/A')}")
    print(f"- 最终信号: {result.get('final_signal', 'N/A')}")
    print(f"- 系统稳定性: {result.get('system_stability', 'N/A')}")
    print(f"- ML置信度: {result.get('ml_confidence', 'N/A')}")

if __name__ == "__main__":
    try:
        # 运行所有测试
        test_strategy_performance()
        test_module_switches()
        test_signal_chain()
        
        print("\n" + "=" * 60)
        print("所有测试完成！")
        print("责任链架构工作正常，性能优化成功。")
        print("=" * 60)
        
    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
