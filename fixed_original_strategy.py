"""
修复版原策略
解决信号生成和止损止盈的问题
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional
from strategy_backtester import BaseBacktestStrategy


class FixedOriginalStrategy(BaseBacktestStrategy):
    """修复版原策略"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 策略参数
        self.atr_period = 14
        self.stop_mult = 2.2
        self.profit_mult = 3.0
        self.max_positions = 1  # 简化为单一持仓
        self.order_volume = 1
        
        # 状态变量
        self.entry_price = 0
        self.stop_loss = 0
        self.take_profit = 0
        
    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """计算EMA"""
        return data.ewm(span=period, adjust=False).mean()
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算技术指标
        data['ema_5'] = self.calculate_ema(data['close'], 5)
        data['ema_15'] = self.calculate_ema(data['close'], 15)
        data['ema_30'] = self.calculate_ema(data['close'], 30)
        data['atr'] = self.calculate_atr(data['high'], data['low'], data['close'], self.atr_period)
        
        # 初始化信号列
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        signals['confidence'] = 0.0
        
        # 状态跟踪
        in_position = False
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        
        # 逐行生成信号
        for i in range(30, len(data)):  # 确保有足够的历史数据
            current_data = data.iloc[i]
            
            ema_5 = current_data['ema_5']
            ema_15 = current_data['ema_15']
            ema_30 = current_data['ema_30']
            atr = current_data['atr']
            current_price = current_data['close']
            
            if pd.isna(ema_5) or pd.isna(ema_15) or pd.isna(ema_30) or pd.isna(atr):
                continue
            
            # 如果有持仓，首先检查止损止盈
            if in_position:
                if current_price <= stop_loss:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'stop_loss'
                    signals.loc[data.index[i], 'confidence'] = 1.0
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    continue
                elif current_price >= take_profit:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'take_profit'
                    signals.loc[data.index[i], 'confidence'] = 1.0
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    continue
                # 技术卖出条件
                elif ema_5 < ema_15 < ema_30:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'technical_exit'
                    signals.loc[data.index[i], 'confidence'] = 0.8
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    continue
            
            # 如果没有持仓，检查买入条件
            if not in_position:
                # 买入条件：EMA多头排列
                if ema_5 > ema_15 > ema_30:
                    signals.loc[data.index[i], 'action'] = 'buy'
                    signals.loc[data.index[i], 'reason'] = 'ema_bullish'
                    signals.loc[data.index[i], 'confidence'] = 0.8
                    
                    # 设置止损止盈
                    in_position = True
                    entry_price = current_price
                    stop_loss = current_price - atr * self.stop_mult
                    take_profit = current_price + atr * self.profit_mult
        
        return signals
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            return self.order_volume
        return 0


class FixedOptimizedStrategy(BaseBacktestStrategy):
    """修复版优化策略"""
    
    def __init__(self, initial_capital: float = 100000):
        super().__init__(initial_capital)
        
        # 优化参数
        self.atr_period = 14
        self.stop_mult = 1.5  # 更紧的止损
        self.profit_mult = 2.0  # 更保守的止盈
        self.max_positions = 1
        self.order_volume = 1
        
        # RSI参数
        self.rsi_period = 14
        self.rsi_oversold = 30
        self.rsi_overbought = 70
        
    def calculate_ema(self, data: pd.Series, period: int) -> pd.Series:
        """计算EMA"""
        return data.ewm(span=period, adjust=False).mean()
    
    def calculate_atr(self, high: pd.Series, low: pd.Series, close: pd.Series, period: int) -> pd.Series:
        """计算ATR"""
        tr1 = high - low
        tr2 = abs(high - close.shift(1))
        tr3 = abs(low - close.shift(1))
        tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        return tr.rolling(window=period).mean()
    
    def calculate_rsi(self, data: pd.Series, period: int) -> pd.Series:
        """计算RSI"""
        delta = data.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        return 100 - (100 / (1 + rs))
    
    def generate_signals(self, data: pd.DataFrame) -> pd.DataFrame:
        """生成交易信号"""
        # 计算技术指标
        data['ema_8'] = self.calculate_ema(data['close'], 8)
        data['ema_21'] = self.calculate_ema(data['close'], 21)
        data['ema_50'] = self.calculate_ema(data['close'], 50)
        data['atr'] = self.calculate_atr(data['high'], data['low'], data['close'], self.atr_period)
        data['rsi'] = self.calculate_rsi(data['close'], self.rsi_period)
        
        # 初始化信号列
        signals = pd.DataFrame(index=data.index)
        signals['action'] = None
        signals['reason'] = ''
        signals['confidence'] = 0.0
        
        # 状态跟踪
        in_position = False
        entry_price = 0
        stop_loss = 0
        take_profit = 0
        trailing_stop = 0
        
        # 逐行生成信号
        for i in range(50, len(data)):  # 确保有足够的历史数据
            current_data = data.iloc[i]
            
            ema_8 = current_data['ema_8']
            ema_21 = current_data['ema_21']
            ema_50 = current_data['ema_50']
            atr = current_data['atr']
            rsi = current_data['rsi']
            current_price = current_data['close']
            
            if pd.isna(ema_8) or pd.isna(ema_21) or pd.isna(ema_50) or pd.isna(atr) or pd.isna(rsi):
                continue
            
            # 如果有持仓，首先检查止损止盈
            if in_position:
                # 更新追踪止损
                if current_price > entry_price * 1.02:  # 盈利2%后启动追踪止损
                    new_trailing = current_price - atr * 1.0
                    trailing_stop = max(trailing_stop, new_trailing)
                    stop_loss = max(stop_loss, trailing_stop)
                
                # 检查退出条件
                if current_price <= stop_loss:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'stop_loss'
                    signals.loc[data.index[i], 'confidence'] = 1.0
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    trailing_stop = 0
                    continue
                elif current_price >= take_profit:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'take_profit'
                    signals.loc[data.index[i], 'confidence'] = 1.0
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    trailing_stop = 0
                    continue
                # 技术卖出条件
                elif ema_8 < ema_21 or rsi > self.rsi_overbought:
                    signals.loc[data.index[i], 'action'] = 'sell'
                    signals.loc[data.index[i], 'reason'] = 'technical_exit'
                    signals.loc[data.index[i], 'confidence'] = 0.8
                    in_position = False
                    entry_price = 0
                    stop_loss = 0
                    take_profit = 0
                    trailing_stop = 0
                    continue
            
            # 如果没有持仓，检查买入条件
            if not in_position:
                # 优化的买入条件
                bullish_trend = ema_8 > ema_21 > ema_50
                rsi_ok = self.rsi_oversold < rsi < self.rsi_overbought
                momentum_ok = ema_8 > ema_21 * 1.001  # 短期EMA明显高于中期EMA
                
                if bullish_trend and rsi_ok and momentum_ok:
                    signals.loc[data.index[i], 'action'] = 'buy'
                    signals.loc[data.index[i], 'reason'] = 'optimized_entry'
                    signals.loc[data.index[i], 'confidence'] = 0.9
                    
                    # 设置止损止盈
                    in_position = True
                    entry_price = current_price
                    stop_loss = current_price - atr * self.stop_mult
                    take_profit = current_price + atr * self.profit_mult
                    trailing_stop = stop_loss
        
        return signals
    
    def calculate_position_size(self, signal: Dict, current_price: float) -> int:
        """计算仓位大小"""
        if signal['action'] == 'buy':
            return self.order_volume
        return 0


if __name__ == "__main__":
    # 测试修复版策略
    from strategy_backtester import StrategyBacktester
    import numpy as np
    
    # 生成测试数据
    dates = pd.date_range(start="2023-01-01", periods=1000, freq="1h")
    n = len(dates)
    
    np.random.seed(42)
    base_price = 100
    trend = np.linspace(0, 0.2, n)
    noise = np.random.normal(0, 0.01, n)
    prices = base_price * (1 + trend + noise)
    
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data['close'] * 1.01
    data['low'] = data['close'] * 0.99
    data['volume'] = 1000
    
    print("测试修复版策略")
    print("=" * 40)
    
    # 测试两个策略
    strategies = {
        "修复原策略": FixedOriginalStrategy(),
        "修复优化策略": FixedOptimizedStrategy()
    }
    
    backtester = StrategyBacktester()
    
    for name, strategy in strategies.items():
        print(f"\n{name}:")
        result = backtester.run_backtest(strategy, data)
        
        closed_trades = [t for t in result.trades if not t.is_open]
        print(f"  交易次数: {len(closed_trades)}")
        print(f"  胜率: {result.win_rate:.2%}")
        print(f"  总收益: {result.total_return:.2%}")
        print(f"  最大回撤: {result.max_drawdown:.2%}")
        print(f"  夏普比率: {result.sharpe_ratio:.3f}")
        print(f"  盈亏比: {result.profit_factor:.2f}")
        
        if closed_trades:
            avg_win = np.mean([t.pnl for t in closed_trades if t.pnl > 0]) if any(t.pnl > 0 for t in closed_trades) else 0
            avg_loss = np.mean([t.pnl for t in closed_trades if t.pnl < 0]) if any(t.pnl < 0 for t in closed_trades) else 0
            print(f"  平均盈利: {avg_win:.2f}")
            print(f"  平均亏损: {avg_loss:.2f}")
