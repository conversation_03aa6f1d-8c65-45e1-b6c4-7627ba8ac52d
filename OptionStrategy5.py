from typing import Literal, Dict, List, Tuple, Optional
import numpy as np
import time
import math
from collections import deque
import threading
import asyncio
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# 机器学习相关导入
try:
    from sklearn.neighbors import KNeighborsRegressor
    from sklearn.preprocessing import StandardScaler
    from sklearn.model_selection import train_test_split
    from sklearn.metrics import mean_squared_error
    ML_AVAILABLE = True
except ImportError:
    ML_AVAILABLE = False
    print("Warning: scikit-learn not available. Using fallback ML implementation.")

from pythongo.base import BaseParams, BaseState, Field
from pythongo.classdef import KLineData, OrderData, TickData, TradeData
from pythongo.ui import BaseStrategy
from pythongo.utils import KLineGenerator


class Params(BaseParams):
    """参数映射模型"""
    exchange: str = Field(default="", title="交易所代码")
    instrument_id: str = Field(default="", title="合约代码")
    kline_style: str = Field(default="M1", title="K 线周期")
    mode: Literal["manual", "auto"] = Field(default="manual", title="工作模式")
    price_type: Literal["D1", "D2"] = Field(default="D1", title="价格档位")
    order_volume: int = Field(default=1, title="报单数量")
    max_positions: int = Field(default=5, title="最大持仓数", ge=1, le=10)
    
    # EMA参数设置 - 优化为更灵敏的参数组合
    ema_fast_period: int = Field(default=8, title="快速EMA周期", ge=5, le=12)
    ema_mid_period: int = Field(default=21, title="中速EMA周期", ge=13, le=34)
    ema_slow_period: int = Field(default=55, title="慢速EMA周期", ge=34, le=89)

    # RSI参数设置 - 采用多时间框架RSI
    rsi_period: int = Field(default=14, title="RSI周期", ge=7, le=21)
    rsi_fast_period: int = Field(default=7, title="快速RSI周期", ge=5, le=10)
    rsi_slow_period: int = Field(default=21, title="慢速RSI周期", ge=14, le=28)
    rsi_upper: int = Field(default=70, title="RSI上限", ge=65, le=80)
    rsi_lower: int = Field(default=30, title="RSI下限", ge=20, le=35)
    rsi_overbought: int = Field(default=80, title="RSI超买", ge=75, le=85)
    rsi_oversold: int = Field(default=20, title="RSI超卖", ge=15, le=25)

    # 成交量参数设置 - 增强成交量分析
    volume_ma_period: int = Field(default=20, title="成交量MA周期", ge=10, le=30)
    volume_breakout_mult: float = Field(default=1.8, title="成交量突破倍数", ge=1.5, le=2.5)
    volume_surge_mult: float = Field(default=2.5, title="成交量激增倍数", ge=2.0, le=3.5)
    
    # ATR参数设置
    atr_period: int = Field(default=14, title="ATR周期", ge=7, le=21)
    atr_short_period: int = Field(default=5, title="短期ATR周期", ge=3, le=10)  # 新增短期ATR参数
    stop_mult: float = Field(default=1.2, title="止损倍数", ge=0.8, le=2.0)
    profit_mult: float = Field(default=1.5, title="止盈倍数", ge=1.2, le=2.5)
    trail_step: float = Field(default=0.3, title="追踪步长", ge=0.2, le=1.0)
    vol_threshold: float = Field(default=1.5, title="波动率阈值", ge=1.2, le=2.0)  # 新增波动率阈值参数
    
    # 动态止盈参数
    profit_take_ratio: float = Field(default=0.5, title="止盈回撤比例", ge=0.3, le=0.7)  # 新增：盈利回撤止盈比例
    
    # 手动模式固定止盈止损设置
    fixed_stop_loss: float = Field(default=0, title="固定止损价", ge=0)
    fixed_take_profit: float = Field(default=0, title="固定止盈价", ge=0)
    use_fixed_stops: bool = Field(default=False, title="使用固定止盈止损")
    
    # 趋势判断参数
    trend_period_min: int = Field(default=8, title="最小趋势周期", ge=5, le=15)
    trend_period_max: int = Field(default=20, title="最大趋势周期", ge=15, le=30)
    trend_weight_recent: float = Field(default=0.6, title="近期价格权重", ge=0.5, le=0.8)
    volume_factor_weight: float = Field(default=0.3, title="成交量因子权重", ge=0.2, le=0.5)
    
    # RSI平滑参数
    rsi_smooth_period: int = Field(default=3, title="RSI平滑周期", ge=2, le=5)
    rsi_trend_threshold: float = Field(default=0.3, title="RSI趋势阈值", ge=0.2, le=0.5)
    
    # 波动率过滤参数
    vol_filter_period: int = Field(default=5, title="波动率过滤周期", ge=3, le=10)
    vol_filter_threshold: float = Field(default=1.8, title="波动率过滤阈值", ge=1.5, le=2.5)
    
    # 多周期趋势参数
    trend_short_period: int = Field(default=3, title="短周期", ge=3, le=5)
    trend_mid_period: int = Field(default=5, title="中周期", ge=5, le=8)
    trend_long_period: int = Field(default=10, title="长周期", ge=8, le=15)
    trend_strength_threshold: float = Field(default=0.4, title="趋势强度阈值", ge=0.3, le=0.6)
    trend_duration_min: int = Field(default=2, title="最小趋势持续周期", ge=2, le=4)
    
    # 随机动量指标参数
    stoch_k_period: int = Field(default=9, title="随机指标K周期", ge=5, le=14)
    stoch_d_period: int = Field(default=3, title="随机指标D周期", ge=2, le=5)
    stoch_upper: int = Field(default=80, title="随机指标上限", ge=75, le=85)
    stoch_lower: int = Field(default=20, title="随机指标下限", ge=15, le=25)
    
    # 订单执行参数
    order_timeout: int = Field(default=10, title="订单超时时间(秒)", ge=5, le=30)
    
    # 趋势计算高级参数
    std_filter_period: int = Field(default=20, title="标准差周期", ge=10, le=30)
    std_filter_mult: float = Field(default=1.5, title="标准差倍数", ge=1.0, le=2.0)
    momentum_smooth_period: int = Field(default=5, title="动量平滑周期", ge=3, le=8)
    acceleration_period: int = Field(default=3, title="加速度计算周期", ge=2, le=5)
    
    # 斐波那契参数
    fib_period: int = Field(default=20, title="斐波那契周期", ge=10, le=30)
    fib_deviation: float = Field(default=0.02, title="斐波那契偏差", ge=0.01, le=0.05)
    fib_profit_ratio: float = Field(default=0.618, title="斐波那契止盈比率", ge=0.5, le=0.786)

    # 新增：VWAP参数
    vwap_period: int = Field(default=20, title="VWAP周期", ge=10, le=50)
    vwap_deviation_mult: float = Field(default=2.0, title="VWAP偏差倍数", ge=1.5, le=3.0)

    # 新增：MACD参数
    macd_fast: int = Field(default=12, title="MACD快线", ge=8, le=16)
    macd_slow: int = Field(default=26, title="MACD慢线", ge=20, le=35)
    macd_signal: int = Field(default=9, title="MACD信号线", ge=7, le=12)

    # 新增：布林带参数
    bb_period: int = Field(default=20, title="布林带周期", ge=15, le=30)
    bb_std_mult: float = Field(default=2.0, title="布林带标准差倍数", ge=1.5, le=2.5)

    # 新增：智能过滤器参数
    signal_confirmation_bars: int = Field(default=2, title="信号确认K线数", ge=1, le=5)
    noise_filter_threshold: float = Field(default=0.3, title="噪音过滤阈值", ge=0.1, le=0.5)
    market_regime_lookback: int = Field(default=50, title="市场状态回看周期", ge=30, le=100)


class State(BaseState):
    """状态映射模型"""
    # EMA指标
    ema_fast: float = Field(default=0, title="快线EMA")
    ema_mid: float = Field(default=0, title="中线EMA")
    ema_slow: float = Field(default=0, title="慢线EMA")
    
    # RSI指标
    rsi: float = Field(default=0, title="RSI值")
    rsi_trend: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="RSI趋势")
    
    # 成交量指标
    volume_ma: float = Field(default=0, title="成交量MA")
    volume_ratio: float = Field(default=0, title="量比")
    is_volume_breakout: bool = Field(default=False, title="成交量突破")
    
    # ATR指标
    atr: float = Field(default=0, title="ATR")
    atr_short: float = Field(default=0, title="短期ATR")
    
    # 趋势状态
    trend_type: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="行情模式")
    is_trending: bool = Field(default=False, title="是否趋势")
    trend_strength: float = Field(default=0, title="趋势强度", ge=0, le=1)
    trend_duration: int = Field(default=0, title="趋势持续周期")
    volatility: float = Field(default=0, title="波动率")
    volatility_ratio: float = Field(default=1.0, title="波动率比值")
    
    # 止损止盈
    stop_loss: float = Field(default=0, title="止损价")
    take_profit: float = Field(default=0, title="止盈价")
    trailing_stop: float = Field(default=0, title="追踪止损价")
    
    # 动态止盈止损
    highest_price: float = Field(default=0, title="最高价")
    lowest_price: float = Field(default=0, title="最低价")
    current_profit: float = Field(default=0, title="当前盈亏")
    max_profit: float = Field(default=0, title="最大盈亏")
    
    # 趋势相关新增状态
    trend_period_current: int = Field(default=0, title="当前趋势周期")
    price_momentum: float = Field(default=0, title="价格动量")
    volume_factor: float = Field(default=0, title="成交量因子")
    
    # RSI相关新增状态
    rsi_smooth: float = Field(default=0, title="平滑RSI")
    rsi_momentum: float = Field(default=0, title="RSI动量")
    
    # 波动率相关新增状态
    volatility_state: Literal["低波动", "中等波动", "高波动"] = Field(default="中等波动", title="波动率状态")
    signal_quality: float = Field(default=0, title="信号质量", ge=0, le=1)
    
    # 多周期趋势状态
    trend_short: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="短周期趋势")
    trend_mid: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="中周期趋势")
    trend_long: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="长周期趋势")
    trend_consensus: bool = Field(default=False, title="趋势共识")
    trend_reversal_confirmed: bool = Field(default=False, title="趋势反转确认")
    
    # 随机动量指标状态
    stoch_k: float = Field(default=50, title="随机指标K值")
    stoch_d: float = Field(default=50, title="随机指标D值")
    stoch_momentum: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="随机指标动量")
    
    # 订单执行状态
    order_time: float = Field(default=0, title="订单时间戳")
    
    # 趋势计算高级状态
    price_std: float = Field(default=0, title="价格标准差")
    price_acceleration: float = Field(default=0, title="价格加速度")
    momentum_smoothed: float = Field(default=0, title="平滑动量")
    
    # 斐波那契状态
    fib_levels: dict = Field(default_factory=dict, title="斐波那契水平")
    fib_support: float = Field(default=0, title="当前支撑位")
    fib_resistance: float = Field(default=0, title="当前阻力位")
    fib_trend_quality: float = Field(default=0, title="趋势质量", ge=0, le=1)

    # 新增：VWAP状态
    vwap: float = Field(default=0, title="VWAP值")
    vwap_upper: float = Field(default=0, title="VWAP上轨")
    vwap_lower: float = Field(default=0, title="VWAP下轨")
    vwap_position: Literal["上方", "下方", "中性"] = Field(default="中性", title="价格相对VWAP位置")

    # 新增：MACD状态
    macd_line: float = Field(default=0, title="MACD线")
    macd_signal: float = Field(default=0, title="MACD信号线")
    macd_histogram: float = Field(default=0, title="MACD柱状图")
    macd_trend: Literal["上升", "下降", "震荡"] = Field(default="震荡", title="MACD趋势")

    # 新增：布林带状态
    bb_upper: float = Field(default=0, title="布林带上轨")
    bb_middle: float = Field(default=0, title="布林带中轨")
    bb_lower: float = Field(default=0, title="布林带下轨")
    bb_width: float = Field(default=0, title="布林带宽度")
    bb_position: Literal["上轨", "中轨", "下轨", "突破上轨", "突破下轨"] = Field(default="中轨", title="价格在布林带位置")

    # 新增：智能过滤器状态
    signal_strength_filtered: float = Field(default=0, title="过滤后信号强度", ge=0, le=1)
    noise_level: float = Field(default=0, title="市场噪音水平", ge=0, le=1)
    market_regime: Literal["趋势", "震荡", "突破", "反转"] = Field(default="震荡", title="市场状态")
    confirmation_count: int = Field(default=0, title="信号确认计数")


class DynamicParamsManager:
    """动态参数管理器"""
    def __init__(self):
        self.params = {
            "ema": [5, 15, 30],
            "atr_period": 14,
            "stop_mult": 2.2,
            "profit_mult": 3.0,
            "trail_step": 1.0
        }
        
        self.history = {
            "ema": [],
            "atr_period": [],
            "stop_mult": [],
            "profit_mult": [],
            "trail_step": []
        }
        
        self.constraints = {
            "ema": {
                "min": [2, 8, 15],
                "max": [10, 30, 60],
                "step": [1, 2, 5]
            },
            "atr_period": {
                "min": 7,
                "max": 21,
                "step": 2
            },
            "stop_mult": {
                "min": 1.5,
                "max": 3.0,
                "step": 0.1
            },
            "profit_mult": {
                "min": 2.0,
                "max": 4.0,
                "step": 0.2
            },
            "trail_step": {
                "min": 0.3,
                "max": 1.5,
                "step": 0.1
            }
        }
        
        # 市场状态阈值
        self.volatility_threshold = 0.5
        self.trend_threshold = 0.6
        self.choppy_threshold = 0.4
        
        # 响应级别
        self.response_levels = {
            "aggressive": 3,  # 激进
            "moderate": 2,    # 适中
            "conservative": 1 # 保守
        }
        
        # 市场状态
        self.market_states = {
            "trending": "趋势",
            "choppy": "震荡",
            "high_volatility": "高波动",
            "low_volatility": "低波动"
        }

    def adjust_params(self, trend_strength: float, volatility: float, is_choppy: bool) -> None:
        """根据市场状态调整参数"""
        # 确定响应级别
        response_level = self._determine_response_level(trend_strength, volatility, is_choppy)
        
        # 调整EMA周期
        if trend_strength > self.trend_threshold:
            self.params["ema"] = [3, 10, 20]  # 更快的响应
        elif trend_strength < 0.3:
            self.params["ema"] = [8, 20, 40]  # 更慢的响应
        else:
            self.params["ema"] = [5, 15, 30]  # 标准响应
            
        # 调整ATR周期
        if volatility > self.volatility_threshold * 1.5:
            self.params["atr_period"] = 21  # 更长的周期以平滑波动
        elif volatility < self.volatility_threshold * 0.5:
            self.params["atr_period"] = 7   # 更短的周期以快速响应
        else:
            self.params["atr_period"] = 14  # 标准周期
            
        # 根据响应级别调整止损止盈倍数
        if response_level == self.response_levels["aggressive"]:
            self.params["stop_mult"] = 1.5
            self.params["profit_mult"] = 2.0
            self.params["trail_step"] = 0.5
        elif response_level == self.response_levels["moderate"]:
            self.params["stop_mult"] = 2.2
            self.params["profit_mult"] = 3.0
            self.params["trail_step"] = 1.0
        else:  # conservative
            self.params["stop_mult"] = 2.8
            self.params["profit_mult"] = 3.5
            self.params["trail_step"] = 1.2
            
        # 记录参数历史
        self._update_param_history()

    def _determine_response_level(self, trend_strength: float, volatility: float, is_choppy: bool) -> int:
        """确定响应级别"""
        if trend_strength > 0.7 and volatility < self.volatility_threshold:
            return self.response_levels["aggressive"]
        elif is_choppy or volatility > self.volatility_threshold * 1.5:
            return self.response_levels["conservative"]
        else:
            return self.response_levels["moderate"]

    def _update_param_history(self) -> None:
        """更新参数历史"""
        for param_name, value in self.params.items():
            self.history[param_name].append(value)
            if len(self.history[param_name]) > 20:  # 保持最近20个值
                self.history[param_name].pop(0)

    def get_smoothed_param(self, param_name: str, window: int = 5) -> float:
        """获取平滑后的参数值"""
        if not self.history[param_name]:
            return self.params[param_name]
            
        # 使用移动平均进行平滑
        history = self.history[param_name][-window:]
        return sum(history) / len(history)


class VolumeSuperTrendAI:
    """Volume SuperTrend AI 核心算法实现"""

    def __init__(self, k_neighbors=3, data_points=10, price_trend_len=20, prediction_len=100):
        self.k = k_neighbors
        self.data_points = data_points
        self.price_trend_len = price_trend_len
        self.prediction_len = prediction_len

        # 数据存储
        self.price_history = deque(maxlen=500)
        self.volume_history = deque(maxlen=500)
        self.supertrend_history = deque(maxlen=500)
        self.labels_history = deque(maxlen=500)

        # ML模型
        if ML_AVAILABLE:
            try:
                self.knn_model = KNeighborsRegressor(n_neighbors=k_neighbors, weights='distance')
                self.scaler = StandardScaler()
            except Exception as e:
                print(f"ML模型初始化失败: {e}")
                self.knn_model = None
                self.scaler = None
        else:
            self.knn_model = None
            self.scaler = None

        self.is_trained = False

    def calculate_vwma(self, prices: List[float], volumes: List[float], period: int) -> float:
        """计算成交量加权移动平均"""
        if len(prices) < period or len(volumes) < period:
            return prices[-1] if prices else 0.0

        price_volume = sum(p * v for p, v in zip(prices[-period:], volumes[-period:]))
        total_volume = sum(volumes[-period:])

        return price_volume / total_volume if total_volume > 0 else prices[-1]

    def calculate_supertrend(self, prices: List[float], volumes: List[float],
                           atr_values: List[float], factor: float = 3.0, period: int = 10) -> Tuple[float, int]:
        """计算Volume SuperTrend"""
        if len(prices) < period:
            return prices[-1] if prices else 0.0, 1

        # 计算VWMA
        vwma = self.calculate_vwma(prices, volumes, period)

        # 获取ATR
        atr = atr_values[-1] if atr_values else 0.01

        # 计算上下轨
        upper_band = vwma + factor * atr
        lower_band = vwma - factor * atr

        # 确定趋势方向
        current_price = prices[-1]

        if hasattr(self, 'prev_supertrend') and hasattr(self, 'prev_direction'):
            # 重新定义上下轨
            if upper_band < self.prev_upper_band or prices[-2] > self.prev_upper_band:
                upper_band = upper_band
            else:
                upper_band = self.prev_upper_band

            if lower_band > self.prev_lower_band or prices[-2] < self.prev_lower_band:
                lower_band = lower_band
            else:
                lower_band = self.prev_lower_band

            # 确定方向
            if self.prev_supertrend == self.prev_upper_band:
                if current_price > upper_band:
                    direction = -1
                else:
                    direction = 1
            else:
                if current_price < lower_band:
                    direction = 1
                else:
                    direction = -1
        else:
            direction = 1

        # 确定SuperTrend值
        if direction == -1:
            supertrend = lower_band
        else:
            supertrend = upper_band

        # 保存当前值供下次使用
        self.prev_supertrend = supertrend
        self.prev_direction = direction
        self.prev_upper_band = upper_band
        self.prev_lower_band = lower_band

        return supertrend, direction

    def update_data(self, price: float, volume: float, atr: float):
        """更新数据"""
        self.price_history.append(price)
        self.volume_history.append(volume)

        # 计算SuperTrend
        supertrend, direction = self.calculate_supertrend(
            list(self.price_history),
            list(self.volume_history),
            [atr]
        )

        self.supertrend_history.append(supertrend)

        # 计算标签（用于训练）
        if len(self.price_history) >= self.price_trend_len:
            price_trend = self.calculate_vwma(
                list(self.price_history),
                list(self.volume_history),
                self.price_trend_len
            )

            if len(self.supertrend_history) >= self.prediction_len:
                st_trend = sum(list(self.supertrend_history)[-self.prediction_len:]) / self.prediction_len
                label = 1 if price_trend > st_trend else 0
            else:
                label = 1 if price > supertrend else 0

            self.labels_history.append(label)

        return supertrend, direction

    def train_model(self):
        """训练k-NN模型"""
        if not ML_AVAILABLE or len(self.supertrend_history) < self.data_points or not self.knn_model or not self.scaler:
            return False

        try:
            # 准备训练数据
            X = []
            y = []

            for i in range(self.data_points, len(self.supertrend_history)):
                features = list(self.supertrend_history)[i-self.data_points:i]
                if i < len(self.labels_history):
                    label = self.labels_history[i]
                    X.append(features)
                    y.append(label)

            if len(X) < self.k:
                return False

            X = np.array(X)
            y = np.array(y)

            # 标准化特征
            X_scaled = self.scaler.fit_transform(X)

            # 训练模型
            self.knn_model.fit(X_scaled, y)
            self.is_trained = True

            return True

        except Exception as e:
            print(f"训练模型失败: {e}")
            return False

    def predict_signal(self, current_supertrend: float) -> Tuple[float, str]:
        """预测交易信号"""
        if not self.is_trained or not ML_AVAILABLE or not self.knn_model or not self.scaler:
            # 使用简单规则作为后备
            if len(self.price_history) > 0 and len(self.supertrend_history) > 0:
                current_price = self.price_history[-1]
                if current_price > current_supertrend:
                    return 1.0, "bullish"
                else:
                    return 0.0, "bearish"
            return 0.5, "neutral"

        try:
            # 准备预测数据
            if len(self.supertrend_history) < self.data_points:
                return 0.5, "neutral"

            features = list(self.supertrend_history)[-self.data_points:]
            features = np.array(features).reshape(1, -1)
            features_scaled = self.scaler.transform(features)

            # 预测
            prediction = self.knn_model.predict(features_scaled)[0]

            # 转换为信号
            if prediction > 0.7:
                return prediction, "bullish"
            elif prediction < 0.3:
                return prediction, "bearish"
            else:
                return prediction, "neutral"

        except Exception as e:
            print(f"预测失败: {e}")
            return 0.5, "neutral"


class AdaptiveParameterOptimizer:
    """自适应参数优化器"""

    def __init__(self):
        self.performance_history = deque(maxlen=100)
        self.parameter_history = deque(maxlen=100)
        self.optimization_thread = None
        self.is_optimizing = False

    def add_performance_data(self, params: dict, performance: float):
        """添加性能数据"""
        self.parameter_history.append(params.copy())
        self.performance_history.append(performance)

    def optimize_parameters(self, current_params: dict) -> dict:
        """优化参数"""
        if len(self.performance_history) < 10:
            return current_params

        try:
            # 找到最佳性能的参数组合
            best_idx = np.argmax(list(self.performance_history))
            best_params = self.parameter_history[best_idx]

            # 基于最佳参数进行微调
            optimized_params = current_params.copy()

            # EMA参数优化
            if 'ema' in best_params:
                current_ema = optimized_params.get('ema', [8, 21, 55])
                best_ema = best_params['ema']

                # 渐进式调整
                alpha = 0.1  # 学习率
                optimized_params['ema'] = [
                    int(current_ema[0] + alpha * (best_ema[0] - current_ema[0])),
                    int(current_ema[1] + alpha * (best_ema[1] - current_ema[1])),
                    int(current_ema[2] + alpha * (best_ema[2] - current_ema[2]))
                ]

            # 止损倍数优化
            if 'stop_mult' in best_params:
                current_stop = optimized_params.get('stop_mult', 2.0)
                best_stop = best_params['stop_mult']
                optimized_params['stop_mult'] = current_stop + 0.1 * (best_stop - current_stop)

            return optimized_params

        except Exception as e:
            print(f"参数优化失败: {e}")
            return current_params


class AsyncMonitoringSystem:
    """异步监控系统"""

    def __init__(self, strategy_instance):
        self.strategy = strategy_instance
        self.executor = ThreadPoolExecutor(max_workers=3)
        self.monitoring_active = False

    def start_monitoring(self):
        """启动监控"""
        self.monitoring_active = True

        # 启动性能监控线程
        self.executor.submit(self._performance_monitor)

        # 启动风险监控线程
        self.executor.submit(self._risk_monitor)

        # 启动参数优化线程
        self.executor.submit(self._parameter_optimizer)

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring_active = False
        self.executor.shutdown(wait=True)

    def _performance_monitor(self):
        """性能监控线程"""
        while self.monitoring_active:
            try:
                # 计算当前性能指标
                if hasattr(self.strategy, 'calculate_performance'):
                    performance = self.strategy.calculate_performance()

                    # 记录性能数据
                    if hasattr(self.strategy, 'adaptive_optimizer'):
                        self.strategy.adaptive_optimizer.add_performance_data(
                            self.strategy.current_params,
                            performance
                        )

                time.sleep(60)  # 每分钟检查一次

            except Exception as e:
                print(f"性能监控异常: {e}")
                time.sleep(30)

    def _risk_monitor(self):
        """风险监控线程"""
        while self.monitoring_active:
            try:
                # 检查风险指标
                if hasattr(self.strategy, '_check_risk_control'):
                    risk_ok = self.strategy._check_risk_control()

                    if not risk_ok:
                        # 触发风险控制措施
                        if hasattr(self.strategy, 'emergency_stop'):
                            self.strategy.emergency_stop()

                time.sleep(30)  # 每30秒检查一次

            except Exception as e:
                print(f"风险监控异常: {e}")
                time.sleep(15)

    def _parameter_optimizer(self):
        """参数优化线程"""
        while self.monitoring_active:
            try:
                # 每小时进行一次参数优化
                if hasattr(self.strategy, 'adaptive_optimizer') and hasattr(self.strategy, 'current_params'):
                    optimized_params = self.strategy.adaptive_optimizer.optimize_parameters(
                        self.strategy.current_params
                    )

                    # 应用优化后的参数
                    self.strategy.current_params = optimized_params

                time.sleep(3600)  # 每小时优化一次

            except Exception as e:
                print(f"参数优化异常: {e}")
                time.sleep(1800)  # 出错时30分钟后重试


class OptionStrategy5(BaseStrategy):
    """EMA+RSI+成交量突破买方策略"""
    def __init__(self):
        super().__init__()
        self.params_map = Params()
        self.state_map = State()
        
        # 交易信号
        self.buy_signal: bool = False
        self.sell_signal: bool = False
        
        # 指标缓存
        self._indicator_cache = {
            'ema': {'last_update': 0, 'value': None},
            'rsi': {'last_update': 0, 'value': None},
            'atr': {'last_update': 0, 'value': None},
            'volume': {'last_update': 0, 'value': None},
            'momentum': {'last_update': 0, 'value': None}
        }
        
        # 历史数据缓存
        self._price_history = []
        self._volume_history = []
        self._rsi_history = []
        self._max_history_length = max(
            self.params_map.ema_slow_period,
            self.params_map.rsi_period,
            self.params_map.atr_period,
            self.params_map.volume_ma_period
        )
        
        # 趋势判断相关
        self.trend_period = 10
        self.trend_count = 0
        self.trend_threshold = 0.6
        self.volatility_threshold = 0.4
        self.min_trend_duration = 5
        self.max_trend_duration = 30
        
        # 参数组合
        self.param_sets = {
            "上升": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.5,
                "profit_mult": 2.0,
                "trail_step": 0.5
            },
            "下降": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.2,
                "profit_mult": 1.5,
                "trail_step": 0.3
            },
            "震荡": {
                "ema": [self.params_map.ema_fast_period, 
                       self.params_map.ema_mid_period, 
                       self.params_map.ema_slow_period],
                "stop_mult": 1.0,
                "profit_mult": 1.5,
                "trail_step": 0.3
            }
        }
        
        self.current_params = None
        self.tick: TickData = None
        self.order_id = None
        self.signal_price = 0

        # 动态止盈止损相关
        self.entry_price = 0

        # Volume SuperTrend AI 组件
        self.volume_supertrend_ai = VolumeSuperTrendAI(
            k_neighbors=5,
            data_points=20,
            price_trend_len=30,
            prediction_len=100
        )

        # 自适应参数优化器
        self.adaptive_optimizer = AdaptiveParameterOptimizer()

        # 异步监控系统
        self.monitoring_system = AsyncMonitoringSystem(self)

        # Volume SuperTrend AI 相关状态
        self.supertrend_value = 0.0
        self.supertrend_direction = 1
        self.ai_signal_strength = 0.5
        self.ai_signal_type = "neutral"

        # 性能跟踪
        self.performance_data = {
            "total_trades": 0,
            "winning_trades": 0,
            "total_profit": 0.0,
            "max_drawdown": 0.0,
            "current_drawdown": 0.0,
            "peak_equity": 0.0
        }

        # 启动后自动优化标志
        self.auto_optimization_completed = False
        self.startup_optimization_thread = None
        self.position_size = 0
        self.is_trailing = False
        
        # 订单管理相关
        self.order_time = 0
        
        # 趋势检测历史数据
        self.trend_history = {
            "short": [],
            "mid": [],
            "long": []
        }
        self.trend_reversal_count = 0

    @property
    def main_indicator_data(self) -> dict[str, float]:
        """主图指标"""
        return {
            "EMA_FAST": self.state_map.ema_fast,
            "EMA_MID": self.state_map.ema_mid,
            "EMA_SLOW": self.state_map.ema_slow,
            "ATR": self.state_map.atr
        }

    @property
    def sub_indicator_data(self) -> dict[str, float]:
        """副图指标"""
        return {
            "RSI": self.state_map.rsi,
            "VOLUME_RATIO": self.state_map.volume_ratio,
            "STOP_LOSS": self.state_map.stop_loss,
            "TAKE_PROFIT": self.state_map.take_profit,
            "TRAILING_STOP": self.state_map.trailing_stop
        }

    def _update_indicator_cache(self, indicator_name: str, value) -> None:
        """更新指标缓存"""
        self._indicator_cache[indicator_name] = {
            'last_update': time.time(),
            'value': value
        }

    def _get_cached_indicator(self, indicator_name: str, max_age: float = 1.0):
        """获取缓存的指标值"""
        cache = self._indicator_cache.get(indicator_name)
        if cache and time.time() - cache['last_update'] <= max_age:
            return cache['value']
        return None

    def _calc_volume_ratio(self, period: int) -> float:
        """计算成交量比率"""
        if len(self._volume_history) < period:
            return 1.0
            
        # 计算当前成交量
        current_volume = self._volume_history[-1]
        
        # 计算历史平均成交量
        avg_volume = np.mean(self._volume_history[-period:])
        
        # 计算比率
        ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        return ratio

    def _update_history_data(self, kline: KLineData) -> None:
        """更新历史数据"""
        # 更新价格历史
        self._price_history.append(kline.close)
        
        # 更新成交量历史
        self._volume_history.append(kline.volume)
        
        # 计算并更新RSI历史
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        self._rsi_history.append(rsi)
        
        # 保持历史数据长度
        max_length = self._max_history_length
        if len(self._price_history) > max_length:
            self._price_history.pop(0)
            self._volume_history.pop(0)
            self._rsi_history.pop(0)

    def calc_indicator(self) -> None:
        """计算技术指标（增强版）"""
        if self.current_params is None:
            self.current_params = self.param_sets["震荡"]

        # 计算EMA
        ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
        ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
        ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])

        # 更新EMA状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)

        # 计算ATR
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        atr_short, _ = self.kline_generator.producer.atr(self.params_map.atr_short_period)

        # 更新ATR状态
        self.state_map.atr = round(atr_long, 2)
        self.state_map.atr_short = round(atr_short, 2)
        self.state_map.volatility_ratio = round(atr_short / atr_long, 2) if atr_long > 0 else 1.0

        # 计算多时间框架RSI
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        # rsi_fast = self.kline_generator.producer.rsi(self.params_map.rsi_fast_period)
        # rsi_slow = self.kline_generator.producer.rsi(self.params_map.rsi_slow_period)

        self.state_map.rsi = round(rsi, 2)

        # 计算RSI动量和趋势
        if len(self._rsi_history) >= 3:
            rsi_momentum = np.mean(np.diff(self._rsi_history[-3:]))
            self.state_map.rsi_momentum = float(rsi_momentum)

            if rsi_momentum > 0.5:
                self.state_map.rsi_trend = "上升"
            elif rsi_momentum < -0.5:
                self.state_map.rsi_trend = "下降"
            else:
                self.state_map.rsi_trend = "震荡"

        # 计算VWAP
        self._calc_vwap()

        # 计算MACD
        self._calc_macd()

        # 计算布林带
        self._calc_bollinger_bands()

        # 计算成交量比率和突破
        volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)
        self.state_map.volume_ratio = round(volume_ratio, 2)
        self.state_map.is_volume_breakout = volume_ratio > self.params_map.volume_breakout_mult

        # 计算随机指标
        self._calc_stochastic()

        # 计算智能过滤器
        self._calc_smart_filters()
        
        # 计算止损止盈
        if self.tick:
            current_price = self.tick.last_price
            
            if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
                self.state_map.stop_loss = self.params_map.fixed_stop_loss
                self.state_map.take_profit = self.params_map.fixed_take_profit
            else:
                vol_adjustment = min(self.state_map.volatility_ratio / self.params_map.vol_threshold, 1.0)
                adjusted_stop_mult = self.current_params["stop_mult"] * vol_adjustment
                
                self.state_map.stop_loss = round(
                    current_price - self.state_map.atr * adjusted_stop_mult, 2
                )
                self.state_map.take_profit = round(
                    current_price + self.state_map.atr * self.current_params["profit_mult"], 2
                )
            
            if self.current_params is not None:
                trail_step = self.current_params["trail_step"]
            else:
                trail_step = self.params_map.trail_step
            self.state_map.trailing_stop = round(
                current_price - self.state_map.atr * trail_step, 2
            )

    def _calc_vwap(self) -> None:
        """计算VWAP指标"""
        if len(self._price_history) < self.params_map.vwap_period or len(self._volume_history) < self.params_map.vwap_period:
            return

        prices = np.array(self._price_history[-self.params_map.vwap_period:])
        volumes = np.array(self._volume_history[-self.params_map.vwap_period:])

        # 计算VWAP
        vwap = np.sum(prices * volumes) / np.sum(volumes) if np.sum(volumes) > 0 else prices[-1]
        self.state_map.vwap = round(float(vwap), 2)

        # 计算VWAP上下轨
        price_std = np.std(prices)
        self.state_map.vwap_upper = round(float(vwap + price_std * self.params_map.vwap_deviation_mult), 2)
        self.state_map.vwap_lower = round(float(vwap - price_std * self.params_map.vwap_deviation_mult), 2)

        # 判断价格相对VWAP位置
        current_price = prices[-1]
        if current_price > self.state_map.vwap_upper:
            self.state_map.vwap_position = "上方"
        elif current_price < self.state_map.vwap_lower:
            self.state_map.vwap_position = "下方"
        else:
            self.state_map.vwap_position = "中性"

    def _calc_macd(self) -> None:
        """计算MACD指标"""
        if len(self._price_history) < max(self.params_map.macd_fast, self.params_map.macd_slow):
            return

        prices = np.array(self._price_history)

        # 计算快慢EMA
        ema_fast = self._calc_ema(prices, self.params_map.macd_fast)
        ema_slow = self._calc_ema(prices, self.params_map.macd_slow)

        # 计算MACD线
        macd_line = ema_fast - ema_slow
        self.state_map.macd_line = round(float(macd_line), 4)

        # 计算信号线（MACD的EMA）
        if not hasattr(self, '_macd_history'):
            self._macd_history = []
        self._macd_history.append(macd_line)
        if len(self._macd_history) > 50:
            self._macd_history.pop(0)

        if len(self._macd_history) >= self.params_map.macd_signal:
            signal_line = self._calc_ema(np.array(self._macd_history), self.params_map.macd_signal)
            self.state_map.macd_signal = round(float(signal_line), 4)

            # 计算MACD柱状图
            self.state_map.macd_histogram = round(float(macd_line - signal_line), 4)

            # 判断MACD趋势
            if len(self._macd_history) >= 3:
                macd_momentum = np.mean(np.diff(self._macd_history[-3:]))
                if macd_momentum > 0.001:
                    self.state_map.macd_trend = "上升"
                elif macd_momentum < -0.001:
                    self.state_map.macd_trend = "下降"
                else:
                    self.state_map.macd_trend = "震荡"

    def _calc_ema(self, data: np.ndarray, period: int) -> float:
        """计算EMA"""
        if len(data) < period:
            return float(np.mean(data))

        alpha = 2.0 / (period + 1)
        ema = data[0]
        for price in data[1:]:
            ema = alpha * price + (1 - alpha) * ema
        return float(ema)

    def _calc_bollinger_bands(self) -> None:
        """计算布林带指标"""
        if len(self._price_history) < self.params_map.bb_period:
            return

        prices = np.array(self._price_history[-self.params_map.bb_period:])

        # 计算中轨（SMA）
        middle = np.mean(prices)
        self.state_map.bb_middle = round(float(middle), 2)

        # 计算标准差
        std = np.std(prices)

        # 计算上下轨
        self.state_map.bb_upper = round(float(middle + std * self.params_map.bb_std_mult), 2)
        self.state_map.bb_lower = round(float(middle - std * self.params_map.bb_std_mult), 2)

        # 计算布林带宽度
        self.state_map.bb_width = round(float((self.state_map.bb_upper - self.state_map.bb_lower) / middle), 4)

        # 判断价格在布林带的位置
        current_price = prices[-1]
        if current_price > self.state_map.bb_upper:
            self.state_map.bb_position = "突破上轨"
        elif current_price < self.state_map.bb_lower:
            self.state_map.bb_position = "突破下轨"
        elif current_price > self.state_map.bb_middle:
            self.state_map.bb_position = "上轨"
        elif current_price < self.state_map.bb_middle:
            self.state_map.bb_position = "下轨"
        else:
            self.state_map.bb_position = "中轨"

    def _calc_stochastic(self) -> None:
        """计算随机指标"""
        if len(self._price_history) < self.params_map.stoch_k_period:
            return

        # 获取最近的高低价数据
        recent_prices = self._price_history[-self.params_map.stoch_k_period:]

        # 简化计算：使用收盘价作为高低价的近似
        highest = max(recent_prices)
        lowest = min(recent_prices)
        current = recent_prices[-1]

        # 计算%K
        if highest != lowest:
            k_percent = 100 * (current - lowest) / (highest - lowest)
        else:
            k_percent = 50

        self.state_map.stoch_k = round(float(k_percent), 2)

        # 计算%D（%K的移动平均）
        if not hasattr(self, '_stoch_k_history'):
            self._stoch_k_history = []
        self._stoch_k_history.append(k_percent)
        if len(self._stoch_k_history) > 20:
            self._stoch_k_history.pop(0)

        if len(self._stoch_k_history) >= self.params_map.stoch_d_period:
            d_percent = np.mean(self._stoch_k_history[-self.params_map.stoch_d_period:])
            self.state_map.stoch_d = round(float(d_percent), 2)

            # 判断随机指标动量
            if len(self._stoch_k_history) >= 3:
                stoch_momentum = np.mean(np.diff(self._stoch_k_history[-3:]))
                if stoch_momentum > 1:
                    self.state_map.stoch_momentum = "上升"
                elif stoch_momentum < -1:
                    self.state_map.stoch_momentum = "下降"
                else:
                    self.state_map.stoch_momentum = "震荡"

    def _calc_smart_filters(self) -> None:
        """计算智能过滤器"""
        # 计算市场噪音水平
        if len(self._price_history) >= 10:
            price_changes = np.diff(self._price_history[-10:])
            price_volatility = np.std(price_changes)
            price_trend = abs(np.mean(price_changes))

            # 噪音水平 = 波动性 / 趋势强度
            if price_trend > 0:
                noise_level = price_volatility / price_trend
            else:
                noise_level = 1.0

            self.state_map.noise_level = min(1.0, max(0.0, float(noise_level)))

        # 计算信号确认计数
        signal_count = 0

        # EMA信号确认
        if self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow:
            signal_count += 1

        # RSI信号确认
        if self.params_map.rsi_lower < self.state_map.rsi < self.params_map.rsi_upper:
            signal_count += 1

        # 成交量信号确认
        if self.state_map.volume_ratio > self.params_map.volume_breakout_mult:
            signal_count += 1

        # MACD信号确认
        if self.state_map.macd_line > self.state_map.macd_signal and self.state_map.macd_trend == "上升":
            signal_count += 1

        # 布林带信号确认
        if self.state_map.bb_position in ["上轨", "突破上轨"]:
            signal_count += 1

        self.state_map.confirmation_count = signal_count

        # 计算过滤后的信号强度
        max_signals = 5
        base_strength = signal_count / max_signals
        noise_adjustment = 1.0 - self.state_map.noise_level

        self.state_map.signal_strength_filtered = min(1.0, base_strength * noise_adjustment)

    def calc_trend(self, kline: KLineData) -> None:
        """计算趋势状态"""
        self._update_history_data(kline)
        
        if len(self._price_history) < self.trend_period:
            return
            
        # 计算价格变化
        price_changes = np.diff(self._price_history[-self.trend_period:])
        weights = np.exp(np.linspace(-1, 0, len(price_changes))) * self.params_map.trend_weight_recent
        weights = weights / np.sum(weights)
        
        # 计算加权变化
        weighted_changes = price_changes * weights
        direction_consistency = abs(np.sum(np.sign(weighted_changes))) / len(weighted_changes)
        
        # 计算成交量因子
        volume_ma = np.mean(self._volume_history[-self.trend_period:])
        volume_std = np.std(self._volume_history[-self.trend_period:])
        recent_volume = np.mean(self._volume_history[-3:])
        
        volume_factor = (recent_volume - volume_ma) / (volume_std if volume_std > 0 else 1)
        volume_factor = max(-1.0, min(1.0, float(volume_factor)))
        self.state_map.volume_factor = volume_factor
        
        # 计算趋势强度
        ema_slopes = []
        for period in [self.params_map.ema_fast_period, self.params_map.ema_mid_period]:
            ema = self.kline_generator.producer.ema(period, array=True)
            if len(ema) >= 2:
                slope = (ema[-1] - ema[-2]) / ema[-2]
                ema_slopes.append(slope)
        
        if ema_slopes:
            base_trend_strength = abs(np.mean(ema_slopes))
            volume_component = abs(volume_factor) * self.params_map.volume_factor_weight
            trend_strength = base_trend_strength * (1 + volume_component)
            self.state_map.trend_strength = min(1.0, float(trend_strength))
            
            price_momentum = np.sum(weighted_changes)
            self.state_map.price_momentum = price_momentum
        
        # 判断趋势方向
        if direction_consistency > self.trend_threshold:
            new_trend = "上升" if np.mean(weighted_changes) > 0 else "下降"
        else:
            new_trend = "震荡"
        
        # 更新趋势状态
        if new_trend != self.state_map.trend_type:
            self.state_map.trend_type = new_trend
            self.state_map.is_trending = new_trend != "震荡"
            self.state_map.trend_duration = 1 if new_trend != "震荡" else 0
            self.current_params = self.param_sets[new_trend]
        elif self.state_map.is_trending:
            self.state_map.trend_duration += 1
            if self.state_map.trend_duration > self.max_trend_duration:
                self.state_map.trend_duration = 0
                self.state_map.is_trending = False
                self.state_map.trend_type = "震荡"
                self.current_params = self.param_sets["震荡"]
        
        # 更新波动率状态
        vol_filter = self.kline_generator.producer.atr(self.params_map.vol_filter_period)[0]
        if vol_filter > self.params_map.vol_filter_threshold * self.state_map.atr:
            self.state_map.volatility_state = "高波动"
        elif vol_filter < self.state_map.atr:
            self.state_map.volatility_state = "低波动"
        else:
            self.state_map.volatility_state = "中等波动"

    def _update_adaptive_params(self):
        """更新自适应参数"""
        # 获取市场状态
        market_state = self._get_market_state()
        state_adjustment = self._get_state_adjustment(market_state)
        
        # 获取响应级别
        response_level = self._get_response_level()
        
        # 基础参数调整
        base_params = {
            "ema": [self.params_map.ema_fast_period, 
                   self.params_map.ema_mid_period, 
                   self.params_map.ema_slow_period],
            "stop_mult": self.params_map.stop_mult,
            "profit_mult": self.params_map.profit_mult,
            "trail_step": self.params_map.trail_step
        }
        
        # 根据市场状态调整参数
        if market_state == "high_volatility":
            # 高波动市场：使用更长的周期和更宽松的止损
            base_params["ema"] = [x * 1.2 for x in base_params["ema"]]
            base_params["stop_mult"] *= 1.2
            base_params["profit_mult"] *= 1.1
            base_params["trail_step"] *= 1.2
        elif market_state == "low_volatility":
            # 低波动市场：使用更短的周期和更严格的止损
            base_params["ema"] = [x * 0.8 for x in base_params["ema"]]
            base_params["stop_mult"] *= 0.8
            base_params["profit_mult"] *= 0.9
            base_params["trail_step"] *= 0.8
        elif market_state == "trending":
            # 趋势市场：使用适中的周期和更宽松的止损
            base_params["stop_mult"] *= 1.1
            base_params["profit_mult"] *= 1.2
            base_params["trail_step"] *= 1.1
        else:  # choppy
            # 震荡市场：使用更严格的止损
            base_params["stop_mult"] *= 0.9
            base_params["profit_mult"] *= 0.8
            base_params["trail_step"] *= 0.9
        
        # 根据响应级别调整参数
        if response_level == 3:  # 激进
            base_params["ema"] = [x * 0.8 for x in base_params["ema"]]
            base_params["stop_mult"] *= 0.8
            base_params["profit_mult"] *= 0.9
            base_params["trail_step"] *= 0.8
        elif response_level == 1:  # 保守
            base_params["ema"] = [x * 1.2 for x in base_params["ema"]]
            base_params["stop_mult"] *= 1.2
            base_params["profit_mult"] *= 1.1
            base_params["trail_step"] *= 1.2
        
        # 根据趋势强度调整参数
        if self.state_map.trend_strength > 0.6:
            # 强趋势：放宽止损，提高止盈
            base_params["stop_mult"] *= 1.1
            base_params["profit_mult"] *= 1.2
            base_params["trail_step"] *= 1.1
        elif self.state_map.trend_strength < 0.3:
            # 弱趋势：收紧止损，降低止盈
            base_params["stop_mult"] *= 0.9
            base_params["profit_mult"] *= 0.8
            base_params["trail_step"] *= 0.9
        
        # 根据波动率调整参数
        if self.state_map.volatility_ratio > self.params_map.vol_threshold:
            # 高波动：收紧止损，降低止盈
            base_params["stop_mult"] *= 0.9
            base_params["profit_mult"] *= 0.8
            base_params["trail_step"] *= 0.9
        elif self.state_map.volatility_ratio < self.params_map.vol_threshold * 0.5:
            # 低波动：放宽止损，提高止盈
            base_params["stop_mult"] *= 1.1
            base_params["profit_mult"] *= 1.2
            base_params["trail_step"] *= 1.1
        
        # 应用状态调整因子
        base_params["stop_mult"] *= state_adjustment["stop_mult"]
        base_params["profit_mult"] *= state_adjustment["profit_mult"]
        base_params["trail_step"] *= state_adjustment["trail_step"]
        
        # 确保参数在合理范围内
        self.current_params = {
            "ema": [max(3, min(60, x)) for x in base_params["ema"]],
            "stop_mult": max(0.8, min(3.0, base_params["stop_mult"])),
            "profit_mult": max(1.2, min(4.0, base_params["profit_mult"])),
            "trail_step": max(0.3, min(1.5, base_params["trail_step"]))
        }
        
        # 更新参数历史
        self._update_param_history()

    def _update_param_history(self):
        """更新参数历史"""
        if not hasattr(self, 'param_history') or self.param_history is None:
            self.param_history = {
                'ema': [],
                'stop_mult': [],
                'profit_mult': [],
                'trail_step': []
            }

        if self.current_params is not None:
            # 添加当前参数到历史
            self.param_history['ema'].append(self.current_params["ema"])
            self.param_history['stop_mult'].append(self.current_params["stop_mult"])
            self.param_history['profit_mult'].append(self.current_params["profit_mult"])
            self.param_history['trail_step'].append(self.current_params["trail_step"])

            # 保持历史长度
            max_history = 20
            for key in self.param_history:
                if len(self.param_history[key]) > max_history:
                    self.param_history[key] = self.param_history[key][-max_history:]

    def _get_smoothed_params(self) -> dict:
        """获取平滑后的参数"""
        if not hasattr(self, 'param_history') or self.param_history is None or self.current_params is None:
            return self.current_params or {}

        smoothed_params = {}

        # 对每个参数进行平滑
        for key in self.current_params:
            if key == 'ema' and len(self.param_history[key]) > 0:
                # EMA参数需要分别平滑
                smoothed_params[key] = [
                    float(np.mean([x[i] for x in self.param_history[key]]))
                    for i in range(3)
                ]
            elif len(self.param_history.get(key, [])) > 0:
                # 其他参数直接平滑
                smoothed_params[key] = float(np.mean(self.param_history[key]))
            else:
                # 如果没有历史数据，使用当前值
                smoothed_params[key] = self.current_params[key]

        return smoothed_params

    def calc_signal(self, kline: KLineData):
        """计算交易信号（Volume SuperTrend AI版本）"""
        # 更新自适应参数
        self._update_adaptive_params()

        # 计算传统指标
        self.calc_trend(kline)
        self.calc_indicator()

        # 获取当前价格和成交量
        current_price = self.tick.last_price if self.tick else kline.close
        current_volume = kline.volume

        # 计算ATR
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)

        # 更新Volume SuperTrend AI数据
        self.supertrend_value, self.supertrend_direction = self.volume_supertrend_ai.update_data(
            current_price, current_volume, atr_long
        )

        # 训练AI模型（如果有足够数据）
        if len(self.volume_supertrend_ai.supertrend_history) >= 50:
            if not self.volume_supertrend_ai.is_trained:
                training_success = self.volume_supertrend_ai.train_model()
                if training_success:
                    print("Volume SuperTrend AI模型训练完成")

        # 获取AI预测信号
        self.ai_signal_strength, self.ai_signal_type = self.volume_supertrend_ai.predict_signal(
            self.supertrend_value
        )

        # 计算Volume SuperTrend AI信号
        vst_ai_signal = self._calculate_vst_ai_signal(current_price)

        # 计算传统技术指标信号
        traditional_signal = self._calculate_traditional_signal()

        # 计算成交量确认信号
        volume_confirmation = self._calculate_volume_confirmation()

        # 综合信号计算
        combined_signal_strength = self._combine_signals(
            vst_ai_signal, traditional_signal, volume_confirmation
        )

        # 风险控制检查
        risk_ok = self._check_risk_control()

        # 生成最终交易信号
        self.buy_signal = (
            combined_signal_strength > 0.7 and
            self.ai_signal_type == "bullish" and
            self.supertrend_direction == -1 and  # 上升趋势
            current_price > self.supertrend_value and
            volume_confirmation > 0.5 and
            risk_ok
        )

        # 卖出信号
        self.sell_signal = (
            combined_signal_strength < 0.3 or
            self.ai_signal_type == "bearish" or
            self.supertrend_direction == 1 or  # 下降趋势
            current_price < self.supertrend_value * 0.98 or  # 跌破SuperTrend 2%
            not risk_ok
        )

        # 更新性能数据
        self._update_performance_tracking()

        # 更新动态止盈止损
        if self.tick:
            self.update_dynamic_stops(self.tick.last_price)

    def _calculate_vst_ai_signal(self, current_price: float) -> float:
        """计算Volume SuperTrend AI信号强度"""
        signal_strength = 0.0

        # 1. SuperTrend方向信号 (权重40%)
        if self.supertrend_direction == -1:  # 上升趋势
            signal_strength += 0.4

        # 2. 价格相对SuperTrend位置 (权重30%)
        if self.supertrend_value > 0:
            price_ratio = current_price / self.supertrend_value
            if price_ratio > 1.01:  # 价格高于SuperTrend 1%
                signal_strength += 0.3 * min((price_ratio - 1) * 10, 1.0)

        # 3. AI预测信号强度 (权重30%)
        if self.ai_signal_type == "bullish":
            signal_strength += 0.3 * self.ai_signal_strength
        elif self.ai_signal_type == "bearish":
            signal_strength -= 0.3 * (1 - self.ai_signal_strength)

        return max(0.0, min(1.0, signal_strength))

    def _calculate_traditional_signal(self) -> float:
        """计算传统技术指标信号"""
        signal_strength = 0.0

        # EMA排列信号 (权重35%)
        if (self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow):
            signal_strength += 0.35

        # RSI信号 (权重25%)
        if 30 < self.state_map.rsi < 70:
            if self.state_map.rsi > 50:
                signal_strength += 0.25 * ((self.state_map.rsi - 50) / 20)

        # 趋势强度信号 (权重25%)
        if hasattr(self.state_map, 'trend_strength'):
            signal_strength += 0.25 * self.state_map.trend_strength

        # 动量信号 (权重15%)
        if hasattr(self.state_map, 'momentum_smoothed') and self.state_map.momentum_smoothed > 0:
            signal_strength += 0.15

        return max(0.0, min(1.0, signal_strength))

    def _calculate_volume_confirmation(self) -> float:
        """计算成交量确认信号"""
        if not hasattr(self.state_map, 'volume_ratio'):
            return 0.5

        # 成交量突破确认
        volume_signal = 0.0

        if self.state_map.volume_ratio > 1.5:  # 成交量放大50%以上
            volume_signal = min(self.state_map.volume_ratio / 3.0, 1.0)
        elif self.state_map.volume_ratio > 1.2:  # 成交量放大20%以上
            volume_signal = 0.5
        else:
            volume_signal = 0.3

        return volume_signal

    def _combine_signals(self, vst_ai: float, traditional: float, volume: float) -> float:
        """综合信号计算"""
        # 权重分配：VST AI 50%, 传统指标 30%, 成交量 20%
        combined = vst_ai * 0.5 + traditional * 0.3 + volume * 0.2

        # 如果AI信号很强，增加权重
        if self.ai_signal_strength > 0.8:
            combined = combined * 1.2

        # 如果成交量确认很强，增加权重
        if volume > 0.8:
            combined = combined * 1.1

        return max(0.0, min(1.0, combined))

    def _check_risk_control(self) -> bool:
        """风险控制检查"""
        try:
            # 1. 检查最大持仓
            position = self.get_position(self.params_map.instrument_id)
            if abs(position.net_position) >= self.params_map.max_positions:
                return False

            # 2. 检查账户风险
            if hasattr(self, '_check_capital_risk'):
                if not self._check_capital_risk():
                    return False

            # 3. 检查市场波动率
            if hasattr(self.state_map, 'volatility_ratio'):
                if self.state_map.volatility_ratio > self.params_map.vol_threshold * 2:
                    return False

            # 4. 检查连续亏损
            if hasattr(self, 'performance_data'):
                recent_trades = self.performance_data.get('total_trades', 0)
                winning_trades = self.performance_data.get('winning_trades', 0)

                if recent_trades >= 5:
                    win_rate = winning_trades / recent_trades
                    if win_rate < 0.3:  # 胜率低于30%
                        return False

            return True

        except Exception as e:
            print(f"风险控制检查异常: {e}")
            return False

    def _update_performance_tracking(self):
        """更新性能跟踪数据"""
        try:
            # 这里可以添加更详细的性能跟踪逻辑
            # 暂时保持简单实现
            pass
        except Exception as e:
            print(f"性能跟踪更新异常: {e}")

    def _startup_optimization(self):
        """启动时参数优化"""
        try:
            print("开始启动参数优化...")
            time.sleep(30)  # 等待30秒收集初始数据

            # 执行参数优化逻辑
            optimization_params = [
                {"ema": [5, 13, 34], "stop_mult": 1.8, "profit_mult": 2.5},
                {"ema": [8, 21, 55], "stop_mult": 2.0, "profit_mult": 3.0},
                {"ema": [12, 26, 50], "stop_mult": 1.5, "profit_mult": 2.0},
            ]

            best_params = None
            best_score = -float('inf')

            for params in optimization_params:
                # 模拟测试参数组合
                score = self._evaluate_parameters(params)
                if score > best_score:
                    best_score = score
                    best_params = params

            if best_params:
                self.current_params = best_params
                print(f"参数优化完成，最佳参数: {best_params}")

            self.auto_optimization_completed = True

        except Exception as e:
            print(f"启动优化异常: {e}")
            self.auto_optimization_completed = True

    def _start_connection_monitor(self):
        """启动网络连接监控"""
        def connection_monitor():
            while self.trading:
                try:
                    # 检查K线生成器状态
                    if self.kline_generator is None:
                        self.log("检测到离线模式，尝试重新连接...")
                        self._attempt_reconnection()

                    time.sleep(60)  # 每分钟检查一次

                except Exception as e:
                    self.log(f"连接监控错误: {e}")
                    time.sleep(30)

        # 启动连接监控线程
        self.connection_monitor_thread = threading.Thread(
            target=connection_monitor,
            daemon=True
        )
        self.connection_monitor_thread.start()
        self.log("网络连接监控已启动")

    def _attempt_reconnection(self):
        """尝试重新连接"""
        try:
            self.log("尝试重新建立K线连接...")

            self.kline_generator = KLineGenerator(
                callback=self.callback,
                real_time_callback=self.real_time_callback,
                exchange=self.params_map.exchange,
                instrument_id=self.params_map.instrument_id,
                style=self.params_map.kline_style
            )

            self.kline_generator.push_history_data()
            self.log("K线连接重新建立成功")

        except Exception as e:
            self.log(f"重连失败: {e}")

    def _evaluate_parameters(self, params: dict) -> float:
        """评估参数组合性能"""
        try:
            # 简单的参数评估逻辑
            # 实际应用中可以使用历史数据回测

            # 基于EMA参数的评分
            ema_score = 1.0
            if params["ema"][0] < params["ema"][1] < params["ema"][2]:
                ema_score = 1.2

            # 基于止损倍数的评分
            stop_score = 1.0
            if 1.5 <= params["stop_mult"] <= 2.5:
                stop_score = 1.1

            # 基于止盈倍数的评分
            profit_score = 1.0
            if 2.0 <= params["profit_mult"] <= 3.5:
                profit_score = 1.1

            return ema_score * stop_score * profit_score + np.random.normal(0, 0.1)

        except Exception as e:
            print(f"参数评估异常: {e}")
            return 0.0

    def calculate_performance(self) -> float:
        """计算当前性能指标"""
        try:
            if not hasattr(self, 'performance_data'):
                return 0.0

            total_trades = self.performance_data.get('total_trades', 0)
            if total_trades == 0:
                return 0.0

            winning_trades = self.performance_data.get('winning_trades', 0)
            total_profit = self.performance_data.get('total_profit', 0.0)
            max_drawdown = self.performance_data.get('max_drawdown', 0.0)

            # 计算综合性能分数
            win_rate = winning_trades / total_trades
            avg_profit = total_profit / total_trades

            # 综合评分公式
            performance_score = (
                win_rate * 0.4 +  # 胜率权重40%
                (avg_profit / 100) * 0.3 +  # 平均盈利权重30%
                (1 - max_drawdown) * 0.3  # 最大回撤权重30%
            )

            return max(0.0, min(1.0, performance_score))

        except Exception as e:
            print(f"性能计算异常: {e}")
            return 0.0

    def emergency_stop(self):
        """紧急停止"""
        try:
            print("触发紧急停止...")

            # 平仓所有持仓
            position = self.get_position(self.params_map.instrument_id)
            if position.net_position != 0:
                self.sell_signal = True
                self.exec_signal()

            # 停止交易
            self.trading = False

        except Exception as e:
            print(f"紧急停止异常: {e}")

    def _get_response_level(self) -> int:
        """获取响应级别"""
        # 根据市场状态和趋势强度确定响应级别
        if self.state_map.trend_strength > 0.7 and self.state_map.volatility_ratio < self.params_map.vol_threshold:
            return 3  # 激进
        elif self.state_map.trend_strength < 0.3 or self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5:
            return 1  # 保守
        else:
            return 2  # 适中

    def _get_base_signals(self) -> dict:
        """获取基础信号"""
        return {
            "trend": {
                "ema_alignment": self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow,
                "trend_strength": self.state_map.trend_strength > 0.4,
                "trend_duration": self.state_map.trend_duration >= self.params_map.trend_duration_min
            },
            "rsi": {
                "level": self.params_map.rsi_lower < self.state_map.rsi < self.params_map.rsi_upper,
                "momentum": self.state_map.rsi_momentum > 0,
                "trend": self.state_map.rsi_trend == "上升"
            },
            "volume": {
                "ratio": self.state_map.volume_ratio > self.params_map.volume_breakout_mult,
                "factor": self.state_map.volume_factor > 0,
                "breakout": self.state_map.is_volume_breakout
            },
            "momentum": {
                "smoothed": self.state_map.momentum_smoothed > 0,
                "acceleration": self.state_map.price_acceleration > 0,
                "stoch": self.state_map.stoch_k > self.state_map.stoch_d
            }
        }

    def _calculate_signal_strength(self, base_signals: dict) -> float:
        """计算信号强度"""
        signal_strength = 0.0
        
        # 趋势信号（权重0.35）
        trend_strength = 0.0
        if base_signals["trend"]["ema_alignment"]:
            trend_strength += 0.4
        if base_signals["trend"]["trend_strength"]:
            trend_strength += 0.3
        if base_signals["trend"]["trend_duration"]:
            trend_strength += 0.3
        signal_strength += trend_strength * 0.35
        
        # RSI信号（权重0.25）
        rsi_strength = 0.0
        if base_signals["rsi"]["level"]:
            rsi_strength += 0.4
        if base_signals["rsi"]["momentum"]:
            rsi_strength += 0.3
        if base_signals["rsi"]["trend"]:
            rsi_strength += 0.3
        signal_strength += rsi_strength * 0.25
        
        # 成交量信号（权重0.25）
        volume_strength = 0.0
        if base_signals["volume"]["ratio"]:
            volume_strength += 0.4
        if base_signals["volume"]["factor"]:
            volume_strength += 0.3
        if base_signals["volume"]["breakout"]:
            volume_strength += 0.3
        signal_strength += volume_strength * 0.25
        
        # 动量信号（权重0.15）
        momentum_strength = 0.0
        if base_signals["momentum"]["smoothed"]:
            momentum_strength += 0.4
        if base_signals["momentum"]["acceleration"]:
            momentum_strength += 0.3
        if base_signals["momentum"]["stoch"]:
            momentum_strength += 0.3
        signal_strength += momentum_strength * 0.15
        
        return signal_strength

    def _confirm_signal(
        self,
        response_level: int,
        base_signals: dict,
        signal_strength: float,
        market_state: str,
        state_adjustment: dict
    ) -> bool:
        """确认信号"""
        # 根据响应级别调整确认要求
        if response_level == 3:  # 激进响应
            # 快速确认：满足2个主要指标即可
            confirm_count = sum([
                sum(base_signals["trend"].values()) >= 2,
                sum(base_signals["rsi"].values()) >= 2,
                sum(base_signals["volume"].values()) >= 2,
                sum(base_signals["momentum"].values()) >= 2
            ])
            return confirm_count >= 2 and signal_strength > 0.5
        
        elif response_level == 2:  # 积极响应
            # 标准确认：信号强度达标且至少2个主要指标确认
            if signal_strength < 0.6:
                return False
                
            confirm_count = sum([
                sum(base_signals["trend"].values()) >= 2,
                sum(base_signals["rsi"].values()) >= 2,
                sum(base_signals["volume"].values()) >= 2
            ])
            return confirm_count >= 2
        
        else:  # 保守响应
            # 严格确认：需要所有主要指标确认
            if signal_strength < 0.7:
                return False
                
            return (
                all(base_signals["trend"].values()) and
                all(base_signals["rsi"].values()) and
                all(base_signals["volume"].values())
            )

    def on_tick(self, tick: TickData):
        super().on_tick(tick)
        self.tick = tick
        self.kline_generator.tick_to_kline(tick)

    def on_order_cancel(self, order: OrderData) -> None:
        """撤单推送回调"""
        super().on_order_cancel(order)
        self.order_id = None

    def on_trade(self, trade: TradeData, log: bool = False) -> None:
        """成交回报处理"""
        super().on_trade(trade, log)
        self.order_id = None
        
        # 更新入场价格和相关状态
        if trade.direction == "buy":
            self.entry_price = trade.price
            self.state_map.highest_price = trade.price
            self.state_map.lowest_price = trade.price
        
        # 更新持仓数量
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

    def on_start(self):
        """策略启动"""
        self.log("策略初始化开始...")

        # 重置所有状态
        self._reset_state()

        # 初始化K线生成器 - 添加错误处理
        max_retries = 3
        retry_delay = 5  # 秒

        for attempt in range(max_retries):
            try:
                self.log(f"尝试初始化K线生成器 (第{attempt + 1}次)")

                self.kline_generator = KLineGenerator(
                    callback=self.callback,
                    real_time_callback=self.real_time_callback,
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    style=self.params_map.kline_style
                )

                # 推送历史数据
                self.kline_generator.push_history_data()
                self.log("K线生成器初始化成功")
                break

            except Exception as e:
                error_msg = str(e)
                self.log(f"K线生成器初始化失败 (第{attempt + 1}次): {error_msg}")

                if "REQUEST_EXPIRED" in error_msg or "签名验证错误" in error_msg:
                    self.log("检测到签名验证错误，可能是网络连接问题")

                if attempt < max_retries - 1:
                    self.log(f"等待{retry_delay}秒后重试...")
                    time.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避
                else:
                    self.log("K线生成器初始化最终失败，启用离线模式")
                    self.kline_generator = None
                    # 不抛出异常，允许策略在离线模式下运行

        super().on_start()

        # 初始化参数
        if self.params_map.mode == "manual":
            self.current_params = {
                "ema": [self.params_map.ema_fast_period,
                       self.params_map.ema_mid_period,
                       self.params_map.ema_slow_period],
                "stop_mult": self.params_map.stop_mult,
                "profit_mult": self.params_map.profit_mult,
                "trail_step": self.params_map.trail_step
            }
        else:
            self.current_params = self.param_sets["震荡"]

        # 设置历史数据最大长度
        self._max_history_length = max(
            self.params_map.ema_slow_period,
            self.params_map.rsi_period,
            self.params_map.atr_period,
            self.params_map.volume_ma_period
        ) * 2  # 预留足够的历史数据空间

        # 启动异步监控系统
        self.monitoring_system.start_monitoring()

        # 启动网络连接监控
        self._start_connection_monitor()

        # 启动自动参数优化（异步）
        self.startup_optimization_thread = threading.Thread(
            target=self._startup_optimization,
            daemon=True
        )
        self.startup_optimization_thread.start()

        self.update_status_bar()

    def on_stop(self):
        """策略停止"""
        # 停止监控系统
        if hasattr(self, 'monitoring_system'):
            self.monitoring_system.stop_monitoring()

        # 等待优化线程结束
        if hasattr(self, 'startup_optimization_thread') and self.startup_optimization_thread:
            if self.startup_optimization_thread.is_alive():
                # 给线程一些时间自然结束
                self.startup_optimization_thread.join(timeout=5)

        super().on_stop()

    def callback(self, kline: KLineData) -> None:
        """接受 K 线回调"""
        # 检查是否在离线模式
        if self.kline_generator is None:
            self.log("离线模式：跳过K线回调处理")
            return

        # 更新历史数据
        self._update_history_data(kline)

        # 计算指标和信号
        self.calc_signal(kline)

        # 信号执行
        self.exec_signal()

        # 计算主图指标
        try:
            if self.current_params is not None:
                ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
                ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
                ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
            else:
                ema_fast = self.kline_generator.producer.ema(self.params_map.ema_fast_period)
                ema_mid = self.kline_generator.producer.ema(self.params_map.ema_mid_period)
                ema_slow = self.kline_generator.producer.ema(self.params_map.ema_slow_period)
            atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)
        except Exception as e:
            self.log(f"指标计算失败: {e}")
            # 使用默认值
            ema_fast = kline.close
            ema_mid = kline.close
            ema_slow = kline.close
            atr_long = kline.close * 0.01

        # 更新主图指标状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)
        self.state_map.atr = round(atr_long, 2)

        # 计算副图指标
        try:
            rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
            volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)
        except Exception as e:
            self.log(f"副图指标计算失败: {e}")
            # 使用默认值
            rsi = 50.0
            volume_ratio = 1.0

        # 更新副图指标状态
        self.state_map.rsi = round(rsi, 2)
        self.state_map.volume_ratio = round(volume_ratio, 2)

        # 更新图表
        self.widget.recv_kline({
            "kline": kline,
            "signal_price": self.signal_price,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        if self.trading:
            self.update_status_bar()

    def real_time_callback(self, kline: KLineData) -> None:
        """使用收到的实时推送 K 线来计算指标并更新线图"""
        # 更新历史数据
        self._update_history_data(kline)
        
        # 计算指标和信号
        self.calc_signal(kline)

        # 计算主图指标
        if self.current_params is not None:
            ema_fast = self.kline_generator.producer.ema(self.current_params["ema"][0])
            ema_mid = self.kline_generator.producer.ema(self.current_params["ema"][1])
            ema_slow = self.kline_generator.producer.ema(self.current_params["ema"][2])
        else:
            ema_fast = self.kline_generator.producer.ema(self.params_map.ema_fast_period)
            ema_mid = self.kline_generator.producer.ema(self.params_map.ema_mid_period)
            ema_slow = self.kline_generator.producer.ema(self.params_map.ema_slow_period)
        atr_long, _ = self.kline_generator.producer.atr(self.params_map.atr_period)

        # 更新主图指标状态
        self.state_map.ema_fast = round(ema_fast, 2)
        self.state_map.ema_mid = round(ema_mid, 2)
        self.state_map.ema_slow = round(ema_slow, 2)
        self.state_map.atr = round(atr_long, 2)

        # 计算副图指标
        rsi = self.kline_generator.producer.rsi(self.params_map.rsi_period)
        volume_ratio = self._calc_volume_ratio(self.params_map.volume_ma_period)

        # 更新副图指标状态
        self.state_map.rsi = round(rsi, 2)
        self.state_map.volume_ratio = round(volume_ratio, 2)

        # 更新图表
        self.widget.recv_kline({
            "kline": kline,
            **self.main_indicator_data,
            **self.sub_indicator_data
        })

        self.update_status_bar()

    def update_dynamic_stops(self, current_price: float) -> None:
        """更新动态止盈止损（多级响应版）"""
        if self.position_size <= 0:
            return
            
        # 如果使用固定止盈止损，则不更新动态止损
        if self.params_map.mode == "manual" and self.params_map.use_fixed_stops:
            return
            
        # 更新最高价和最低价
        if current_price > self.state_map.highest_price:
            self.state_map.highest_price = current_price
        if current_price < self.state_map.lowest_price or self.state_map.lowest_price == 0:
            self.state_map.lowest_price = current_price
            
        # 计算当前盈亏
        self.state_map.current_profit = (current_price - self.entry_price) * self.position_size
        if self.state_map.current_profit > self.state_map.max_profit:
            self.state_map.max_profit = self.state_map.current_profit
            
        # 获取市场状态和调整因子
        market_state = self._get_market_state()
        state_adjustment = self._get_state_adjustment(market_state)
        
        # 获取响应级别
        response_level = self._get_response_level()
        
        # 基础ATR倍数
        if self.current_params is not None:
            base_atr_multiple = self.state_map.atr * self.current_params["stop_mult"]
        else:
            base_atr_multiple = self.state_map.atr * self.params_map.stop_mult
        
        # 根据市场状态和风险调整止损倍数
        risk_adjustment = state_adjustment["stop_mult"]
        atr_multiple = base_atr_multiple * risk_adjustment
        
        if self.state_map.current_profit > 0:
            # 根据响应级别调整追踪止损
            if response_level == 3:  # 激进
                trail_distance = base_atr_multiple * 0.6
                profit_target = self.state_map.highest_price + base_atr_multiple * 1.2
            elif response_level == 2:  # 适中
                trail_distance = base_atr_multiple * 0.8
                profit_target = self.state_map.highest_price + base_atr_multiple * 1.5
            else:  # 保守
                trail_distance = base_atr_multiple
                profit_target = self.state_map.highest_price + base_atr_multiple * 2.0
            
            # 根据盈利水平动态调整
            if self.entry_price > 0 and self.position_size > 0:
                profit_ratio = self.state_map.current_profit / (self.entry_price * self.position_size)
                
                # 盈利水平调整
                if profit_ratio > 0.05:  # 大盈利
                    trail_distance *= 0.8
                    profit_target = self.state_map.highest_price + base_atr_multiple * 1.8
                elif profit_ratio > 0.02:  # 中等盈利
                    trail_distance *= 0.9
                    profit_target = self.state_map.highest_price + base_atr_multiple * 1.5
                
                # 趋势强度调整
                if self.state_map.trend_strength > 0.6:
                    trail_distance *= 1.1  # 强趋势时放宽追踪
                elif self.state_map.trend_strength < 0.3:
                    trail_distance *= 0.9  # 弱趋势时收紧追踪
                
                # 波动率调整
                if self.state_map.volatility_ratio > self.params_map.vol_threshold:
                    trail_distance *= 0.9  # 高波动时收紧追踪
                
                # 最终调整
                trail_distance *= risk_adjustment
                
                # 设置止损价格
                self.state_map.stop_loss = round(
                    max(
                        self.entry_price,  # 不低于入场价
                        self.state_map.highest_price - trail_distance
                    ),
                    2
                )
                
                # 设置止盈价格
                self.state_map.take_profit = round(profit_target, 2)
            else:
                # 如果无法计算盈利比例，使用默认值
                self.state_map.stop_loss = round(
                    max(
                        self.entry_price,
                        self.state_map.highest_price - base_atr_multiple
                    ),
                    2
                )
                if self.current_params is not None:
                    profit_mult = self.current_params["profit_mult"]
                else:
                    profit_mult = self.params_map.profit_mult
                self.state_map.take_profit = round(
                    self.state_map.highest_price + base_atr_multiple * profit_mult,
                    2
                )
        else:
            # 未盈利时的保护性止损
            if response_level == 3:  # 激进
                protection_stop = self.entry_price - atr_multiple * 0.8
            elif response_level == 2:  # 适中
                protection_stop = self.entry_price - atr_multiple
            else:  # 保守
                protection_stop = self.entry_price - atr_multiple * 1.2
            
            # 确保止损不会太远
            max_loss_distance = atr_multiple * 1.5
            protection_stop = max(
                protection_stop,
                self.entry_price - max_loss_distance
            )
            
            self.state_map.stop_loss = round(protection_stop, 2)

    def _get_market_state(self) -> str:
        """获取市场状态（增强版）"""
        # 1. 计算基础指标
        volatility_score = self._calculate_volatility_score()
        trend_score = self._calculate_trend_score()
        volume_score = self._calculate_volume_score()
        momentum_score = self._calculate_momentum_score()
        
        # 2. 计算综合得分
        market_score = {
            "volatility": volatility_score,
            "trend": trend_score,
            "volume": volume_score,
            "momentum": momentum_score
        }
        
        # 3. 确定市场状态
        if trend_score > 0.7 and volatility_score < 0.3:
            return "趋势"
        elif trend_score < 0.3 and volatility_score > 0.7:
            return "震荡"
        elif volume_score > 0.7 and momentum_score > 0.7:
            return "突破"
        elif trend_score < 0.3 and momentum_score < 0.3:
            return "反转"
        else:
            return "平衡"

    def _get_state_adjustment(self, market_state: str) -> dict:
        """根据市场状态获取参数调整因子
        
        Args:
            market_state: 市场状态
            
        Returns:
            dict: 包含各参数调整因子的字典
        """
        # 默认调整因子
        adjustment = {
            "ema": 1.0,  # EMA周期调整因子
            "stop_mult": 1.0,  # 止损倍数调整因子
            "profit_mult": 1.0,  # 止盈倍数调整因子
            "trail_step": 1.0,  # 追踪步长调整因子
            "volume": 1.0,  # 成交量调整因子
            "rsi": 1.0  # RSI调整因子
        }
        
        # 根据市场状态调整参数
        if market_state == "趋势":
            # 趋势市场下，增加EMA周期，降低止损倍数
            adjustment["ema"] = 1.2
            adjustment["stop_mult"] = 0.8
            adjustment["profit_mult"] = 1.2
            adjustment["trail_step"] = 1.2
        elif market_state == "震荡":
            # 震荡市场下，缩短EMA周期，增加止损倍数
            adjustment["ema"] = 0.8
            adjustment["stop_mult"] = 1.2
            adjustment["profit_mult"] = 0.8
            adjustment["trail_step"] = 0.8
        elif market_state == "突破":
            # 突破市场下，保持EMA周期，增加止盈倍数
            adjustment["profit_mult"] = 1.5
            adjustment["volume"] = 1.2
        elif market_state == "反转":
            # 反转市场下，缩短EMA周期，增加止损倍数
            adjustment["ema"] = 0.7
            adjustment["stop_mult"] = 1.5
            adjustment["rsi"] = 1.2
            
        return adjustment

    def _calculate_volatility_score(self) -> float:
        """计算波动率得分"""
        # 1. ATR波动率
        atr_volatility = self.state_map.volatility_ratio
        
        # 2. 价格波动率
        if len(self._price_history) >= 20:
            price_std = np.std(self._price_history[-20:])
            price_mean = np.mean(self._price_history[-20:])
            price_volatility = price_std / price_mean if price_mean > 0 else 0
        else:
            price_volatility = 0
        
        # 3. 短期波动率
        if len(self._price_history) >= 5:
            short_std = np.std(self._price_history[-5:])
            short_mean = np.mean(self._price_history[-5:])
            short_volatility = short_std / short_mean if short_mean > 0 else 0
        else:
            short_volatility = 0
        
        # 4. 综合得分
        volatility_score = (
            atr_volatility * 0.4 +
            price_volatility * 0.3 +
            short_volatility * 0.3
        )
        
        return min(1.0, max(0.0, float(volatility_score)))

    def _calculate_trend_score(self) -> float:
        """计算趋势得分"""
        # 1. EMA趋势
        ema_trend = (
            self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow
        )
        ema_score = 1.0 if ema_trend else 0.0
        
        # 2. 趋势强度
        trend_strength = self.state_map.trend_strength
        
        # 3. 趋势持续性
        trend_duration = min(1.0, self.state_map.trend_duration / 20)
        
        # 4. 趋势质量
        trend_quality = self._calculate_trend_quality()
        
        # 5. 综合得分
        trend_score = (
            ema_score * 0.3 +
            trend_strength * 0.3 +
            trend_duration * 0.2 +
            trend_quality * 0.2
        )
        
        return min(1.0, max(0.0, trend_score))

    def _calculate_volume_score(self) -> float:
        """计算成交量得分"""
        # 1. 成交量比率
        volume_ratio = self.state_map.volume_ratio
        
        # 2. 成交量趋势
        if len(self._volume_history) >= 5:
            volume_trend = np.mean(np.diff(self._volume_history[-5:]))
            volume_trend_score = 1.0 if volume_trend > 0 else 0.0
        else:
            volume_trend_score = 0.0
        
        # 3. 成交量突破
        volume_breakout = self.state_map.is_volume_breakout
        breakout_score = 1.0 if volume_breakout else 0.0
        
        # 4. 综合得分
        volume_score = (
            min(1.0, volume_ratio) * 0.4 +
            volume_trend_score * 0.3 +
            breakout_score * 0.3
        )
        
        return min(1.0, max(0.0, volume_score))

    def _calculate_momentum_score(self) -> float:
        """计算动量得分"""
        # 1. RSI动量
        rsi_momentum = self.state_map.rsi_momentum
        rsi_score = min(1.0, max(0.0, (rsi_momentum + 1) / 2))
        
        # 2. 随机指标动量
        stoch_momentum = self.state_map.stoch_k - self.state_map.stoch_d
        stoch_score = min(1.0, max(0.0, (stoch_momentum + 100) / 200))
        
        # 3. 价格动量
        if len(self._price_history) >= 5:
            price_momentum = np.mean(np.diff(self._price_history[-5:]))
            price_score = min(1.0, max(0.0, float((price_momentum + 1) / 2)))
        else:
            price_score = 0.0
        
        # 4. 综合得分
        momentum_score = (
            rsi_score * 0.4 +
            stoch_score * 0.3 +
            price_score * 0.3
        )
        
        return min(1.0, max(0.0, momentum_score))

    def _calculate_trend_quality(self) -> float:
        """计算趋势质量"""
        # 1. 趋势一致性
        trend_consistency = self._calculate_trend_consistency()
        
        # 2. 趋势稳定性
        trend_stability = self._calculate_trend_stability()
        
        # 3. 趋势强度
        trend_strength = self.state_map.trend_strength
        
        # 4. 综合得分
        trend_quality = (
            trend_consistency * 0.4 +
            trend_stability * 0.3 +
            trend_strength * 0.3
        )
        
        return min(1.0, max(0.0, trend_quality))

    def _calculate_trend_consistency(self) -> float:
        """计算趋势一致性"""
        if len(self._price_history) < 10:
            return 0.0
        
        # 计算价格变化方向
        price_changes = np.diff(self._price_history[-10:])
        direction_changes = np.diff(np.sign(price_changes))
        
        # 计算一致性得分
        consistency = 1.0 - (np.sum(np.abs(direction_changes)) / len(direction_changes))
        
        return min(1.0, max(0.0, consistency))

    def _calculate_trend_stability(self) -> float:
        """计算趋势稳定性"""
        if len(self._price_history) < 20:
            return 0.0
        
        # 计算价格波动
        price_std = np.std(self._price_history[-20:])
        price_mean = np.mean(self._price_history[-20:])
        
        # 计算稳定性得分
        stability = 1.0 - (price_std / price_mean if price_mean > 0 else 0)
        
        return min(1.0, max(0.0, float(stability)))

    def _check_risk_control(self) -> bool:
        """检查风险控制（增强版）"""
        try:
            # 1. 检查市场风险
            if not self._check_market_risk():
                return False

            # 2. 检查持仓风险
            if not self._check_position_risk():
                return False

            # 3. 检查资金风险
            if not self._check_capital_risk():
                return False

            # 4. 检查波动率风险
            if not self._check_volatility_risk():
                return False

            # 5. 检查趋势风险
            if not self._check_trend_risk():
                return False

            return True

        except Exception as e:
            self.log(f"风险控制检查异常: {e}")
            return False

    def _check_market_risk(self) -> bool:
        """检查市场风险"""
        # 1. 检查市场状态
        market_state = self._get_market_state()
        if market_state == "high_volatility":
            return False
            
        # 2. 检查趋势强度
        if self.state_map.trend_strength < 0.3:
            return False
            
        # 3. 检查波动率
        if self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5:
            return False
            
        return True

    def _check_position_risk(self) -> bool:
        """检查持仓风险"""
        # 1. 检查持仓数量
        position = self.get_position(self.params_map.instrument_id)
        if position.net_position >= self.params_map.max_positions:
            return False
            
        # 2. 检查持仓盈亏
        if position.net_position > 0:
            current_price = self.tick.last_price if self.tick else 0
            if current_price > 0:
                position_profit = (current_price - self.entry_price) * position.net_position
                if position_profit < -self.params_map.max_loss_per_trade:
                    return False
                    
        # 3. 检查持仓时间
        if position.net_position > 0 and self.state_map.trend_duration > self.params_map.max_hold_period:
            return False
            
        return True

    def _check_capital_risk(self) -> bool:
        """检查资金风险"""
        try:
            # 1. 获取账户信息 - 使用正确的方法名
            account = None
            if hasattr(self, 'account'):
                account = self.account
            elif hasattr(self, 'get_account_info'):
                account = self.get_account_info()
            elif hasattr(self, 'account_info'):
                account = self.account_info

            if not account:
                # 如果没有账户信息，使用基础风险检查
                return self._check_basic_risk()

            # 2. 检查可用资金
            available_capital = getattr(account, 'available', 0)
            min_capital = getattr(self.params_map, 'min_available_capital', 1000)
            if available_capital < min_capital:
                return False

            # 3. 检查单笔风险
            current_price = self.tick.last_price if self.tick else getattr(self, 'entry_price', 0)
            if current_price <= 0:
                return False

            position_value = abs(self.position_size) * current_price
            max_position_ratio = getattr(self.params_map, 'max_position_ratio', 0.1)
            if position_value > available_capital * max_position_ratio:
                return False

            # 4. 检查总风险
            total_risk = self._calculate_total_risk()
            max_risk_ratio = getattr(self.params_map, 'max_risk_ratio', 0.05)
            if total_risk > available_capital * max_risk_ratio:
                return False

            return True

        except Exception as e:
            self.log(f"资金风险检查异常: {e}")
            return self._check_basic_risk()

    def _check_basic_risk(self) -> bool:
        """基础风险检查（当无法获取账户信息时使用）"""
        try:
            # 检查仓位大小是否合理
            if abs(self.position_size) > 1000:  # 假设最大仓位限制
                return False

            # 检查ATR是否正常
            if self.state_map.atr <= 0:
                return False

            # 检查价格是否正常
            current_price = self.tick.last_price if self.tick else 0
            if current_price <= 0:
                return False

            return True

        except Exception as e:
            self.log(f"基础风险检查异常: {e}")
            return False

    def _check_volatility_risk(self) -> bool:
        """检查波动率风险"""
        # 1. 检查ATR波动率
        if self.state_map.volatility_ratio > self.params_map.vol_threshold * 1.5:
            return False
            
        # 2. 检查价格波动率
        if len(self._price_history) >= 20:
            price_std = np.std(self._price_history[-20:])
            price_mean = np.mean(self._price_history[-20:])
            if price_std / price_mean > self.params_map.price_vol_threshold:
                return False
                
        # 3. 检查短期波动率
        if len(self._price_history) >= 5:
            short_std = np.std(self._price_history[-5:])
            short_mean = np.mean(self._price_history[-5:])
            if short_std / short_mean > self.params_map.short_vol_threshold:
                return False
                
        return True

    def _check_trend_risk(self) -> bool:
        """检查趋势风险"""
        # 1. 检查趋势质量
        trend_quality = self._calculate_trend_quality()
        if trend_quality < 0.5:
            return False
            
        # 2. 检查趋势一致性
        trend_consistency = self._calculate_trend_consistency()
        if trend_consistency < 0.6:
            return False
            
        # 3. 检查趋势稳定性
        trend_stability = self._calculate_trend_stability()
        if trend_stability < 0.5:
            return False
            
        return True

    def _calculate_trend_quality(self) -> float:
        """计算趋势质量"""
        try:
            if len(self._price_history) < 10:
                return 0.5

            # 计算价格趋势的线性度
            prices = np.array(self._price_history[-10:])
            x = np.arange(len(prices))
            correlation = np.corrcoef(x, prices)[0, 1]

            return abs(correlation) if not np.isnan(correlation) else 0.5

        except Exception:
            return 0.5

    def _calculate_trend_consistency(self) -> float:
        """计算趋势一致性"""
        try:
            if len(self._price_history) < 5:
                return 0.6

            # 计算价格变化的一致性
            price_changes = np.diff(self._price_history[-5:])
            positive_changes = np.sum(price_changes > 0)
            total_changes = len(price_changes)

            consistency = max(int(positive_changes), total_changes - int(positive_changes)) / total_changes
            return consistency

        except Exception:
            return 0.6

    def _calculate_trend_stability(self) -> float:
        """计算趋势稳定性"""
        try:
            if len(self._price_history) < 10:
                return 0.5

            # 计算价格波动的稳定性
            prices = np.array(self._price_history[-10:])
            price_std = np.std(prices)
            price_mean = np.mean(prices)

            stability = 1.0 - (price_std / price_mean if price_mean > 0 else 0)
            return max(0.0, min(1.0, float(stability)))

        except Exception:
            return 0.5

    def _calculate_total_risk(self) -> float:
        """计算总风险"""
        try:
            # 获取当前价格
            current_price = self.tick.last_price if self.tick else 0
            if current_price <= 0:
                return 0.0

            # 获取入场价格
            entry_price = getattr(self, 'entry_price', current_price)

            # 1. 计算持仓风险
            stop_loss = self.state_map.stop_loss if self.state_map.stop_loss > 0 else current_price * 0.95
            position_risk = abs(self.position_size) * abs(entry_price - stop_loss)

            # 2. 计算潜在风险
            potential_risk = abs(self.position_size) * abs(current_price - stop_loss)

            # 3. 计算波动率风险
            if self.current_params is not None:
                stop_mult = self.current_params["stop_mult"]
            else:
                stop_mult = self.params_map.stop_mult
            volatility_risk = self.state_map.atr * abs(self.position_size) * stop_mult

            # 4. 综合风险
            total_risk = max(position_risk, potential_risk, volatility_risk)

            return float(total_risk)

        except Exception as e:
            self.log(f"计算总风险异常: {e}")
            return 0.0

    def exec_signal(self):
        """交易信号执行（增强版）"""
        # 检查风险控制
        if not self._check_risk_control():
            self.buy_signal = False
            return
            
        current_time = time.time()
        
        # 检查订单超时
        if self.order_id is not None:
            if current_time - self.order_time > self.params_map.order_timeout:
                self.cancel_order(self.order_id)
                self.order_id = None
                self.order_time = 0
        
        position = self.get_position(self.params_map.instrument_id)
        self.position_size = position.net_position

        # 检查持仓限制
        if position.net_position >= self.params_map.max_positions:
            self.buy_signal = False

        # 获取市场状态和调整因子
        market_state = self._get_market_state()
        state_adjustment = self._get_state_adjustment(market_state)
        
        # 获取响应级别
        response_level = self._get_response_level()
        
        # 检查动态止损
        if self.tick and position.net_position > 0:
            current_price = self.tick.last_price
            
            # 计算当前盈亏
            current_profit = (current_price - self.entry_price) * self.position_size
            
            # 根据响应级别调整止损触发条件
            if response_level == 3:  # 激进
                stop_loss_buffer = self.state_map.atr * 0.2
            elif response_level == 2:  # 适中
                stop_loss_buffer = self.state_map.atr * 0.1
            else:  # 保守
                stop_loss_buffer = 0
            
            # 检查止损条件
            if current_price <= self.state_map.stop_loss + stop_loss_buffer:
                self.sell_signal = True
                self.sell_reason = "止损触发"
            
            # 检查止盈条件
            elif current_price >= self.state_map.take_profit:
                self.sell_signal = True
                self.sell_reason = "止盈触发"
            
            # 检查盈利回撤
            elif current_profit > 0 and current_profit < self.state_map.max_profit * (1 - self.params_map.profit_take_ratio):
                self.sell_signal = True
                self.sell_reason = "盈利回撤"
            
            # 检查趋势反转
            elif self._check_trend_reversal():
                self.sell_signal = True
                self.sell_reason = "趋势反转"
            
            # 检查波动率风险
            elif self._check_volatility_risk():
                self.sell_signal = True
                self.sell_reason = "波动率风险"

        # 卖出信号执行
        if position.net_position > 0 and self.sell_signal:
            if self.trading:
                # 获取最优执行价格
                sell_price = self._get_optimal_execution_price("sell", response_level)
                
                # 检查价格合理性
                if not self._validate_execution_price(sell_price, "sell"):
                    return
                
                # 执行卖出订单
                self.order_id = self.auto_close_position(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    price=sell_price,
                    volume=position.net_position,
                    order_direction="sell"
                )
                self.order_time = current_time
                
                # 重置状态
                self._reset_position_state()

        # 买入信号执行
        if self.buy_signal:
            if self.trading:
                # 获取最优执行价格
                buy_price = self._get_optimal_execution_price("buy", response_level)
                
                # 检查价格合理性
                if not self._validate_execution_price(buy_price, "buy"):
                    return
                
                # 执行买入订单
                self.order_id = self.send_order(
                    exchange=self.params_map.exchange,
                    instrument_id=self.params_map.instrument_id,
                    volume=self.params_map.order_volume,
                    price=buy_price,
                    order_direction="buy"
                )
                self.order_time = current_time

    def _get_optimal_execution_price(self, direction: str, response_level: int) -> float:
        """获取最优执行价格"""
        if not self.tick:
            return 0
            
        # 获取市场深度信息
        bid_price = self.tick.bid_price
        ask_price = self.tick.ask_price
        last_price = self.tick.last_price
        
        # 计算价格滑点
        price_slip = self._calculate_price_slip(direction, response_level)
        
        if direction == "buy":
            # 买入价格计算
            if response_level == 3:  # 激进
                return ask_price + price_slip  # 使用卖一价加滑点
            elif response_level == 2:  # 适中
                return last_price + price_slip  # 使用最新价加滑点
            else:  # 保守
                return bid_price + price_slip  # 使用买一价加滑点
        else:
            # 卖出价格计算
            if response_level == 3:  # 激进
                return bid_price - price_slip  # 使用买一价减滑点
            elif response_level == 2:  # 适中
                return last_price - price_slip  # 使用最新价减滑点
            else:  # 保守
                return ask_price - price_slip  # 使用卖一价减滑点

    def _calculate_price_slip(self, direction: str, response_level: int) -> float:
        """计算价格滑点"""
        # 基础滑点
        base_slip = self.state_map.atr * 0.1
        
        # 根据响应级别调整
        if response_level == 3:  # 激进
            slip_mult = 0.5
        elif response_level == 2:  # 适中
            slip_mult = 1.0
        else:  # 保守
            slip_mult = 1.5
            
        # 根据市场状态调整
        market_state = self._get_market_state()
        if market_state == "high_volatility":
            slip_mult *= 1.5
        elif market_state == "low_volatility":
            slip_mult *= 0.8
            
        # 根据成交量调整
        if self.state_map.volume_ratio > 2.0:
            slip_mult *= 1.2
        elif self.state_map.volume_ratio < 0.5:
            slip_mult *= 0.8
            
        return base_slip * slip_mult

    def _validate_execution_price(self, price: float, direction: str) -> bool:
        """验证执行价格合理性"""
        if not self.tick:
            return False
            
        # 获取当前价格信息
        bid_price = self.tick.bid_price
        ask_price = self.tick.ask_price
        last_price = self.tick.last_price
        
        # 计算价格偏差
        if direction == "buy":
            price_deviation = abs(price - ask_price) / ask_price
            max_deviation = self.state_map.atr * 0.2 / ask_price
        else:
            price_deviation = abs(price - bid_price) / bid_price
            max_deviation = self.state_map.atr * 0.2 / bid_price
            
        # 检查价格偏差
        if price_deviation > max_deviation:
            return False
            
        # 检查价格合理性
        if direction == "buy":
            if price > ask_price * 1.1 or price < bid_price:
                return False
        else:
            if price < bid_price * 0.9 or price > ask_price:
                return False
                
        return True

    def _check_trend_reversal(self) -> bool:
        """检查趋势反转"""
        # 检查EMA排列
        ema_reversal = (
            self.state_map.ema_fast < self.state_map.ema_mid and
            self.state_map.ema_mid < self.state_map.ema_slow
        )
        
        # 检查RSI趋势
        rsi_reversal = (
            self.state_map.rsi < self.params_map.rsi_lower and
            self.state_map.rsi_momentum < 0
        )
        
        # 检查成交量趋势
        volume_reversal = (
            self.state_map.volume_ratio < 0.8 and
            self.state_map.volume_factor < 0
        )
        
        # 综合判断
        return (
            ema_reversal and
            (rsi_reversal or volume_reversal) and
            self.state_map.trend_strength < 0.3
        )

    def _reset_position_state(self):
        """重置持仓状态"""
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0
        self.sell_reason = None

    def _reset_state(self):
        """完全重置策略状态"""
        self.signal_price = 0
        self.buy_signal = False
        self.sell_signal = False
        self.tick = None
        self._price_history = []
        self._volume_history = []
        self._rsi_history = []
        self.trend_count = 0
        
        # 重置指标缓存
        self._indicator_cache = {
            'ema': {'last_update': 0, 'value': None},
            'rsi': {'last_update': 0, 'value': None},
            'atr': {'last_update': 0, 'value': None},
            'volume': {'last_update': 0, 'value': None},
            'momentum': {'last_update': 0, 'value': None}
        }
        
        # 重置动态止盈止损相关变量
        self.entry_price = 0
        self.position_size = 0
        self.is_trailing = False
        self.state_map.highest_price = 0
        self.state_map.lowest_price = 0
        self.state_map.current_profit = 0
        self.state_map.max_profit = 0

    def _validate_signal(self, base_signals: dict, market_state: str) -> bool:
        """多级信号验证机制"""
        # 1. 基础信号验证
        if not self._validate_base_signals(base_signals):
            return False
            
        # 2. 市场状态验证
        if not self._validate_market_state(market_state):
            return False
            
        # 3. 趋势强度验证
        if not self._validate_trend_strength():
            return False
            
        # 4. 波动率验证
        if not self._validate_volatility():
            return False
            
        # 5. 成交量验证
        if not self._validate_volume():
            return False
            
        return True

    def _validate_base_signals(self, base_signals: dict) -> bool:
        """验证基础信号"""
        # EMA排列验证
        ema_valid = (
            self.state_map.ema_fast > self.state_map.ema_mid > self.state_map.ema_slow and
            (self.state_map.ema_fast - self.state_map.ema_mid) / self.state_map.ema_mid > 0.001 and
            (self.state_map.ema_mid - self.state_map.ema_slow) / self.state_map.ema_slow > 0.001
        )
        
        # RSI验证
        rsi_valid = (
            self.state_map.rsi > self.params_map.rsi_lower and
            self.state_map.rsi < self.params_map.rsi_upper and
            self.state_map.rsi_momentum > 0
        )
        
        # 成交量验证
        volume_valid = (
            self.state_map.volume_ratio > self.params_map.volume_breakout_mult or
            (self.state_map.volume_ratio > 1.2 and self.state_map.trend_strength > 0.4)
        )
        
        return ema_valid and (rsi_valid or volume_valid)

    def _validate_market_state(self, market_state: str) -> bool:
        """验证市场状态"""
        if market_state == "high_volatility":
            # 高波动时需要更强的趋势确认
            return self.state_map.trend_strength > 0.5
        elif market_state == "low_volatility":
            # 低波动时需要更严格的信号确认
            return (
                self.state_map.volume_ratio > 1.5 and
                self.state_map.trend_strength > 0.4
            )
        elif market_state == "trending":
            # 趋势市场可以适当放宽条件
            return self.state_map.trend_strength > 0.3
        else:  # choppy
            # 震荡市场需要更严格的确认
            return (
                self.state_map.volume_ratio > 1.8 and
                self.state_map.trend_strength > 0.5
            )

    def _validate_trend_strength(self) -> bool:
        """验证趋势强度"""
        # 基础趋势强度要求
        if self.state_map.trend_strength < 0.3:
            return False
            
        # 趋势持续性要求
        if self.state_map.trend_duration < self.params_map.trend_duration_min:
            return False
            
        # 趋势质量要求
        if self.state_map.trend_quality < 0.6:
            return False
            
        return True

    def _validate_volatility(self) -> bool:
        """验证波动率"""
        # 波动率范围检查
        if self.state_map.volatility_ratio > self.params_map.vol_threshold * 2.0:
            return False
            
        # 波动率趋势检查
        if self.state_map.volatility_momentum > 0.5:
            return False
            
        return True

    def _validate_volume(self) -> bool:
        """验证成交量"""
        # 成交量突破检查
        if self.state_map.volume_ratio < 1.2:
            return False
            
        # 成交量趋势检查
        if self.state_map.volume_momentum < 0:
            return False
            
        # 成交量持续性检查
        if self.state_map.volume_persistence < 0.6:
            return False
            
        return True


class PerformanceMonitor:
    """性能监控器"""
    def __init__(self):
        self.trade_history = []
        self.performance_metrics = {
            "win_rate": 0.0,
            "profit_factor": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "avg_trade_duration": 0.0,
            "avg_profit_per_trade": 0.0
        }
        self.market_conditions = {
            "high_volatility": 0,
            "low_volatility": 0,
            "trending": 0,
            "choppy": 0
        }
        self.parameter_history = {
            "ema": [],
            "stop_mult": [],
            "profit_mult": [],
            "trail_step": []
        }

    def update_trade_history(self, trade_data: dict):
        """更新交易历史"""
        self.trade_history.append(trade_data)
        self._calculate_metrics()

    def _calculate_metrics(self):
        """计算性能指标"""
        if not self.trade_history:
            return

        # 计算胜率
        winning_trades = sum(1 for trade in self.trade_history if trade["profit"] > 0)
        self.performance_metrics["win_rate"] = winning_trades / len(self.trade_history)

        # 计算盈亏比
        total_profit = sum(trade["profit"] for trade in self.trade_history if trade["profit"] > 0)
        total_loss = abs(sum(trade["profit"] for trade in self.trade_history if trade["profit"] < 0))
        self.performance_metrics["profit_factor"] = total_profit / total_loss if total_loss > 0 else float('inf')

        # 计算最大回撤
        cumulative_returns = np.cumsum([trade["profit"] for trade in self.trade_history])
        rolling_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = rolling_max - cumulative_returns
        self.performance_metrics["max_drawdown"] = np.max(drawdowns) if len(drawdowns) > 0 else 0

        # 计算夏普比率
        returns = np.array([trade["profit"] for trade in self.trade_history])
        self.performance_metrics["sharpe_ratio"] = float(np.mean(returns) / np.std(returns)) if len(returns) > 1 else 0.0

        # 计算平均持仓时间
        durations = [trade["duration"] for trade in self.trade_history]
        self.performance_metrics["avg_trade_duration"] = float(np.mean(durations)) if durations else 0.0

        # 计算平均每笔交易盈利
        self.performance_metrics["avg_profit_per_trade"] = float(np.mean(returns)) if len(returns) > 0 else 0.0

    def update_market_conditions(self, market_state: str):
        """更新市场状态统计"""
        if market_state in self.market_conditions:
            self.market_conditions[market_state] += 1

    def update_parameter_history(self, params: dict):
        """更新参数历史"""
        for key in self.parameter_history:
            if key in params:
                self.parameter_history[key].append(params[key])

    def get_performance_report(self) -> dict:
        """获取性能报告"""
        return {
            "metrics": self.performance_metrics,
            "market_conditions": self.market_conditions,
            "parameter_history": self.parameter_history
        }

# 移除不存在的导入，这些功能已经集成到主策略类中

class RiskControlSystem:
    """风险控制系统"""
    def __init__(self):
        self.risk_limits = {
            "max_position_size": 5,
            "max_daily_loss": 0.05,
            "max_position_risk": 0.02,
            "max_total_risk": 0.1,
            "min_win_rate": 0.4,
            "max_drawdown": 0.15
        }
        self.risk_metrics = {
            "current_drawdown": 0.0,
            "daily_pnl": 0.0,
            "position_risk": 0.0,
            "total_risk": 0.0,
            "win_rate": 0.0
        }
        self.risk_factors = {
            "market_volatility": 1.0,
            "trend_strength": 1.0,
            "volume_ratio": 1.0,
            "position_size": 1.0
        }

    def check_risk_limits(self) -> bool:
        """检查风险限制"""
        # 检查每日损失
        if self.risk_metrics["daily_pnl"] < -self.risk_limits["max_daily_loss"]:
            return False
            
        # 检查持仓风险
        if self.risk_metrics["position_risk"] > self.risk_limits["max_position_risk"]:
            return False
            
        # 检查总风险
        if self.risk_metrics["total_risk"] > self.risk_limits["max_total_risk"]:
            return False
            
        # 检查胜率
        if self.risk_metrics["win_rate"] < self.risk_limits["min_win_rate"]:
            return False
            
        # 检查回撤
        if self.risk_metrics["current_drawdown"] > self.risk_limits["max_drawdown"]:
            return False
            
        return True

    def calculate_risk_factors(self, market_state: str, indicators: dict):
        """计算风险因子"""
        # 市场波动率因子
        self.risk_factors["market_volatility"] = self._calculate_volatility_factor(
            indicators.get("atr_ratio", 1.0),
            indicators.get("price_volatility", 0.0)
        )
        # 趋势强度因子
        self.risk_factors["trend_strength"] = self._calculate_trend_factor(
            indicators.get("trend_strength", 0.0),
            indicators.get("trend_quality", 0.0)
        )
        # 成交量因子
        self.risk_factors["volume_ratio"] = self._calculate_volume_factor(
            indicators.get("volume_ratio", 1.0),
            indicators.get("volume_trend", 0.0)
        )
        # 持仓规模因子
        self.risk_factors["position_size"] = self._calculate_position_factor(
            indicators.get("position_size", 0),
            indicators.get("max_position_size", 0)
        )

    def _calculate_volatility_factor(self, atr_ratio: float, price_volatility: float) -> float:
        """计算波动率因子"""
        return 1.0 / (1.0 + atr_ratio + price_volatility)

    def _calculate_trend_factor(self, trend_strength: float, trend_quality: float) -> float:
        """计算趋势因子"""
        return (trend_strength + trend_quality) / 2

    def _calculate_volume_factor(self, volume_ratio: float, volume_trend: float) -> float:
        """计算成交量因子"""
        return min(1.0, volume_ratio) * (1.0 + volume_trend)

    def _calculate_position_factor(self, position_size: int, max_position: int) -> float:
        """计算持仓规模因子"""
        return 1.0 - (position_size / max_position)

    def get_risk_adjustment(self) -> float:
        """获取风险调整因子"""
        return float(np.prod(list(self.risk_factors.values())))

    def update_risk_metrics(self, trade_data: dict):
        """更新风险指标"""
        # 更新每日盈亏
        self.risk_metrics["daily_pnl"] += trade_data["profit"]
        
        # 更新持仓风险
        if trade_data["direction"] == "buy":
            self.risk_metrics["position_risk"] = (
                trade_data["volume"] * trade_data["price"] * self.risk_limits["max_position_risk"]
            )
        
        # 更新总风险
        self.risk_metrics["total_risk"] = (
            self.risk_metrics["position_risk"] * self.risk_factors["market_volatility"]
        )
        
        # 更新回撤
        if trade_data["profit"] < 0:
            self.risk_metrics["current_drawdown"] = max(
                self.risk_metrics["current_drawdown"],
                abs(trade_data["profit"])
            )

class SignalManager:
    """信号管理器"""
    def __init__(self):
        self.signal_cache = {
            "trend": {"value": None, "timestamp": 0, "strength": 0},
            "rsi": {"value": None, "timestamp": 0, "strength": 0},
            "volume": {"value": None, "timestamp": 0, "strength": 0},
            "momentum": {"value": None, "timestamp": 0, "strength": 0}
        }
        self.signal_queue = {
            "high": [],  # 强信号队列
            "medium": [],  # 中等信号队列
            "low": []  # 弱信号队列
        }
        self.market_state_weights = {
            "trending": {
                "trend": 0.4,
                "rsi": 0.2,
                "volume": 0.2,
                "momentum": 0.2
            },
            "choppy": {
                "trend": 0.2,
                "rsi": 0.4,
                "volume": 0.3,
                "momentum": 0.1
            },
            "high_volatility": {
                "trend": 0.3,
                "rsi": 0.3,
                "volume": 0.2,
                "momentum": 0.2
            }
        }
        self.response_levels = {
            "aggressive": 3,
            "moderate": 2,
            "conservative": 1
        }
        self.signal_history = []
        self.max_history_length = 100

    def update_signal_cache(self, signal_type: str, value, strength: float) -> None:
        """更新信号缓存"""
        self.signal_cache[signal_type] = {
            "value": value,
            "timestamp": time.time(),
            "strength": strength
        }

    def calculate_signal_strength(self, market_state: str) -> float:
        """计算综合信号强度"""
        if market_state not in self.market_state_weights:
            return 0.0
            
        weights = self.market_state_weights[market_state]
        total_strength = 0.0
        
        for signal_type, weight in weights.items():
            if self.signal_cache[signal_type]["value"] is not None:
                total_strength += self.signal_cache[signal_type]["strength"] * weight
                
        return total_strength

    def queue_signal(self, signal_type: str, strength: float, priority: str) -> None:
        """将信号加入队列"""
        if priority not in self.signal_queue:
            return
            
        signal = {
            "type": signal_type,
            "strength": strength,
            "timestamp": time.time()
        }
        
        self.signal_queue[priority].append(signal)
        
        # 保持队列长度
        if len(self.signal_queue[priority]) > 10:
            self.signal_queue[priority].pop(0)

    def get_next_signal(self, market_state: str) -> Optional[dict]:
        """获取下一个待处理信号"""
        # 首先检查高优先级队列
        if self.signal_queue["high"]:
            return self.signal_queue["high"].pop(0)

        # 检查中等优先级队列
        if self.signal_queue["medium"]:
            signal = self.signal_queue["medium"][0]
            # 如果信号强度足够且市场状态合适，则返回
            if signal["strength"] > 0.7 and market_state != "high_volatility":
                return self.signal_queue["medium"].pop(0)

        return None

    def update_response_level(self, market_state: str, volatility: float) -> int:
        """更新响应级别"""
        if market_state == "high_volatility" or volatility > 0.7:
            return self.response_levels["conservative"]
        elif market_state == "trending" and volatility < 0.3:
            return self.response_levels["aggressive"]
        else:
            return self.response_levels["moderate"]

    def confirm_signal(self, signal: dict, market_state: str, response_level: int) -> bool:
        """确认信号"""
        if not signal:
            return False
            
        # 获取当前信号强度
        current_strength = self.calculate_signal_strength(market_state)
        
        # 根据响应级别设置确认阈值
        if response_level == self.response_levels["aggressive"]:
            return current_strength > 0.5
        elif response_level == self.response_levels["moderate"]:
            return current_strength > 0.6
        else:  # conservative
            return current_strength > 0.7

    def add_signal_history(self, signal: dict, confirmed: bool) -> None:
        """添加信号历史"""
        self.signal_history.append({
            "signal": signal,
            "confirmed": confirmed,
            "timestamp": time.time()
        })
        
        # 保持历史长度
        if len(self.signal_history) > self.max_history_length:
            self.signal_history.pop(0)

    def get_signal_performance(self) -> dict:
        """获取信号性能统计"""
        if not self.signal_history:
            return {}
            
        total_signals = len(self.signal_history)
        confirmed_signals = sum(1 for s in self.signal_history if s["confirmed"])
        
        return {
            "total_signals": total_signals,
            "confirmed_signals": confirmed_signals,
            "confirmation_rate": confirmed_signals / total_signals if total_signals > 0 else 0
        }

class DynamicStopManager:
    """动态止盈止损管理器"""
    def __init__(self):
        self.position_parts = {
            "quick": {"ratio": 0.5, "target": 1.5},  # 快速止盈部分
            "medium": {"ratio": 0.3, "target": 2.0},  # 中等止盈部分
            "slow": {"ratio": 0.2, "target": None}  # 追踪止损部分
        }
        self.base_stop_mult = 1.2
        self.base_profit_mult = 1.5
        self.trailing_step = 0.3
        self.stop_history = []
        self.max_history_length = 50
        self.performance_metrics = {
            "avg_profit": 0.0,
            "avg_loss": 0.0,
            "win_rate": 0.0
        }

    def calculate_stop_mult(self, atr_short: float, atr_long: float) -> float:
        """计算动态止损倍数"""
        volatility_ratio = atr_short / atr_long if atr_long > 0 else 1.0
        return self.base_stop_mult * (1 + 0.5 * volatility_ratio)

    def calculate_profit_mult(self, trend_strength: float, volatility: float) -> float:
        """计算动态止盈倍数"""
        if trend_strength > 0.6:
            base_mult = 2.0 + 0.5 * trend_strength
        else:
            base_mult = self.base_profit_mult
            
        # 高波动时降低止盈倍数
        if volatility > 0.7:
            base_mult *= 0.8
            
        return base_mult

    def update_trailing_stop(self, current_price: float, highest_price: float, 
                           atr: float, trend_strength: float) -> float:
        """更新追踪止损"""
        if current_price <= highest_price:
            return highest_price - atr * self.trailing_step
            
        # 在强趋势中加快追踪速度
        if trend_strength > 0.7:
            step_mult = 1.3
        else:
            step_mult = 1.0
            
        return current_price - atr * self.trailing_step * step_mult

    def calculate_partial_take_profit(self, entry_price: float, atr: float, 
                                    trend_strength: float, volatility: float) -> dict:
        """计算分步止盈目标"""
        profit_mult = self.calculate_profit_mult(trend_strength, volatility)
        
        targets = {}
        for part_name, part_config in self.position_parts.items():
            if part_config["target"] is not None:
                targets[part_name] = {
                    "price": entry_price + atr * part_config["target"],
                    "ratio": part_config["ratio"]
                }
            else:
                # 对于追踪止损部分，使用动态止盈倍数
                targets[part_name] = {
                    "price": entry_price + atr * profit_mult,
                    "ratio": part_config["ratio"]
                }
                
        return targets

    def update_performance_metrics(self, trade_data: dict) -> None:
        """更新性能指标"""
        self.stop_history.append(trade_data)
        
        if len(self.stop_history) > self.max_history_length:
            self.stop_history.pop(0)
            
        # 计算平均盈利和亏损
        profits = [t["profit"] for t in self.stop_history if t["profit"] > 0]
        losses = [t["profit"] for t in self.stop_history if t["profit"] < 0]
        
        self.performance_metrics["avg_profit"] = float(np.mean(profits)) if profits else 0.0
        self.performance_metrics["avg_loss"] = float(np.mean(losses)) if losses else 0.0
        
        # 计算胜率
        winning_trades = sum(1 for t in self.stop_history if t["profit"] > 0)
        self.performance_metrics["win_rate"] = winning_trades / len(self.stop_history) if self.stop_history else 0

    def adjust_parameters(self) -> None:
        """根据性能调整参数"""
        if len(self.stop_history) < 10:
            return
            
        # 如果胜率低于40%，收紧止损
        if self.performance_metrics["win_rate"] < 0.4:
            self.base_stop_mult *= 0.9
            self.trailing_step *= 0.9
            
        # 如果平均盈利/亏损比小于1.5，调整止盈倍数
        profit_ratio = abs(self.performance_metrics["avg_profit"] / self.performance_metrics["avg_loss"]) if self.performance_metrics["avg_loss"] != 0 else 0
        if profit_ratio < 1.5:
            self.base_profit_mult *= 1.1
            
        # 确保参数在合理范围内
        self.base_stop_mult = max(0.8, min(2.0, self.base_stop_mult))
        self.base_profit_mult = max(1.2, min(3.0, self.base_profit_mult))
        self.trailing_step = max(0.2, min(1.0, self.trailing_step))

    def get_stop_performance(self) -> dict:
        """获取止损性能统计"""
        return {
            "total_trades": len(self.stop_history),
            "win_rate": self.performance_metrics["win_rate"],
            "profit_factor": abs(self.performance_metrics["avg_profit"] / self.performance_metrics["avg_loss"]) if self.performance_metrics["avg_loss"] != 0 else 0,
            "avg_profit": self.performance_metrics["avg_profit"],
            "avg_loss": self.performance_metrics["avg_loss"]
        }

class MarketStateClassifier:
    """市场状态分类器"""
    def __init__(self):
        self.state_params = {
            "strong_trend": {
                "ema": [3, 8, 15],
                "stop_mult": 0.8,
                "profit_mult": 2.5,
                "rsi_upper": 75,
                "rsi_lower": 25,
                "volume_threshold": 1.8
            },
            "weak_trend": {
                "ema": [5, 13, 26],
                "stop_mult": 1.0,
                "profit_mult": 2.0,
                "rsi_upper": 70,
                "rsi_lower": 30,
                "volume_threshold": 1.5
            },
            "high_vol_choppy": {
                "ema": [8, 20, 40],
                "stop_mult": 1.5,
                "profit_mult": 1.2,
                "rsi_upper": 80,
                "rsi_lower": 20,
                "volume_threshold": 2.0
            },
            "low_vol_choppy": {
                "ema": [10, 25, 50],
                "stop_mult": 1.2,
                "profit_mult": 1.5,
                "rsi_upper": 65,
                "rsi_lower": 35,
                "volume_threshold": 1.2
            }
        }
        
        self.state_weights = {
            "trend_strength": 0.4,
            "volatility": 0.3,
            "volume": 0.2,
            "momentum": 0.1
        }
        
        self.state_thresholds = {
            "strong_trend": 0.8,
            "weak_trend": 0.6,
            "high_vol_choppy": 0.7,
            "low_vol_choppy": 0.5
        }
        
        self.state_history = []
        self.max_history_length = 100
        self.smoothing_factor = 0.2

    def classify_market_state(self, indicators: dict) -> str:
        """分类市场状态"""
        # 计算各维度得分
        trend_score = self._calculate_trend_score(indicators)
        vol_score = self._calculate_volatility_score(indicators)
        volume_score = self._calculate_volume_score(indicators)
        momentum_score = self._calculate_momentum_score(indicators)
        
        # 计算综合得分
        total_score = (
            self.state_weights["trend_strength"] * trend_score +
            self.state_weights["volatility"] * vol_score +
            self.state_weights["volume"] * volume_score +
            self.state_weights["momentum"] * momentum_score
        )
        
        # 确定市场状态
        if total_score >= self.state_thresholds["strong_trend"]:
            state = "strong_trend"
        elif total_score >= self.state_thresholds["weak_trend"]:
            state = "weak_trend"
        elif total_score >= self.state_thresholds["high_vol_choppy"]:
            state = "high_vol_choppy"
        else:
            state = "low_vol_choppy"
            
        # 更新状态历史
        self._update_state_history(state)
        
        return state

    def get_state_params(self, state: str) -> dict:
        """获取状态对应的参数"""
        if state not in self.state_params:
            return self.state_params["weak_trend"]  # 默认返回弱趋势参数
            
        return self.state_params[state]

    def _calculate_trend_score(self, indicators: dict) -> float:
        """计算趋势得分"""
        trend_strength = indicators.get("trend_strength", 0)
        trend_quality = indicators.get("trend_quality", 0)
        trend_consistency = indicators.get("trend_consistency", 0)
        
        return (trend_strength * 0.5 + trend_quality * 0.3 + trend_consistency * 0.2)

    def _calculate_volatility_score(self, indicators: dict) -> float:
        """计算波动率得分"""
        atr_ratio = indicators.get("atr_ratio", 1.0)
        price_volatility = indicators.get("price_volatility", 0)
        
        return min(1.0, (atr_ratio + price_volatility) / 2)

    def _calculate_volume_score(self, indicators: dict) -> float:
        """计算成交量得分"""
        volume_ratio = indicators.get("volume_ratio", 1.0)
        volume_trend = indicators.get("volume_trend", 0)
        
        return min(1.0, (volume_ratio + volume_trend) / 2)

    def _calculate_momentum_score(self, indicators: dict) -> float:
        """计算动量得分"""
        rsi = indicators.get("rsi", 50)
        momentum = indicators.get("momentum", 0)
        
        # 将RSI和动量标准化到0-1范围
        rsi_score = abs(rsi - 50) / 50
        momentum_score = min(1.0, abs(momentum))
        
        return (rsi_score * 0.6 + momentum_score * 0.4)

    def _update_state_history(self, new_state: str) -> None:
        """更新状态历史"""
        self.state_history.append(new_state)
        if len(self.state_history) > self.max_history_length:
            self.state_history.pop(0)

    def get_state_transition_probability(self, from_state: str, to_state: str) -> float:
        """获取状态转移概率"""
        if not self.state_history:
            return 0.0
            
        transitions = 0
        total = 0
        
        for i in range(len(self.state_history) - 1):
            if self.state_history[i] == from_state:
                total += 1
                if self.state_history[i + 1] == to_state:
                    transitions += 1
                    
        return transitions / total if total > 0 else 0.0

    def smooth_state_transition(self, current_params: dict, target_params: dict) -> dict:
        """平滑状态转换"""
        smoothed = {}
        for key in current_params:
            if key in target_params:
                if isinstance(current_params[key], list):
                    smoothed[key] = [
                        current_params[key][i] * (1 - self.smoothing_factor) +
                        target_params[key][i] * self.smoothing_factor
                        for i in range(len(current_params[key]))
                    ]
                else:
                    smoothed[key] = (
                        current_params[key] * (1 - self.smoothing_factor) +
                        target_params[key] * self.smoothing_factor
                    )
            else:
                smoothed[key] = current_params[key]
                
        return smoothed

# 移除不存在的导入，这些功能已经集成到主策略类中
