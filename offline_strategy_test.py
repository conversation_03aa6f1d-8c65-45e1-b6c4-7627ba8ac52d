#!/usr/bin/env python3
"""
Volume SuperTrend AI 离线测试脚本
测试策略的核心功能，不依赖网络连接

此脚本模拟市场数据，测试Volume SuperTrend AI算法的各个组件
"""

import sys
import time
import numpy as np
from collections import deque
import threading
from datetime import datetime

# 模拟数据类
class MockKLineData:
    def __init__(self, timestamp, open_price, high, low, close, volume):
        self.timestamp = timestamp
        self.open = open_price
        self.high = high
        self.low = low
        self.close = close
        self.volume = volume

class MockTickData:
    def __init__(self, timestamp, last_price, volume):
        self.timestamp = timestamp
        self.last_price = last_price
        self.volume = volume

def test_volume_supertrend_ai_offline():
    """离线测试Volume SuperTrend AI核心功能"""
    print("=" * 60)
    print("Volume SuperTrend AI 离线功能测试")
    print("=" * 60)
    
    try:
        # 导入独立的Volume SuperTrend AI实现
        sys.path.append('.')
        from volume_supertrend_ai_standalone import TradingSignalGenerator
        
        # 创建信号生成器
        signal_generator = TradingSignalGenerator()
        print("✓ Volume SuperTrend AI 信号生成器创建成功")
        
        # 生成模拟市场数据
        print("\n--- 生成模拟市场数据 ---")
        market_data = generate_realistic_market_data(200)
        print(f"✓ 生成了 {len(market_data)} 个市场数据点")
        
        # 测试数据更新和信号生成
        print("\n--- 测试信号生成 ---")
        signals = []
        
        for i, data in enumerate(market_data):
            # 更新市场数据
            signal_generator.update_market_data(
                price=data['price'],
                volume=data['volume'],
                high=data['high'],
                low=data['low']
            )
            
            # 每10个数据点生成一次信号
            if i % 10 == 0 and i > 30:
                signal = signal_generator.generate_trading_signal(
                    current_price=data['price'],
                    risk_tolerance=0.6
                )
                signals.append(signal)
                
                print(f"数据点 {i:3d}: 价格={data['price']:7.2f}, "
                      f"信号={signal['action']:4s}, "
                      f"置信度={signal['signal_data']['confidence']:.3f}, "
                      f"AI信号={signal['signal_data']['ai_signal']}")
        
        print(f"\n✓ 生成了 {len(signals)} 个交易信号")
        
        # 训练AI模型
        print("\n--- 测试AI模型训练 ---")
        if signal_generator.train_ai_model():
            print("✓ AI模型训练成功")
        else:
            print("⚠ AI模型训练失败，使用简单规则")
        
        # 测试训练后的信号质量
        print("\n--- 测试训练后信号质量 ---")
        post_training_signals = []
        
        # 使用最后50个数据点测试
        for i, data in enumerate(market_data[-50:], len(market_data)-50):
            signal = signal_generator.generate_trading_signal(
                current_price=data['price'],
                risk_tolerance=0.7
            )
            
            if signal['action'] != 'hold':
                post_training_signals.append(signal)
                print(f"训练后信号 {len(post_training_signals):2d}: "
                      f"价格={data['price']:7.2f}, "
                      f"动作={signal['action']:4s}, "
                      f"置信度={signal['signal_data']['confidence']:.3f}")
        
        # 性能分析
        print("\n--- 性能分析 ---")
        performance = signal_generator.get_performance_summary()
        
        print("信号统计:")
        for key, value in performance.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.3f}")
            else:
                print(f"  {key}: {value}")
        
        # 信号质量分析
        if signals:
            buy_signals = [s for s in signals if s['action'] == 'buy']
            sell_signals = [s for s in signals if s['action'] == 'sell']
            hold_signals = [s for s in signals if s['action'] == 'hold']
            
            print(f"\n信号分布:")
            print(f"  买入信号: {len(buy_signals)} ({len(buy_signals)/len(signals)*100:.1f}%)")
            print(f"  卖出信号: {len(sell_signals)} ({len(sell_signals)/len(signals)*100:.1f}%)")
            print(f"  观望信号: {len(hold_signals)} ({len(hold_signals)/len(signals)*100:.1f}%)")
            
            if buy_signals or sell_signals:
                active_signals = buy_signals + sell_signals
                avg_confidence = np.mean([s['signal_data']['confidence'] for s in active_signals])
                print(f"  平均置信度: {avg_confidence:.3f}")
        
        print("\n✓ Volume SuperTrend AI 离线测试完成")
        return True
        
    except Exception as e:
        print(f"✗ 离线测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def generate_realistic_market_data(num_points):
    """生成真实的市场数据"""
    np.random.seed(42)
    
    data = []
    base_price = 100.0
    current_price = base_price
    
    # 模拟不同的市场阶段
    phases = [
        {"name": "上升趋势", "trend": 0.02, "volatility": 0.8, "length": 60},
        {"name": "震荡整理", "trend": 0.0, "volatility": 1.2, "length": 40},
        {"name": "下降趋势", "trend": -0.015, "volatility": 1.0, "length": 50},
        {"name": "反弹", "trend": 0.025, "volatility": 1.5, "length": 50}
    ]
    
    point_index = 0
    
    for phase in phases:
        if point_index >= num_points:
            break
            
        print(f"生成 {phase['name']} 数据...")
        
        for i in range(min(phase['length'], num_points - point_index)):
            # 基础趋势
            trend_component = phase['trend'] * (i / 10)
            
            # 随机波动
            volatility_component = np.random.normal(0, phase['volatility'] * 0.5)
            
            # 计算新价格
            price_change = trend_component + volatility_component
            current_price = max(current_price + price_change, 1.0)  # 确保价格为正
            
            # 生成高低价
            daily_range = current_price * 0.02 * phase['volatility']
            high = current_price + np.random.uniform(0, daily_range)
            low = current_price - np.random.uniform(0, daily_range)
            
            # 确保价格关系正确
            high = max(high, current_price)
            low = min(low, current_price)
            
            # 生成成交量
            base_volume = 1000
            volume_trend = 1.0
            
            # 价格变化大时成交量增加
            if abs(price_change) > 1.0:
                volume_trend = 1.5
            
            # 趋势强烈时成交量增加
            if abs(phase['trend']) > 0.01:
                volume_trend *= 1.3
            
            volume = max(100, int(base_volume * volume_trend * np.random.uniform(0.5, 2.0)))
            
            data.append({
                'timestamp': point_index,
                'price': round(current_price, 2),
                'high': round(high, 2),
                'low': round(low, 2),
                'volume': volume,
                'phase': phase['name']
            })
            
            point_index += 1
    
    return data

def test_strategy_resilience():
    """测试策略的鲁棒性"""
    print("\n" + "=" * 60)
    print("策略鲁棒性测试")
    print("=" * 60)
    
    try:
        sys.path.append('.')
        from volume_supertrend_ai_standalone import TradingSignalGenerator
        
        signal_generator = TradingSignalGenerator()
        
        # 测试极端市场条件
        extreme_scenarios = [
            {"name": "价格暴跌", "price_change": -10, "volume_mult": 3.0},
            {"name": "价格暴涨", "price_change": 15, "volume_mult": 2.5},
            {"name": "成交量枯竭", "price_change": 0, "volume_mult": 0.1},
            {"name": "成交量爆发", "price_change": 2, "volume_mult": 5.0},
        ]
        
        base_price = 100.0
        
        for scenario in extreme_scenarios:
            print(f"\n--- 测试 {scenario['name']} ---")
            
            # 先提供一些正常数据
            for i in range(20):
                price = base_price + np.random.normal(0, 0.5)
                volume = 1000 + np.random.normal(0, 200)
                signal_generator.update_market_data(price, volume)
            
            # 应用极端条件
            extreme_price = base_price + scenario['price_change']
            extreme_volume = 1000 * scenario['volume_mult']
            
            signal_generator.update_market_data(extreme_price, extreme_volume)
            
            # 生成信号
            signal = signal_generator.generate_trading_signal(extreme_price, risk_tolerance=0.8)
            
            print(f"  极端条件: 价格={extreme_price:.2f}, 成交量={extreme_volume:.0f}")
            print(f"  策略响应: {signal['action']}, 置信度={signal['signal_data']['confidence']:.3f}")
            print(f"  AI信号: {signal['signal_data']['ai_signal']}")
            
            # 验证策略没有崩溃
            assert signal['action'] in ['buy', 'sell', 'hold'], "无效的交易信号"
            assert 0 <= signal['signal_data']['confidence'] <= 1, "置信度超出范围"
        
        print("\n✓ 策略鲁棒性测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 鲁棒性测试失败: {e}")
        return False

def simulate_real_time_trading():
    """模拟实时交易环境"""
    print("\n" + "=" * 60)
    print("实时交易模拟")
    print("=" * 60)
    
    try:
        sys.path.append('.')
        from volume_supertrend_ai_standalone import TradingSignalGenerator
        
        signal_generator = TradingSignalGenerator()
        
        print("开始实时交易模拟 (30秒)...")
        
        start_time = time.time()
        signal_count = 0
        base_price = 100.0
        
        while time.time() - start_time < 30:  # 运行30秒
            # 生成实时数据
            current_time = time.time()
            price_noise = np.random.normal(0, 0.3)
            trend = 0.01 * np.sin((current_time - start_time) / 10)  # 10秒周期的趋势
            
            current_price = base_price + trend + price_noise
            current_volume = 1000 + np.random.normal(0, 300)
            
            # 更新数据
            signal_generator.update_market_data(current_price, max(100, current_volume))
            
            # 每3秒生成一次信号
            if int(current_time) % 3 == 0:
                signal = signal_generator.generate_trading_signal(current_price)
                signal_count += 1
                
                timestamp = datetime.now().strftime("%H:%M:%S")
                print(f"[{timestamp}] 信号 {signal_count:2d}: "
                      f"价格={current_price:6.2f}, "
                      f"动作={signal['action']:4s}, "
                      f"置信度={signal['signal_data']['confidence']:.3f}")
            
            time.sleep(1)  # 每秒更新一次
        
        print(f"\n✓ 实时交易模拟完成，共生成 {signal_count} 个信号")
        return True
        
    except Exception as e:
        print(f"✗ 实时交易模拟失败: {e}")
        return False

if __name__ == "__main__":
    print("Volume SuperTrend AI 策略离线测试套件")
    print("测试时间:", datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
    print("=" * 60)
    
    # 运行所有测试
    test_results = []
    
    # 1. 核心功能测试
    test_results.append(("核心功能测试", test_volume_supertrend_ai_offline()))
    
    # 2. 鲁棒性测试
    test_results.append(("鲁棒性测试", test_strategy_resilience()))
    
    # 3. 实时交易模拟
    test_results.append(("实时交易模拟", simulate_real_time_trading()))
    
    # 总结测试结果
    print("\n" + "=" * 60)
    print("测试结果总结")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{test_name:20s}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n总体结果: {passed_tests}/{len(test_results)} 测试通过")
    
    if passed_tests == len(test_results):
        print("🎉 所有测试通过！Volume SuperTrend AI策略离线功能正常")
    else:
        print("⚠️  部分测试失败，请检查相关功能")
    
    print("\n注意: 这是离线测试，不需要网络连接")
    print("策略的网络连接问题已通过自动重连机制解决")
