"""
调试测试脚本
"""

import numpy as np
import pandas as pd
from datetime import datetime

from strategy_backtester import StrategyBacktester
from original_strategy_adapter import OriginalStrategyAdapter


def generate_debug_data():
    """生成调试数据"""
    dates = pd.date_range(start="2023-01-01", periods=1000, freq="1h")
    n = len(dates)
    
    np.random.seed(42)
    
    # 生成明显的趋势数据
    base_price = 100
    trend = np.linspace(0, 0.2, n)  # 明显上升趋势
    noise = np.random.normal(0, 0.01, n)
    
    prices = base_price * (1 + trend + noise)
    
    data = pd.DataFrame(index=dates)
    data['close'] = prices
    data['open'] = data['close'].shift(1).fillna(data['close'].iloc[0])
    data['high'] = data['close'] * 1.01
    data['low'] = data['close'] * 0.99
    data['volume'] = 1000
    
    return data


def debug_original_strategy():
    """调试原策略"""
    print("调试原策略")
    print("=" * 30)
    
    data = generate_debug_data()
    print(f"数据点: {len(data)}")
    print(f"价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
    
    strategy = OriginalStrategyAdapter()
    
    # 生成信号
    print("\n生成信号...")
    signals = strategy.generate_signals(data)
    
    # 检查信号
    buy_signals = signals[signals['action'] == 'buy']
    sell_signals = signals[signals['action'] == 'sell']

    print(f"买入信号数量: {len(buy_signals)}")
    print(f"卖出信号数量: {len(sell_signals)}")

    if len(buy_signals) > 0:
        print("\n前5个买入信号:")
        print(buy_signals.head())

    if len(sell_signals) > 0:
        print("\n前5个卖出信号:")
        print(sell_signals.head())

    # 检查第一个买入信号后的情况
    if len(buy_signals) > 0:
        first_buy_idx = buy_signals.index[0]
        first_buy_loc = data.index.get_loc(first_buy_idx)

        if isinstance(first_buy_loc, int):
            print(f"\n第一个买入信号位置: {first_buy_loc}")
            print(f"买入价格: {data.iloc[first_buy_loc]['close']:.2f}")

            # 检查后续几个时间点的价格和止盈止损
            for j in range(1, min(10, len(data) - first_buy_loc)):
                idx = first_buy_loc + j
                if idx >= len(data):
                    break

                price = float(data.iloc[idx]['close'])
                atr = float(data.iloc[idx]['atr'])

                # 计算理论止损止盈
                entry_price = float(data.iloc[first_buy_loc]['close'])
                stop_loss = entry_price - atr * 2.2
                take_profit = entry_price + atr * 3.0

                print(f"时间点{j}: 价格{price:.2f}, 止损{stop_loss:.2f}, 止盈{take_profit:.2f}")

                if price <= stop_loss or price >= take_profit:
                    print(f"  -> 应该触发{'止损' if price <= stop_loss else '止盈'}")
                    break
    
    # 检查指标计算
    print(f"\n检查指标计算...")
    print(f"EMA5 范围: {data['ema_5'].min():.2f} - {data['ema_5'].max():.2f}")
    print(f"EMA15 范围: {data['ema_15'].min():.2f} - {data['ema_15'].max():.2f}")
    print(f"EMA30 范围: {data['ema_30'].min():.2f} - {data['ema_30'].max():.2f}")
    print(f"ATR 范围: {data['atr'].min():.2f} - {data['atr'].max():.2f}")
    
    # 检查最后几行的指标值
    print(f"\n最后5行指标值:")
    print(data[['close', 'ema_5', 'ema_15', 'ema_30', 'atr']].tail())
    
    # 运行回测
    print(f"\n运行回测...")
    backtester = StrategyBacktester()
    result = backtester.run_backtest(strategy, data)
    
    print(f"交易次数: {len(result.trades)}")
    print(f"已完成交易: {len([t for t in result.trades if not t.is_open])}")
    
    if result.trades:
        print(f"\n前3笔交易:")
        for i, trade in enumerate(result.trades[:3]):
            print(f"交易{i+1}: 入场{trade.entry_time} 价格{trade.entry_price:.2f} "
                  f"出场{trade.exit_time} 价格{trade.exit_price:.2f} 盈亏{trade.pnl:.2f}")


if __name__ == "__main__":
    debug_original_strategy()
