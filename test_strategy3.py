#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Strategy3 测试脚本
测试高级模糊推理交易策略的基本功能
"""

import sys
import time
from Strategy3 import Strategy3, TickData, KLineData, create_strategy

def test_strategy_basic():
    """测试策略基本功能"""
    print("=== Strategy3 基本功能测试 ===")
    
    # 创建策略实例
    strategy = create_strategy()
    print("✓ 策略创建成功")
    
    # 测试参数配置
    print(f"✓ Hull MA周期: {strategy.params_map.hull_period}")
    print(f"✓ STC参数: {strategy.params_map.stc_fast_period}/{strategy.params_map.stc_slow_period}/{strategy.params_map.stc_cycle_period}")
    print(f"✓ 模糊推理: {'启用' if strategy.params_map.enable_fuzzy else '禁用'}")
    print(f"✓ 控制论: {'启用' if strategy.params_map.enable_control else '禁用'}")
    print(f"✓ 机器学习: {'启用' if strategy.params_map.enable_ml else '禁用'}")
    
    return strategy

def test_strategy_indicators(strategy):
    """测试技术指标计算"""
    print("\n=== 技术指标计算测试 ===")
    
    # 模拟K线数据
    test_klines = [
        KLineData(100, 101, 99, 100.5, 1000),
        KLineData(100.5, 102, 100, 101.2, 1200),
        KLineData(101.2, 103, 101, 102.1, 1100),
        KLineData(102.1, 104, 101.5, 103.0, 1300),
        KLineData(103.0, 105, 102.5, 104.2, 1400),
        KLineData(104.2, 106, 103.8, 105.1, 1250),
        KLineData(105.1, 107, 104.5, 106.3, 1350),
        KLineData(106.3, 108, 105.8, 107.2, 1450),
        KLineData(107.2, 109, 106.5, 108.1, 1550),
        KLineData(108.1, 110, 107.3, 109.0, 1600),
    ]
    
    # 逐个处理K线数据
    for i, kline in enumerate(test_klines):
        try:
            strategy.calc_indicator(kline)
            print(f"K线 {i+1}: Hull MA={strategy.state_map.hull_ma:.4f}, "
                  f"STC={strategy.state_map.stc_value:.2f}, "
                  f"波动率={strategy.state_map.volatility:.4f}")
        except Exception as e:
            print(f"K线 {i+1} 处理错误: {e}")
    
    print("✓ 技术指标计算测试完成")

def test_strategy_signals(strategy):
    """测试信号生成"""
    print("\n=== 信号生成测试 ===")
    
    # 模拟价格上涨趋势
    rising_prices = [100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110]
    
    for i, price in enumerate(rising_prices):
        kline = KLineData(price-0.5, price+0.5, price-1, price, 1000)
        
        try:
            # 计算指标和信号
            strategy.calc_indicator(kline)
            strategy.calc_signal(kline)
            
            print(f"价格 {price}: 主信号={strategy.state_map.final_signal:.4f}, "
                  f"买入={strategy.buy_signal}, 卖出={strategy.sell_signal}")
                  
        except Exception as e:
            print(f"价格 {price} 信号生成错误: {e}")
    
    print("✓ 信号生成测试完成")

def test_strategy_tick_processing(strategy):
    """测试Tick数据处理"""
    print("\n=== Tick数据处理测试 ===")
    
    # 模拟Tick数据
    test_ticks = [
        TickData(100.0, 1000),
        TickData(100.5, 1200),
        TickData(101.0, 1100),
        TickData(101.5, 1300),
        TickData(102.0, 1400),
    ]
    
    for i, tick in enumerate(test_ticks):
        try:
            strategy.on_tick(tick)
            print(f"Tick {i+1}: 价格={tick.last_price}, 成交量={tick.volume}")
        except Exception as e:
            print(f"Tick {i+1} 处理错误: {e}")
    
    print("✓ Tick数据处理测试完成")

def test_strategy_indicators_display(strategy):
    """测试指标显示"""
    print("\n=== 指标显示测试 ===")
    
    try:
        main_indicators = strategy.main_indicator_data
        sub_indicators = strategy.sub_indicator_data
        
        print("主图指标:")
        for key, value in main_indicators.items():
            print(f"  {key}: {value:.4f}")
        
        print("副图指标:")
        for key, value in sub_indicators.items():
            print(f"  {key}: {value:.4f}")
        
        print("✓ 指标显示测试完成")
        
    except Exception as e:
        print(f"指标显示测试错误: {e}")

def main():
    """主测试函数"""
    print("Strategy3 高级模糊推理交易策略测试")
    print("=" * 50)
    
    try:
        # 基本功能测试
        strategy = test_strategy_basic()
        
        # 技术指标测试
        test_strategy_indicators(strategy)
        
        # 信号生成测试
        test_strategy_signals(strategy)
        
        # Tick处理测试
        test_strategy_tick_processing(strategy)
        
        # 指标显示测试
        test_strategy_indicators_display(strategy)
        
        print("\n" + "=" * 50)
        print("✓ 所有测试完成！策略运行正常")
        print("✓ 策略已准备好在无限易Pro中使用")
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
