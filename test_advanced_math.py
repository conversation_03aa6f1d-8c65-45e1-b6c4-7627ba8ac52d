#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试高级数学功能模块
验证Strategy3.py中实现的高级数学模块是否正常工作
"""

import sys
import os
import numpy as np
from typing import Dict, Any

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_group_theory_module():
    """测试群论模块"""
    print("=" * 50)
    print("测试群论数学基础模块")
    print("=" * 50)
    
    try:
        from Strategy3 import LieGroup, PermutationGroup, SymmetryDetector, GroupTheoryDecisionEngine
        
        # 测试李群
        print("1. 测试李群运算...")
        lie_group = LieGroup(3)
        test_matrix = np.array([[1, 0], [0, 1]], dtype=complex)
        invariant = lie_group.compute_invariant(test_matrix)
        print(f"   李群不变量计算: {invariant}")
        
        # 测试置换群
        print("2. 测试置换群...")
        perm_group = PermutationGroup(4)
        test_pattern = np.array([1, 2, 3, 4])
        symmetries = perm_group.find_pattern_symmetry(test_pattern)
        print(f"   找到的对称性数量: {len(symmetries)}")
        
        # 测试对称性检测
        print("3. 测试对称性检测...")
        detector = SymmetryDetector(10)
        for i in range(15):
            detector.update_price_data(100 + np.sin(i * 0.5) * 5)
        
        symmetry_info = detector.detect_multiscale_symmetry()
        print(f"   对称性强度: {symmetry_info['symmetry_strength']:.3f}")
        
        # 测试群论决策引擎
        print("4. 测试群论决策引擎...")
        engine = GroupTheoryDecisionEngine()
        market_data = {'price': 100.0, 'volume': 1000.0, 'volatility': 0.02}
        result = engine.optimize_decision_rules(market_data)
        print(f"   优化信号: {result.get('optimized_signal', 0.0):.3f}")
        print(f"   置信度: {result.get('confidence', 0.0):.3f}")
        
        print("✓ 群论模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 群论模块测试失败: {e}")
        return False

def test_topology_module():
    """测试拓扑空间模糊集模块"""
    print("\n" + "=" * 50)
    print("测试拓扑空间模糊集优化系统")
    print("=" * 50)
    
    try:
        from Strategy3 import MarketTopology, TopologicalFuzzySet, ContinuousMembershipSystem
        
        # 测试市场拓扑
        print("1. 测试市场拓扑结构...")
        topology = MarketTopology(3)
        test_point = np.array([0.5, 0.3, 0.8])
        neighborhood = topology.compute_neighborhood(test_point, 0.1)
        print(f"   邻域函数创建成功")
        
        # 测试拓扑模糊集
        print("2. 测试拓扑模糊集...")
        def test_membership(x):
            return 1.0 / (1.0 + np.exp(-5 * x[0]))
        
        topo_fuzzy_set = TopologicalFuzzySet(test_membership, topology)
        membership = topo_fuzzy_set.membership_degree(test_point)
        print(f"   隶属度: {membership:.3f}")
        print(f"   连续性验证: {topo_fuzzy_set.continuity_verified}")
        
        # 测试连续隶属度系统
        print("3. 测试连续隶属度系统...")
        cms = ContinuousMembershipSystem()
        market_data = {'trend': 0.1, 'volatility': 0.02, 'volume_ratio': 1.2}
        result = cms.process_market_data(market_data)
        print(f"   拓扑信号: {result.get('topological_signal', 0.0):.3f}")
        
        print("✓ 拓扑模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 拓扑模块测试失败: {e}")
        return False

def test_probability_module():
    """测试概率测度论模块"""
    print("\n" + "=" * 50)
    print("测试概率测度论模块")
    print("=" * 50)
    
    try:
        from Strategy3 import ProbabilitySpace, FuzzyProbabilityCalculator, MartingaleAnalyzer
        
        # 测试概率空间
        print("1. 测试概率空间...")
        sample_space = set(range(10))
        prob_space = ProbabilitySpace(sample_space)
        
        # 定义随机变量
        def price_rv(outcome):
            return 100 + outcome * 0.5
        
        prob_space.define_random_variable('price', price_rv)
        expectation = prob_space.compute_expectation('price')
        print(f"   价格期望: {expectation:.2f}")
        
        # 测试模糊概率计算
        print("2. 测试模糊概率计算...")
        fuzzy_calc = FuzzyProbabilityCalculator(prob_space)
        
        def high_price_membership(outcome):
            price = price_rv(outcome)
            return 1.0 / (1.0 + np.exp(-0.1 * (price - 102)))
        
        fuzzy_calc.define_fuzzy_event('high_price', high_price_membership)
        fuzzy_prob = fuzzy_calc.compute_fuzzy_probability('high_price')
        print(f"   高价格模糊概率: {fuzzy_prob:.3f}")
        
        # 测试鞅分析
        print("3. 测试鞅理论分析...")
        martingale = MartingaleAnalyzer(prob_space)
        
        # 添加价格序列
        for i in range(25):
            price = 100 + np.random.normal(0, 1) * 0.5
            martingale.update_price(price, i)
        
        martingale_test = martingale.is_martingale(20)
        print(f"   是否为鞅: {martingale_test['is_martingale']}")
        print(f"   置信度: {martingale_test['confidence']:.3f}")
        
        print("✓ 概率测度论模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 概率测度论模块测试失败: {e}")
        return False

def test_functional_analysis_module():
    """测试泛函分析模块"""
    print("\n" + "=" * 50)
    print("测试泛函分析优化框架")
    print("=" * 50)
    
    try:
        from Strategy3 import HilbertSpace, FunctionSpaceMapper, FixedPointSolver, ContractionMapping
        
        # 测试Hilbert空间
        print("1. 测试Hilbert空间...")
        hilbert = HilbertSpace(3)
        v1 = np.array([1, 0, 0])
        v2 = np.array([0, 1, 0])
        
        inner_prod = hilbert.inner_product(v1, v2)
        angle = hilbert.compute_angle(v1, v2)
        print(f"   内积: {inner_prod:.3f}")
        print(f"   夹角: {angle:.3f} 弧度")
        
        # 测试函数空间映射
        print("2. 测试函数空间映射...")
        mapper = FunctionSpaceMapper(5, 5)
        time_series = [100, 101, 99, 102, 98]
        
        function_repr = mapper.map_time_series_to_function_space(time_series)
        fourier_coeffs = mapper.fourier_basis_projection(time_series)
        print(f"   函数空间表示维度: {len(function_repr)}")
        print(f"   傅里叶系数维度: {len(fourier_coeffs)}")
        
        # 测试不动点求解
        print("3. 测试不动点求解...")
        def contraction_func(x):
            return 0.5 * x + 0.1
        
        contraction = ContractionMapping(contraction_func, 0.5)
        contraction.set_metric_space(hilbert)
        
        solver = FixedPointSolver(hilbert)
        initial_point = np.array([1.0, 0.0, 0.0])
        result = solver.picard_iteration(contraction, initial_point)
        
        print(f"   收敛: {result['converged']}")
        if result['converged']:
            print(f"   迭代次数: {result['iterations']}")
        
        print("✓ 泛函分析模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 泛函分析模块测试失败: {e}")
        return False

def test_information_theory_module():
    """测试信息论模块"""
    print("\n" + "=" * 50)
    print("测试信息论优化模块")
    print("=" * 50)
    
    try:
        from Strategy3 import EntropyCalculator, MutualInformationCalculator
        
        # 测试熵计算
        print("1. 测试熵计算系统...")
        entropy_calc = EntropyCalculator(30)
        
        # 添加测试数据
        for i in range(35):
            value = 100 + np.sin(i * 0.3) * 5 + np.random.normal(0, 1)
            entropy_calc.update_data(value)
        
        shannon_entropy = entropy_calc.shannon_entropy()
        renyi_entropy = entropy_calc.renyi_entropy(2.0)
        
        print(f"   Shannon熵: {shannon_entropy:.3f}")
        print(f"   Rényi熵: {renyi_entropy:.3f}")
        
        # 测试市场不确定性量化
        uncertainty = entropy_calc.market_uncertainty_quantification()
        print(f"   不确定性水平: {uncertainty['uncertainty_level']}")
        print(f"   不确定性分数: {uncertainty['uncertainty_score']:.3f}")
        
        # 测试互信息计算
        print("2. 测试互信息计算...")
        mi_calc = MutualInformationCalculator(30)
        
        # 添加特征和目标数据
        for i in range(35):
            price = 100 + np.sin(i * 0.3) * 5
            volume = 1000 + np.cos(i * 0.2) * 200
            mi_calc.add_feature('volume', volume)
            mi_calc.update_target(price)
        
        mutual_info = mi_calc.mutual_information('volume')
        print(f"   价格-成交量互信息: {mutual_info:.3f}")
        
        # 测试特征选择
        feature_ranking = mi_calc.feature_selection_by_mutual_information(3)
        print(f"   特征排名: {feature_ranking}")
        
        print("✓ 信息论模块测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 信息论模块测试失败: {e}")
        return False

def test_intelligent_collaboration():
    """测试智能协作架构"""
    print("\n" + "=" * 50)
    print("测试智能协作架构")
    print("=" * 50)
    
    try:
        from Strategy3 import MarketStatePerceiver, IntelligentScheduler, DecisionFusionEngine
        
        # 测试市场状态感知器
        print("1. 测试市场状态感知器...")
        perceiver = MarketStatePerceiver()
        
        # 更新市场数据
        for i in range(20):
            price = 100 + np.sin(i * 0.2) * 3
            volume = 1000 + np.random.normal(0, 100)
            volatility = 0.02 + abs(np.sin(i * 0.1)) * 0.02
            perceiver.update_market_data(price, volume, volatility)
        
        state_info = perceiver.detect_market_state()
        print(f"   当前市场状态: {state_info['current_state']}")
        print(f"   状态置信度: {state_info['confidence']:.3f}")
        
        # 测试智能调度器
        print("2. 测试智能调度器...")
        scheduler = IntelligentScheduler()
        
        # 注册模拟模块
        scheduler.register_module('test_module1', None, 1.0)
        scheduler.register_module('test_module2', None, 0.8)
        
        market_data = {'price': 100.0, 'volume': 1000.0, 'volatility': 0.02}
        scheduling_result = scheduler.schedule_modules(market_data)
        print(f"   调度效率: {scheduling_result.get('scheduling_efficiency', 0.0):.3f}")
        
        # 测试决策融合引擎
        print("3. 测试决策融合引擎...")
        fusion_engine = DecisionFusionEngine()
        fusion_engine.register_modules(['module1', 'module2', 'module3'])
        
        # 模拟模块结果
        module_results = {
            'module1': {'signal': 0.1, 'confidence': 0.8},
            'module2': {'signal': 0.05, 'confidence': 0.7},
            'module3': {'signal': -0.02, 'confidence': 0.6}
        }
        
        fusion_result = fusion_engine.fuse_module_decisions(module_results)
        print(f"   融合信号: {fusion_result['fused_signal']:.3f}")
        print(f"   融合置信度: {fusion_result['fused_confidence']:.3f}")
        print(f"   融合策略: {fusion_result['fusion_strategy']}")
        
        print("✓ 智能协作架构测试通过")
        return True
        
    except Exception as e:
        print(f"✗ 智能协作架构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试Strategy3.py中的高级数学功能模块")
    print("=" * 80)
    
    test_results = []
    
    # 运行各模块测试
    test_results.append(test_group_theory_module())
    test_results.append(test_topology_module())
    test_results.append(test_probability_module())
    test_results.append(test_functional_analysis_module())
    test_results.append(test_information_theory_module())
    test_results.append(test_intelligent_collaboration())
    
    # 总结测试结果
    print("\n" + "=" * 80)
    print("测试总结")
    print("=" * 80)
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"总测试数: {total}")
    print(f"通过测试: {passed}")
    print(f"失败测试: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有高级数学模块测试通过！")
        print("Strategy3.py中的高级数学功能已成功实现并可正常工作。")
    else:
        print(f"\n⚠️  有 {total - passed} 个模块测试失败，请检查相关实现。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
